/* Custom admin styles for product edit view */
#optima_product_data {
  .options_group {
    padding-inline: 1rem;
  }
}

/* Customer table styles */
.optima-customers-section {
  margin-top: 20px;
}

.optima-customer-actions {
  margin: 20px 0;
  display: flex;
  gap: 10px;
}

.optima-customers-section table.customers {
  margin-top: 15px;
  width: 100%;
  border-collapse: collapse;
}

.optima-customers-section table.customers th,
.optima-customers-section table.customers td {
  padding: 8px;
  text-align: left;
}

#wc-optima-customers-loading {
  margin: 20px 0;
}

#wc-optima-customers-results {
  margin-top: 20px;
}

/* RO Documents table styles */
.optima-rco-section {
  margin-top: 20px;
}

.optima-rco-actions {
  margin: 20px 0;
  display: flex;
  gap: 10px;
}

.optima-rco-section table.ro-documents {
  margin-top: 15px;
  width: 100%;
  border-collapse: collapse;
  table-layout: fixed;
}

.optima-rco-section table.ro-documents th,
.optima-rco-section table.ro-documents td {
  padding: 8px;
  text-align: left;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Style for action buttons in document list */
.optima-rco-section table.ro-documents td .button {
  margin-right: 5px;
  margin-bottom: 5px;
}

.optima-rco-section table.ro-documents td .download-document-button {
  background-color: #f0f0f0;
  border-color: #ccc;
}

.optima-rco-section table.ro-documents td .download-document-button:hover {
  background-color: #e0e0e0;
  border-color: #999;
}

/* Make the table horizontally scrollable */
.optima-rco-section #wc-optima-ro-documents-results {
  overflow-x: auto;
}

#wc-optima-ro-documents-loading {
  margin: 20px 0;
}

#wc-optima-ro-documents-results {
  margin-top: 20px;
}

/* Add to existing CSS */
.optima-search-document {
  margin-bottom: 20px;
  background: #f9f9f9;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.optima-search-document h3 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 16px;
}

.search-description {
  margin-bottom: 15px;
  color: #666;
}

.search-fields-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.optima-search-document input,
.optima-search-document select {
  width: 100%;
  padding: 6px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.optima-search-document label {
  font-weight: 600;
  display: block;
  margin-bottom: 5px;
}

.search-field-container {
  display: flex;
  flex-direction: column;
}

.search-actions {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

/* Invoices styles */
.optima-invoices-section {
  margin-top: 20px;
}

.optima-search-invoice {
  margin-bottom: 20px;
  padding: 15px;
  background: #f9f9f9;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.optima-search-invoice h3 {
  margin-top: 0;
  margin-bottom: 15px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.search-field {
  flex: 1 0 200px;
}

.search-field label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.search-field input {
  width: 100%;
  padding: 6px;
}

.search-actions {
  flex-basis: 100%;
  margin-top: 10px;
  display: flex;
  gap: 10px;
}

.optima-invoices-section table.invoices {
  margin-top: 15px;
  width: 100%;
  border-collapse: collapse;
  table-layout: fixed;
}

.optima-invoices-section table.invoices th,
.optima-invoices-section table.invoices td {
  padding: 8px;
  text-align: left;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Make the table horizontally scrollable */
.optima-invoices-section #wc-optima-invoices-results {
  overflow-x: auto;
}

#wc-optima-invoices-loading {
  margin: 20px 0;
}

#wc-optima-invoices-results {
  margin-top: 20px;
}

/* Styles for RO Details Modal */
.ro-modal {
  display: none; /* Hidden by default */
  position: fixed; /* Stay in place */
  z-index: 1000; /* Sit on top */
  left: 0;
  top: 0;
  width: 100%; /* Full width */
  height: 100%; /* Full height */
  overflow: auto; /* Enable scroll if needed */
  background-color: rgba(0, 0, 0, 0.6); /* Black w/ opacity */
}

.ro-modal-content {
  background-color: #fefefe;
  margin: 10% auto; /* 10% from the top and centered */
  padding: 20px;
  border: 1px solid #888;
  width: 80%; /* Could be more or less, depending on screen size */
  max-width: 700px;
  position: relative;
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
}

.ro-modal-close {
  color: #aaa;
  position: absolute;
  top: 10px;
  right: 25px;
  font-size: 28px;
  font-weight: bold;
}

.ro-modal-close:hover,
.ro-modal-close:focus {
  color: black;
  text-decoration: none;
  cursor: pointer;
}

#ro-details-modal-content dl {
  margin-bottom: 1em;
}

#ro-details-modal-content dt {
  font-weight: bold;
  margin-top: 0.5em;
}

#ro-details-modal-content dd {
  margin-left: 1.5em;
  margin-bottom: 0.5em;
}

#ro-details-modal-content ul {
  list-style: disc;
  margin-left: 2em;
}

#ro-details-modal-content li {
  margin-bottom: 0.5em;
}

/* Styles for sync charts */
.optima-sync-charts-container {
  display: flex;
  flex-wrap: wrap;
  gap: 30px;
  margin: 20px 0 30px;
}

.optima-sync-chart-wrapper {
  flex: 1 0 400px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  padding: 20px;
  max-width: 600px;
}

.optima-sync-chart-wrapper h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 16px;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.optima-sync-chart-data {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 20px;
}

.optima-sync-data-item {
  flex: 1 0 45%;
  display: flex;
  flex-direction: column;
}

.optima-sync-data-label {
  font-size: 13px;
  color: #666;
  margin-bottom: 5px;
}

.optima-sync-data-value {
  font-size: 18px;
  font-weight: 600;
}

.optima-sync-chart {
  position: relative;
  height: 300px;
  width: 100%;
}

@media (max-width: 782px) {
  .optima-sync-charts-container {
    flex-direction: column;
  }

  .optima-sync-chart-wrapper {
    max-width: 100%;
  }
}

/* Styles for sync progress bar */
.optima-sync-progress-container {
  margin: 20px 0;
  background: #f9f9f9;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.optima-sync-progress-text {
  margin-bottom: 10px;
  font-weight: 600;
}

.optima-sync-progress-bar {
  height: 20px;
  background-color: #e0e0e0;
  border-radius: 10px;
  overflow: hidden;
  margin-bottom: 10px;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
}

.optima-sync-progress-bar-inner {
  height: 100%;
  background: linear-gradient(90deg, #4caf50, #8bc34a);
  border-radius: 10px;
  width: 0;
  transition: width 0.5s ease-in-out;
}

.optima-sync-status {
  font-size: 14px;
  color: #666;
  margin-top: 5px;
}

/* Styles for imports container */
.optima-imports-container {
  display: flex;
  gap: 20px;
  margin: 20px 0;
}

.optima-imports-list {
  flex: 0 0 300px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  padding: 20px;
  max-height: 600px;
  overflow-y: auto;
}

.optima-import-details {
  flex: 1;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  padding: 20px;
  min-height: 400px;
}

.optima-imports-list h3,
.optima-import-details h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 16px;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.optima-imports-list-items {
  list-style: none;
  margin: 0;
  padding: 0;
}

.optima-import-item {
  padding: 10px;
  border: 1px solid #eee;
  border-radius: 4px;
  margin-bottom: 10px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.optima-import-item:hover {
  background-color: #f9f9f9;
  border-color: #ddd;
}

.optima-import-item.active {
  background-color: #f0f7ff;
  border-color: #007cba;
  border-left: 4px solid #007cba;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.optima-import-item-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
}

.optima-import-date {
  font-weight: 600;
  color: #333;
}

.optima-import-type {
  font-size: 12px;
  background: #eee;
  padding: 2px 6px;
  border-radius: 3px;
  color: #666;
}

.optima-import-item-summary {
  font-size: 13px;
  color: #666;
}

.optima-no-import-selected {
  color: #999;
  font-style: italic;
  text-align: center;
  margin-top: 50px;
}

.optima-import-details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.optima-import-details-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.optima-import-details-date {
  font-size: 14px;
  color: #666;
}

.optima-import-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.optima-import-stat-item {
  background: #f9f9f9;
  border-radius: 4px;
  padding: 15px;
  text-align: center;
}

/* Style for clickable stat items with detailed reports */
.optima-import-stat-item[data-has-details="true"] {
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

.optima-import-stat-item[data-has-details="true"]:hover {
  background-color: #f0f7ff;
  border-color: #007cba;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.optima-import-stat-value {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 5px;
  display: block;
}

.optima-import-stat-label {
  font-size: 13px;
  color: #666;
}

.optima-import-chart-container {
  margin-top: 30px;
  height: 300px;
}

@media (max-width: 782px) {
  .optima-imports-container {
    flex-direction: column;
  }

  .optima-imports-list {
    flex: 0 0 auto;
    max-height: 300px;
  }
}

/* Manual sync container styles */
.optima-manual-sync-container {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin: 20px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.optima-manual-sync-container form {
  margin: 0;
}

.sync-info h3 {
  margin-top: 0;
  margin-bottom: 8px;
  font-size: 16px;
  color: #333;
}

.sync-info p {
  margin: 0;
  color: #666;
}

/* Progress bar styles */
.optima-sync-progress-container {
  display: none;
  margin-top: 15px;
  width: 50%;
}

.optima-sync-progress-bar {
  height: 20px;
  background-color: #f0f0f0;
  border-radius: 10px;
  overflow: hidden;
  position: relative;
  margin-bottom: 10px;
}

.optima-sync-progress-bar-inner {
  height: 100%;
  background-color: #2271b1;
  width: 0%;
  transition: width 0.3s ease;
  border-radius: 10px;
}

.optima-sync-progress-text {
  text-align: center;
  font-size: 13px;
  color: #666;
  margin-bottom: 5px;
}

.optima-sync-status {
  margin-top: 10px;
  font-style: italic;
  color: #666;
}

/* Logs and diagnostics styles */
.optima-logs-container,
.optima-diagnostics-container {
  background-color: #f8f8f8;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 10px;
  margin-top: 10px;
}

.optima-logs-content {
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: monospace;
  font-size: 12px;
  line-height: 1.5;
  margin: 0;
}

.optima-diagnostics-container table {
  width: 100%;
  border-collapse: collapse;
}

.optima-diagnostics-container th,
.optima-diagnostics-container td {
  padding: 8px;
  text-align: left;
  border-bottom: 1px solid #ddd;
}

.optima-diagnostics-container th {
  background-color: #f2f2f2;
}

/* Improved logs display */
.optima-logs-list {
  font-family: monospace;
  font-size: 12px;
  line-height: 1.5;
}

.optima-log-item {
  margin-bottom: 5px;
  border: 1px solid #ddd;
  border-radius: 3px;
}

.optima-log-header {
  padding: 8px;
  background-color: #f2f2f2;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.optima-log-timestamp {
  color: #666;
  margin-right: 10px;
  white-space: nowrap;
}

.optima-log-type {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 3px;
  margin-right: 10px;
  font-weight: bold;
  color: white;
  min-width: 60px;
  text-align: center;
}

.optima-log-type-api {
  background-color: #3498db;
}

.optima-log-type-error {
  background-color: #e74c3c;
}

.optima-log-type-info {
  background-color: #2ecc71;
}

.optima-log-type-php_error {
  background-color: #f39c12;
}

.optima-log-message {
  flex-grow: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.optima-log-toggle {
  margin-left: 10px;
  font-weight: bold;
  font-size: 16px;
}

.optima-log-content {
  display: none;
  padding: 10px;
  background-color: #fff;
  border-top: 1px solid #ddd;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.optima-log-content pre {
  margin: 0;
  white-space: pre-wrap;
}

/* Advanced section styles */
.optima-advanced-section {
  margin-top: 20px;
  margin-bottom: 20px;
}

.optima-advanced-section-header {
  margin-bottom: 25px;
}

.optima-advanced-section-header h2 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 20px;
  color: #23282d;
}

.optima-advanced-section-header p {
  margin-top: 0;
  color: #666;
  font-size: 14px;
}

.optima-advanced-category {
  margin-bottom: 30px;
}

.optima-advanced-category-title {
  background-color: #f0f0f0;
  padding: 12px 15px;
  border-left: 4px solid #2271b1;
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: 600;
  color: #23282d;
}

.optima-advanced-category-description {
  margin-bottom: 15px;
  color: #666;
  padding: 0 5px;
}

.optima-advanced-cards {
  display: grid;
  grid-template-columns: 1fr;
  gap: 20px;
}

.optima-advanced-card {
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 20px;
  margin: 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.optima-advanced-card h3 {
  margin-top: 0;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
  color: #23282d;
  font-size: 15px;
}

.optima-advanced-card p {
  margin-bottom: 15px;
  color: #666;
}

.optima-advanced-card .description {
  font-size: 13px;
  color: #666;
  margin-top: 8px;
}

/* Danger button styling */
.button-danger {
  background-color: #dc3545 !important;
  color: white !important;
  border-color: #dc3545 !important;
}

.button-danger:hover {
  background-color: #bd2130 !important;
  border-color: #bd2130 !important;
}

/* Order integration data styles */
.optima-integration-data {
  margin-top: 20px;
  padding: 10px;
  background-color: #f8f8f8;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.optima-integration-data h3 {
  margin-top: 0 !important;
  margin-bottom: 10px !important;
  font-size: 14px !important;
}

.optima-data-fields p {
  margin: 5px 0;
}

.optima-data-fields strong {
  display: inline-block;
  min-width: 180px;
}

.optima-data-fields a {
  color: #0073aa;
  text-decoration: none;
  cursor: pointer;
}

.optima-data-fields a:hover {
  color: #00a0d2;
  text-decoration: underline;
}

