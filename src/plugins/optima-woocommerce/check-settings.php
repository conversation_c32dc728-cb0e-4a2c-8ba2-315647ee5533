<?php
/**
 * Check Optima WooCommerce settings
 * 
 * This script checks the current settings for the Optima WooCommerce plugin
 */

// Load WordPress
require_once dirname(__FILE__) . '/../../../wp-load.php';

// Check if the plugin is active
if (!function_exists('is_plugin_active')) {
    include_once(ABSPATH . 'wp-admin/includes/plugin.php');
}

if (!is_plugin_active('optima-woocommerce/optima-woocommerce.php')) {
    echo "Optima WooCommerce plugin is not active.\n";
    exit;
}

// Get the current settings
$settings = get_option('wc_optima_settings', []);

echo "Current Optima WooCommerce settings:\n";
echo "------------------------------------\n";

foreach ($settings as $key => $value) {
    if ($key === 'password') {
        $value = '********';
    }
    echo "$key: $value\n";
}

echo "\n";

// Check if detailed reports are enabled
$detailed_reports = isset($settings['detailed_import_reports']) && $settings['detailed_import_reports'] === 'yes';
echo "Detailed reports enabled: " . ($detailed_reports ? 'yes' : 'no') . "\n";

// Check the most recent import
echo "\nMost recent import:\n";
echo "-------------------\n";

if (class_exists('WC_Optima_Import_Stats')) {
    $imports = WC_Optima_Import_Stats::get_stats();
    
    if (!empty($imports)) {
        // Sort by timestamp (newest first)
        usort($imports, function($a, $b) {
            return $b['timestamp'] - $a['timestamp'];
        });
        
        $latest_import = $imports[0];
        
        echo "Import type: " . $latest_import['import_type'] . "\n";
        echo "Date: " . $latest_import['date'] . "\n";
        echo "Timestamp: " . $latest_import['timestamp'] . "\n";
        
        if ($latest_import['import_type'] === 'products') {
            echo "Added: " . (isset($latest_import['added']) ? $latest_import['added'] : 0) . "\n";
            echo "Updated: " . (isset($latest_import['updated']) ? $latest_import['updated'] : 0) . "\n";
            echo "Skipped: " . (isset($latest_import['skipped']) ? $latest_import['skipped'] : 0) . "\n";
            
            // Check if detailed reports are available
            $has_detailed = 
                (isset($latest_import['detailed_skipped']) && !empty($latest_import['detailed_skipped'])) ||
                (isset($latest_import['detailed_updated']) && !empty($latest_import['detailed_updated'])) ||
                (isset($latest_import['detailed_added']) && !empty($latest_import['detailed_added']));
            
            echo "Has detailed reports: " . ($has_detailed ? 'yes' : 'no') . "\n";
            
            if ($has_detailed) {
                echo "Detailed skipped count: " . (isset($latest_import['detailed_skipped']) ? count($latest_import['detailed_skipped']) : 0) . "\n";
                echo "Detailed updated count: " . (isset($latest_import['detailed_updated']) ? count($latest_import['detailed_updated']) : 0) . "\n";
                echo "Detailed added count: " . (isset($latest_import['detailed_added']) ? count($latest_import['detailed_added']) : 0) . "\n";
            }
        }
    } else {
        echo "No imports found.\n";
    }
} else {
    echo "WC_Optima_Import_Stats class not found.\n";
}
