jQuery(document).ready(function ($) {
  // The manual sync form now uses standard form submission
  // No JavaScript handler needed for the form submission itself

  // Function to poll for progress updates
  // Make it globally accessible so it can be called from inline script
  window.startProgressPolling = function () {
    console.log("Starting progress polling...");
    var pollInterval;
    var consecutiveErrors = 0;
    var maxConsecutiveErrors = 5;
    var pollingDelay = 1000; // Start with 1 second for more frequent updates
    var maxPollingDelay = 5000; // Max 5 seconds
    var startTime = new Date().getTime();
    var timeoutMinutes = 30; // Maximum time to poll before giving up (30 minutes)

    // Function to check if we've been polling too long
    function isPollingTimeout() {
      var currentTime = new Date().getTime();
      var elapsedMinutes = (currentTime - startTime) / (1000 * 60);
      return elapsedMinutes > timeoutMinutes;
    }

    // Function to perform the actual polling
    function pollProgress() {
      $.ajax({
        url: wc_optima_params.ajax_url,
        type: "POST",
        data: {
          action: "wc_optima_get_sync_progress",
        },
        success: function (response) {
          // Reset error counter on success
          consecutiveErrors = 0;

          if (response.success) {
            // Update progress bar
            var progress = response.data.progress;
            var status = response.data.status;
            var inProgress = response.data.in_progress;

            // Update the progress bar width with smooth transition
            $("#optima-sync-progress-bar-inner").css({
              width: progress + "%",
              transition: "width 0.5s ease-in-out",
            });

            // Update the progress text
            $("#optima-sync-progress-percent").text(progress + "%");

            // Update status message
            $("#optima-sync-status").text(status);

            // Hide the form when progress container is visible
            $("#optima-manual-sync-form").hide();

            // If sync is complete, stop polling and re-enable the button
            if (!inProgress && progress >= 100) {
              clearInterval(pollInterval);
              $("#optima-manual-sync-button").prop("disabled", false);

              // Show the form again
              $("#optima-manual-sync-form").show();

              // Reload the page after 3 seconds to show updated stats
              setTimeout(function () {
                location.reload();
              }, 3000);
              return;
            }

            // If we've been stuck at "Rozpoczynanie synchronizacji..." for too long, try to restart
            if (status.includes("Rozpoczynanie synchronizacji") && progress === 0) {
              var currentTime = new Date().getTime();
              var elapsedSeconds = (currentTime - startTime) / 1000;

              // If stuck for more than 30 seconds at the beginning, show a message
              if (elapsedSeconds > 30) {
                $("#optima-sync-status").text(
                  "Synchronizacja trwa dłużej niż zwykle. Proszę czekać...",
                );
              }

              // If stuck for more than 2 minutes, suggest a refresh
              if (elapsedSeconds > 120) {
                clearInterval(pollInterval);
                $("#optima-sync-status").html(
                  "Synchronizacja nie rozpoczęła się poprawnie. <a href='javascript:location.reload()'>Odśwież stronę</a> i spróbuj ponownie.",
                );
                $("#optima-manual-sync-button").prop("disabled", false);
                $("#optima-manual-sync-form").show();
                return;
              }
            }

            // Check for timeout
            if (isPollingTimeout()) {
              clearInterval(pollInterval);
              $("#optima-sync-status").html(
                "Przekroczono maksymalny czas synchronizacji. <a href='javascript:location.reload()'>Odśwież stronę</a> aby sprawdzić status.",
              );
              $("#optima-manual-sync-button").prop("disabled", false);
              $("#optima-manual-sync-form").show();
              return;
            }
          }
        },
        error: function () {
          // Increment error counter
          consecutiveErrors++;

          // If we've had too many consecutive errors, stop polling
          if (consecutiveErrors >= maxConsecutiveErrors) {
            clearInterval(pollInterval);
            $("#optima-sync-status").html(
              "Wystąpił błąd podczas pobierania postępu synchronizacji. <a href='javascript:location.reload()'>Odśwież stronę</a> aby sprawdzić status.",
            );
            $("#optima-manual-sync-button").prop("disabled", false);
            $("#optima-manual-sync-form").show();
            return;
          }

          // Increase polling delay on errors (up to max)
          pollingDelay = Math.min(pollingDelay * 1.5, maxPollingDelay);

          // Check for timeout
          if (isPollingTimeout()) {
            clearInterval(pollInterval);
            $("#optima-sync-status").html(
              "Przekroczono maksymalny czas synchronizacji. <a href='javascript:location.reload()'>Odśwież stronę</a> aby sprawdzić status.",
            );
            $("#optima-manual-sync-button").prop("disabled", false);
            $("#optima-manual-sync-form").show();
            return;
          }
        },
        complete: function () {
          // If we're not using setInterval (for variable timing), schedule the next poll
          if (!pollInterval) {
            setTimeout(pollProgress, pollingDelay);
          }
        },
      });
    }

    // Start polling immediately
    pollProgress();

    // Then continue at regular intervals
    pollInterval = setInterval(pollProgress, pollingDelay);
  };
  // Helper function to format detail keys for display
  function formatDetailKey(key) {
    if (!key) return "";

    // Replace underscores with spaces
    let formatted = key.replace(/_/g, " ");

    // Capitalize first letter
    formatted = formatted.charAt(0).toUpperCase() + formatted.slice(1);

    // Special cases
    const specialCases = {
      id: "ID",
      ro: "RO",
      vat: "VAT",
      nip: "NIP",
      gus: "GUS",
      api: "API",
      url: "URL",
      wc: "WC",
      sku: "SKU",
      pdf: "PDF",
      html: "HTML",
    };

    // Replace special cases
    Object.keys(specialCases).forEach(function (caseKey) {
      const regex = new RegExp("\\b" + caseKey + "\\b", "gi");
      formatted = formatted.replace(regex, specialCases[caseKey]);
    });

    return formatted;
  }
  // Handle log item click to show/hide details
  $(document).on("click", ".optima-log-header", function () {
    var logId = $(this).data("log-id");
    var contentDiv = $("#optima-log-content-" + logId);
    var toggleSpan = $(this).find(".optima-log-toggle");

    if (contentDiv.is(":visible")) {
      contentDiv.slideUp(200);
      toggleSpan.text("+");
    } else {
      contentDiv.slideDown(200);
      toggleSpan.text("-");
    }
  });

  // Sync charts have been removed

  // Initialize import details functionality
  initImportDetails();

  // Function to handle import item click and load details
  function initImportDetails() {
    // Import item click handler
    $(document).on("click", ".optima-import-item", function () {
      loadImportDetails($(this));
    });

    // Auto-select first import item on page load
    setTimeout(function () {
      const firstImportItem = $(".optima-imports-list-items .optima-import-item").first();
      if (firstImportItem.length) {
        firstImportItem.addClass("active");
        loadImportDetails(firstImportItem);
      }
    }, 100); // Small delay to ensure DOM is ready
  }

  // Function to load import details for a given item
  function loadImportDetails(itemElement) {
    const timestamp = itemElement.data("timestamp");

    // Add active class to clicked item and remove from others
    $(".optima-import-item").removeClass("active");
    itemElement.addClass("active");

    // Show loading state
    $("#optima-import-details-content").html(
      '<p class="loading"><span class="spinner is-active" style="float: none;"></span> ' +
        wc_optima_params.loading_import_details +
        "</p>",
    );

    // Get import details via AJAX
    $.ajax({
      url: wc_optima_params.ajax_url,
      type: "POST",
      data: {
        action: "wc_optima_get_import_details",
        nonce: wc_optima_params.get_import_details_nonce,
        timestamp: timestamp,
      },
      success: function (response) {
        if (response.success) {
          // Debug log
          console.log("Import details received:", response.data);
          console.log("Has detailed reports:", response.data.has_detailed_reports);

          // Display the basic import details
          displayImportDetails(response.data);

          // if detailed reports are available
        } else {
          $("#optima-import-details-content").html('<p class="error">' + response.data + "</p>");
        }
      },
      error: function () {
        $("#optima-import-details-content").html(
          '<p class="error">' + wc_optima_params.ajax_error + "</p>",
        );
      },
    });
  }

  // Function to display import details
  function displayImportDetails(importData) {
    // Create HTML for import details
    let html = '<div class="optima-import-details-header">';
    html += '<div class="optima-import-details-title">' + importData.type_label + "</div>";
    html += '<div class="optima-import-details-date">' + importData.date + "</div>";
    html += "</div>";

    // Create stats grid
    html += '<div class="optima-import-stats-grid">';

    // Check if stats property exists and is an array before iterating
    if (importData.stats && Array.isArray(importData.stats)) {
      importData.stats.forEach(function (stat) {
        // Extract action type from the label (added, updated, skipped)
        var actionType = "";
        if (stat.label.includes("Dodane")) {
          actionType = "added";
        } else if (stat.label.includes("Zaktualizowane")) {
          actionType = "updated";
        } else if (stat.label.includes("Pominięte")) {
          actionType = "skipped";
        }

        // Add data attributes for action type and detailed reports availability
        html +=
          '<div class="optima-import-stat-item" data-action="' +
          actionType +
          '" data-has-details="' +
          (importData.has_detailed_reports ? "true" : "false") +
          '">';
        html += '<span class="optima-import-stat-value">' + stat.value + "</span>";
        html += '<span class="optima-import-stat-label">' + stat.label + "</span>";
        html += "</div>";
      });
    } else {
      // If no stats are available, display a message
      html += '<div class="optima-import-stat-item">';
      html += '<span class="optima-import-stat-value">-</span>';
      html += '<span class="optima-import-stat-label">Brak statystyk</span>';
      html += "</div>";
    }

    html += "</div>";

    // Add chart container
    html += '<div class="optima-import-chart-container">';
    html += '<canvas id="importDetailsChart" width="400" height="300"></canvas>';
    html += "</div>";

    // Update content
    $("#optima-import-details-content").html(html);

    // Initialize chart if chart data exists
    const chartElement = document.getElementById("importDetailsChart");

    if (
      chartElement &&
      importData.charts &&
      importData.charts.labels &&
      importData.charts.data &&
      importData.charts.colors
    ) {
      const ctx = chartElement.getContext("2d");

      new Chart(ctx, {
        type: "doughnut",
        data: {
          labels: importData.charts.labels,
          datasets: [
            {
              data: importData.charts.data,
              backgroundColor: importData.charts.colors,
              borderColor: importData.charts.colors.map((color) =>
                typeof color === "string" ? color.replace("0.7", "1") : color,
              ),
              borderWidth: 1,
            },
          ],
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: "right",
              labels: {
                boxWidth: 15,
                padding: 15,
              },
            },
            tooltip: {
              callbacks: {
                label: function (context) {
                  const label = context.label || "";
                  const value = context.raw || 0;
                  return `${label}: ${value}`;
                },
              },
            },
          },
          cutout: "60%",
        },
      });
    } else {
      // If chart data is missing, display a message
      $("#importDetailsChart")
        .parent()
        .html('<p class="no-chart-data">Brak danych do wyświetlenia wykresu</p>');
    }
  }

  // Sync charts function has been removed

  // Handle the import categories button click
  $("#wc-optima-import-categories").on("click", function (e) {
    e.preventDefault();

    // Show loading indicator
    $("#wc-optima-categories-loading").show();
    $("#wc-optima-categories-results").empty();

    // Create nonce for category import
    var importCategoriesNonce = wc_optima_params.import_categories_nonce || "";

    // Make AJAX request
    $.ajax({
      url: wc_optima_params.ajax_url,
      type: "POST",
      data: {
        action: "wc_optima_import_categories",
        nonce: importCategoriesNonce,
      },
      success: function (response) {
        // Hide loading indicator
        $("#wc-optima-categories-loading").hide();

        if (response.success && response.data) {
          // Display results
          displayCategoryImportResults(response.data);
        } else {
          // Show error message
          $("#wc-optima-categories-results").html(
            '<div class="notice notice-error"><p>' +
              wc_optima_params.error_prefix +
              " " +
              (response.data || "Nie udało się zaimportować kategorii.") +
              "</p></div>",
          );
        }
      },
      error: function (xhr, status, error) {
        // Hide loading indicator and show error
        $("#wc-optima-categories-loading").hide();
        $("#wc-optima-categories-results").html(
          '<div class="notice notice-error"><p>' +
            wc_optima_params.error_prefix +
            " " +
            (error || wc_optima_params.generic_error) +
            "</p></div>",
        );
      },
    });
  });

  // Function to display category import results
  function displayCategoryImportResults(results) {
    var html = '<div class="notice notice-success"><p>';
    html += "Import kategorii zakończony pomyślnie. ";
    html += "Utworzono: " + results.created + ", ";
    html += "Zaktualizowano: " + results.updated + ", ";
    html += "Pominięto: " + results.skipped;

    // Add modified categories that were skipped if available
    if (results.modified_skipped && results.modified_skipped > 0) {
      html += ", Pominięto zmodyfikowanych: " + results.modified_skipped;
    }

    // Add categories skipped due to same simplified name
    if (results.same_slug_skipped && results.same_slug_skipped > 0) {
      html += ", Pominięto z tą samą uproszczoną nazwą: " + results.same_slug_skipped;
    }

    html += "</p></div>";

    // Add table with imported categories if there are any
    if (results.categories && results.categories.length > 0) {
      html += "<h3>Zaimportowane kategorie:</h3>";
      html += '<table class="wp-list-table widefat fixed striped categories">';
      html += "<thead><tr>";
      html += "<th>Nazwa</th>";
      html += "<th>ID Optima</th>";
      html += "<th>ID WooCommerce</th>";
      html += "<th>GID</th>";
      html += "<th>Kod</th>";
      html += "<th>Uwagi</th>";
      html += "</tr></thead>";
      html += "<tbody>";

      // Add rows for each category
      $.each(results.categories, function (_, category) {
        html += "<tr>";
        html += "<td>" + category.name + "</td>";
        html += "<td>" + category.optima_id + "</td>";
        html += "<td>" + category.wc_id + "</td>";
        html += "<td>" + (category.gid_number || "") + "</td>";
        html += "<td>" + (category.code || "") + "</td>";
        html += "<td>" + (category.note || "") + "</td>";
        html += "</tr>";
      });

      html += "</tbody></table>";
    }

    // Add the results to the page
    $("#wc-optima-categories-results").html(html);
  }

  // Function to display RO documents in a table
  function displayRODocuments(documents) {
    if (!documents.length) {
      $("#wc-optima-ro-documents-results").html(
        '<div class="notice notice-warning"><p>' +
          wc_optima_params.no_documents_found +
          "</p></div>",
      );
      return;
    }

    // Create table
    var table = $('<table class="wp-list-table widefat fixed striped ro-documents">');

    // Add table header
    var thead = $("<thead>").appendTo(table);
    var headerRow = $("<tr>").appendTo(thead);

    $("<th>").text(wc_optima_params.th_id).appendTo(headerRow);
    $("<th>").text(wc_optima_params.th_type).appendTo(headerRow);
    $("<th>").text(wc_optima_params.th_number).appendTo(headerRow);
    $("<th>").text(wc_optima_params.th_foreign_number).appendTo(headerRow);
    $("<th>").text(wc_optima_params.th_payment_method).appendTo(headerRow);
    $("<th>").text(wc_optima_params.th_currency).appendTo(headerRow);
    $("<th>").text(wc_optima_params.th_status).appendTo(headerRow);
    $("<th>").text(wc_optima_params.th_sale_date).appendTo(headerRow);
    $("<th>").text(wc_optima_params.th_amount_to_pay).appendTo(headerRow);
    $("<th>").text(wc_optima_params.th_category).appendTo(headerRow);
    $("<th>").text(wc_optima_params.th_payer).appendTo(headerRow);
    $("<th>").text(wc_optima_params.th_recipient).appendTo(headerRow);
    $("<th>").text(wc_optima_params.th_elements).appendTo(headerRow);
    $("<th>").text(wc_optima_params.th_reservation_date).appendTo(headerRow);
    $("<th>").text(wc_optima_params.th_actions).appendTo(headerRow);

    // Add table body
    var tbody = $("<tbody>").appendTo(table);

    // Add rows for each document
    $.each(documents, function (_, document) {
      var row = $("<tr>").appendTo(tbody);

      $("<td>")
        .text(document.id || "")
        .appendTo(row);
      $("<td>")
        .text(document.type || "")
        .appendTo(row);
      $("<td>")
        .text(document.fullNumber || "")
        .appendTo(row);
      $("<td>")
        .text(document.foreignNumber || "")
        .appendTo(row);
      $("<td>")
        .text(document.paymentMethod || "")
        .appendTo(row);
      $("<td>")
        .text(document.currency || "")
        .appendTo(row);
      $("<td>")
        .text(document.status || "")
        .appendTo(row);
      $("<td>")
        .text(
          document.documentSaleDate ? new Date(document.documentSaleDate).toLocaleDateString() : "",
        )
        .appendTo(row);
      $("<td>")
        .text(document.amountToPay || "")
        .appendTo(row);
      $("<td>")
        .text(document.category || "")
        .appendTo(row);
      $("<td>")
        .text(document.payer ? document.payer.name1 || document.payer.code : "")
        .appendTo(row);
      $("<td>")
        .text(document.recipient ? document.recipient.name1 || document.recipient.code : "")
        .appendTo(row);
      $("<td>")
        .text(
          document.elements
            ? document.elements.length + " " + wc_optima_params.items_suffix
            : "0 " + wc_optima_params.items_suffix,
        )
        .appendTo(row);
      $("<td>")
        .text(
          document.documentReservationDate
            ? new Date(document.documentReservationDate).toLocaleDateString()
            : "",
        )
        .appendTo(row);

      // Create actions cell with buttons
      var actionsCell = $("<td>").appendTo(row);

      // Add "Show Details" button
      $('<button class="button button-small show-ro-details-button">')
        .text("Pokaż szczegóły")
        .attr("data-doc-id", document.id)
        .appendTo(actionsCell);

      // Add space between buttons
      actionsCell.append(" ");

      // Add "Download PDF" button
      $('<a class="button button-small download-document-button">')
        .text(wc_optima_params.download_pdf)
        .attr(
          "href",
          wc_optima_params.ajax_url +
            "?action=wc_optima_download_document" +
            "&nonce=" +
            wc_optima_params.download_document_nonce +
            "&document_id=" +
            document.id +
            "&format=pdf",
        )
        .attr("target", "_blank")
        .appendTo(actionsCell);

      // Add space between buttons
      actionsCell.append(" ");

      // Add "Download HTML" button
      $('<a class="button button-small download-document-button">')
        .text(wc_optima_params.download_html)
        .attr(
          "href",
          wc_optima_params.ajax_url +
            "?action=wc_optima_download_document" +
            "&nonce=" +
            wc_optima_params.download_document_nonce +
            "&document_id=" +
            document.id +
            "&format=html",
        )
        .attr("target", "_blank")
        .appendTo(actionsCell);
    });

    // Add table to results div
    $("#wc-optima-ro-documents-results").html(table);

    // Add count message
    $("<p>")
      .text(wc_optima_params.showing_documents.replace("%d", documents.length))
      .prependTo("#wc-optima-ro-documents-results");
  }

  // Obsługa kliknięcia przycisku "Pokaż szczegóły" dla dokumentu RO
  $("#wc-optima-ro-documents-results").on("click", ".show-ro-details-button", function (e) {
    e.preventDefault();
    var documentId = $(this).data("doc-id");

    // Pokaż wskaźnik ładowania (można dodać dedykowany)
    // $("#wc-optima-ro-details-loading").show(); // Przykładowy ID
    $("#wc-optima-ro-documents-loading").show(); // Użyjemy istniejącego na razie

    // Wyczyść poprzednie szczegóły w modalu
    $("#ro-details-modal-content").empty();

    $.ajax({
      url: wc_optima_params.ajax_url,
      type: "POST",
      data: {
        action: "wc_optima_get_ro_document_details", // Nowa akcja AJAX
        nonce: wc_optima_params.ro_details_nonce, // Nowy nonce
        document_id: documentId,
      },
      success: function (response) {
        $("#wc-optima-ro-documents-loading").hide(); // Ukryj wskaźnik ładowania

        if (response.success && response.data) {
          displayRODetailsModal(response.data);
        } else {
          alert(
            wc_optima_params.error_prefix +
              " " +
              (response.data || "Nie udało się pobrać szczegółów dokumentu."),
          );
        }
      },
      error: function (xhr, status, error) {
        $("#wc-optima-ro-documents-loading").hide(); // Ukryj wskaźnik ładowania

        // Try to parse the response text for more detailed error
        var errorMessage = error || wc_optima_params.generic_error;
        try {
          if (xhr.responseText) {
            var responseObj = JSON.parse(xhr.responseText);
            if (responseObj && responseObj.data) {
              errorMessage = responseObj.data;
            }
          }
        } catch (e) {
          console.error("Error parsing error response:", e);
          if (xhr.responseText) {
            errorMessage = "Response error: " + xhr.responseText.substring(0, 100);
            if (xhr.responseText.length > 100) errorMessage += "...";
          }
        }

        alert(wc_optima_params.error_prefix + " " + errorMessage);

        // Log the full error to console for debugging
        console.error("AJAX Error:", {
          status: status,
          error: error,
          xhr: xhr,
        });
      },
    });
  });

  // Funkcja do wyświetlania szczegółów dokumentu RO w modalu jako tabela
  function displayRODetailsModal(details) {
    var modalContent = $("#ro-details-modal-content");
    modalContent.empty(); // Wyczyść poprzednią zawartość

    // Główne informacje dokumentu
    var mainInfoTable = $('<table class="wp-list-table widefat fixed striped ro-details-table">');
    var mainInfoThead = $("<thead>").appendTo(mainInfoTable);
    var mainInfoHeaderRow = $("<tr>").appendTo(mainInfoThead);

    $("<th>").text("Pole").appendTo(mainInfoHeaderRow);
    $("<th>").text("Wartość").appendTo(mainInfoHeaderRow);

    var mainInfoTbody = $("<tbody>").appendTo(mainInfoTable);

    // Dodaj główne informacje do tabeli
    for (var key in details) {
      if (details.hasOwnProperty(key) && key !== "elements" && typeof details[key] !== "object") {
        var row = $("<tr>").appendTo(mainInfoTbody);
        $("<td>").text(formatDetailKey(key)).addClass("ro-detail-label").appendTo(row);
        $("<td>").text(details[key]).appendTo(row);
      }
    }

    // Dodaj główną tabelę do modalu
    modalContent.append($("<h3>").text("Informacje podstawowe"));
    modalContent.append(mainInfoTable);

    // Obsługa obiektów zagnieżdżonych (np. płatnik, odbiorca)
    for (var key in details) {
      if (
        details.hasOwnProperty(key) &&
        typeof details[key] === "object" &&
        details[key] !== null &&
        !Array.isArray(details[key]) &&
        key !== "elements"
      ) {
        var nestedTable = $(
          '<table class="wp-list-table widefat fixed striped ro-details-nested-table">',
        );
        var nestedThead = $("<thead>").appendTo(nestedTable);
        var nestedHeaderRow = $("<tr>").appendTo(nestedThead);

        $("<th>").text("Pole").appendTo(nestedHeaderRow);
        $("<th>").text("Wartość").appendTo(nestedHeaderRow);

        var nestedTbody = $("<tbody>").appendTo(nestedTable);

        for (var nestedKey in details[key]) {
          if (details[key].hasOwnProperty(nestedKey)) {
            var nestedRow = $("<tr>").appendTo(nestedTbody);
            $("<td>")
              .text(formatDetailKey(nestedKey))
              .addClass("ro-detail-label")
              .appendTo(nestedRow);
            $("<td>").text(details[key][nestedKey]).appendTo(nestedRow);
          }
        }

        modalContent.append($("<h3>").text(formatDetailKey(key)));
        modalContent.append(nestedTable);
      }
    }

    // Obsługa elementów (jeśli istnieją)
    if (details.elements && Array.isArray(details.elements) && details.elements.length > 0) {
      modalContent.append($("<h3>").text("Elementy dokumentu"));

      // Pobierz wszystkie możliwe klucze z elementów
      var allElementKeys = [];
      details.elements.forEach(function (element) {
        for (var key in element) {
          if (element.hasOwnProperty(key) && allElementKeys.indexOf(key) === -1) {
            allElementKeys.push(key);
          }
        }
      });

      // Utwórz tabelę elementów
      var elementsTable = $(
        '<table class="wp-list-table widefat fixed striped ro-elements-table">',
      );
      var elementsThead = $("<thead>").appendTo(elementsTable);
      var elementsHeaderRow = $("<tr>").appendTo(elementsThead);

      // Dodaj nagłówki kolumn
      $("<th>").text("Lp.").appendTo(elementsHeaderRow);
      allElementKeys.forEach(function (key) {
        $("<th>").text(formatDetailKey(key)).appendTo(elementsHeaderRow);
      });

      var elementsTbody = $("<tbody>").appendTo(elementsTable);

      // Dodaj wiersze dla każdego elementu
      details.elements.forEach(function (element, index) {
        var elementRow = $("<tr>").appendTo(elementsTbody);

        // Dodaj numer porządkowy
        $("<td>")
          .text(index + 1)
          .appendTo(elementRow);

        // Dodaj wartości dla każdej kolumny
        allElementKeys.forEach(function (key) {
          var cell = $("<td>").appendTo(elementRow);

          if (element.hasOwnProperty(key)) {
            if (typeof element[key] === "object" && element[key] !== null) {
              // Dla zagnieżdżonych obiektów w elemencie, wyświetl jako JSON
              cell.text(JSON.stringify(element[key]));
            } else {
              cell.text(element[key]);
            }
          } else {
            cell.text("-");
          }
        });
      });

      // Dodaj tabelę elementów do modalu
      modalContent.append(elementsTable);

      // Dodaj przewijanie poziome dla tabeli elementów
      elementsTable.wrap('<div style="overflow-x: auto; margin-bottom: 20px;"></div>');
    }

    // Dodaj style CSS dla tabel
    $("<style>")
      .text(
        `

        .ro-details-table, .ro-details-nested-table, .ro-elements-table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 20px;
          table-layout: auto !important;
        }
        .ro-details-table th, .ro-details-nested-table th, .ro-elements-table th {
          background-color: #f1f1f1;
          font-weight: bold;
        }
        .ro-details-table td, .ro-details-nested-table td, .ro-elements-table td,
        .ro-details-table th, .ro-details-nested-table th, .ro-elements-table th {
          padding: 8px;
          border: 1px solid #ddd;
        }
        .ro-detail-label {
          font-weight: bold;
          width: 30%;
        }
        #ro-details-modal-content h3 {
          margin-top: 20px;
          margin-bottom: 10px;
        }
      `,
      )
      .appendTo(modalContent);

    // Pokaż modal
    $("#ro-details-modal").show();
  }

  // Funkcja pomocnicza do formatowania kluczy JSON na bardziej czytelne etykiety
  function formatDetailKey(key) {
    // Prosta zamiana camelCase na słowa z wielkiej litery
    var result = key.replace(/([A-Z])/g, " $1");
    return result.charAt(0).toUpperCase() + result.slice(1);
  }

  // Obsługa zamknięcia modala
  $(".ro-modal-close").on("click", function () {
    $("#ro-details-modal").hide();
  });

  // Zamknięcie modala po kliknięciu poza nim
  $(window).on("click", function (event) {
    if ($(event.target).is("#ro-details-modal")) {
      $("#ro-details-modal").hide();
    }
  });

  // Customer-related functionality removed

  // Clear search form
  $("#wc-optima-clear-search").on("click", function (e) {
    e.preventDefault();

    // Clear all input fields
    $("#wc-optima-document-id").val("");
    $("#wc-optima-foreign-number").val("");
    $("#wc-optima-full-number").val("");
    $("#wc-optima-document-date").val("");

    // Reset document type to empty value
    $("#wc-optima-document-type").val("");

    // Clear results
    $("#wc-optima-ro-documents-results").empty();
  });

  $("#wc-optima-search-ro-document").on("click", function (e) {
    e.preventDefault();

    // Get values from all search fields
    var documentId = $("#wc-optima-document-id").val();
    var foreignNumber = $("#wc-optima-foreign-number").val();
    var fullNumber = $("#wc-optima-full-number").val();
    var documentDate = $("#wc-optima-document-date").val();
    var documentType = $("#wc-optima-document-type").val();

    // If only document type is selected, we'll search for all documents of that type
    var onlyDocumentTypeSelected =
      documentType !== "" && !documentId && !foreignNumber && !fullNumber && !documentDate;

    // Prepare data object for AJAX request
    var data = {
      action: "wc_optima_search_ro_document",
      nonce: wc_optima_params.search_nonce,
    };

    // If only document type is selected, set a special search type
    if (onlyDocumentTypeSelected) {
      data.search_type = "document_type_only";
    } else {
      // Determine search type based on which fields are filled
      // Priority: ID > Foreign Number > Full Number > Date
      if (documentId) {
        data.search_type = "id";
        data.document_id = documentId;
      } else if (foreignNumber) {
        data.search_type = "foreignNumber";
        data.foreign_number = foreignNumber;
      } else if (fullNumber) {
        data.search_type = "fullNumber";
        data.full_number = fullNumber;
      } else if (documentDate) {
        data.search_type = "date";
        data.document_date = documentDate;
      } else {
        // If no search criteria is provided, show an error
        alert(wc_optima_params.enter_at_least_one_field);
        return;
      }
    }

    // Document type is no longer required
    // Removed validation that required document type

    // Add all non-empty fields to the request
    if (documentId) data.document_id = documentId;
    if (foreignNumber) data.foreign_number = foreignNumber;
    if (fullNumber) data.full_number = fullNumber;
    if (documentDate) data.document_date = documentDate;

    // Always add document type
    data.document_type = documentType;

    // Show loading indicator
    $("#wc-optima-ro-documents-loading").show();
    $("#wc-optima-ro-documents-results").empty();

    // Make AJAX request
    $.ajax({
      url: wc_optima_params.ajax_url,
      type: "POST",
      data: data,
      success: function (response) {
        // Hide loading indicator
        $("#wc-optima-ro-documents-loading").hide();

        if (response.success && response.data) {
          // Check if response.data is an array or a single object
          if (Array.isArray(response.data)) {
            // It's an array, pass it directly
            displayRODocuments(response.data);
          } else {
            // It's a single object, wrap it in an array
            displayRODocuments([response.data]);
          }
        } else {
          // Show error message
          $("#wc-optima-ro-documents-results").html(
            '<div class="notice notice-error"><p>' +
              wc_optima_params.error_prefix +
              " " +
              (response.data || wc_optima_params.document_not_found) +
              "</p></div>",
          );
        }
      },
      error: function (xhr, status, error) {
        // Hide loading indicator and show error
        $("#wc-optima-ro-documents-loading").hide();

        // Try to parse the response text for more detailed error
        var errorMessage = error || wc_optima_params.generic_error;
        try {
          if (xhr.responseText) {
            var responseObj = JSON.parse(xhr.responseText);
            if (responseObj && responseObj.data) {
              errorMessage = responseObj.data;
            }
          }
        } catch (e) {
          console.error("Error parsing error response:", e);
          // If we can't parse the JSON, show the raw response text (truncated)
          if (xhr.responseText) {
            errorMessage = "Response error: " + xhr.responseText.substring(0, 100);
            if (xhr.responseText.length > 100) errorMessage += "...";
          }
        }

        $("#wc-optima-ro-documents-results").html(
          '<div class="notice notice-error"><p>' +
            wc_optima_params.error_prefix +
            " " +
            errorMessage +
            "</p></div>",
        );

        // Log the full error to console for debugging
        console.error("AJAX Error:", {
          status: status,
          error: error,
          xhr: xhr,
        });
      },
    });
  });

  // Removed invoice-related functionality
});

