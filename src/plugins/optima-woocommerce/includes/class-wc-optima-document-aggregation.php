<?php

/**
 * Document aggregation handler class for Optima WooCommerce integration
 *
 * @package Optima_WooCommerce
 */

// Prevent direct access to the file
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Class for handling document aggregation when order status changes to processing
 */
class WC_Optima_Document_Aggregation
{
    /**
     * API instance
     *
     * @var WC_Optima_API
     */
    private $api;

    /**
     * Constructor
     *
     * @param WC_Optima_API $api API instance
     */
    public function __construct($api)
    {
        $this->api = $api;

        // Add hook for order status change to processing
        add_action('woocommerce_order_status_changed', array($this, 'process_document_aggregation'), 10, 4);
    }

    /**
     * Process document aggregation when order status changes to processing
     *
     * @param int $order_id Order ID
     * @param string $old_status Old order status
     * @param string $new_status New order status
     * @param WC_Order $order Order object
     */
    public function process_document_aggregation($order_id, $old_status, $new_status, $order)
    {
        // Only process if the new status is "processing"
        if ($new_status !== 'processing') {
            return;
        }

        // Create final invoice directly from RO document
        $this->create_final_invoice($order);
    }



    /**
     * Create Final Invoice from RO document
     *
     * @param WC_Order $order Order object
     * @return bool True if successful, false otherwise
     */
    private function create_final_invoice($order)
    {
        // Check if final invoice already created for this order
        $final_invoice_id = $order->get_meta('_optima_final_invoice_id', true);

        if (!empty($final_invoice_id)) {
            WC_Optima_Logs::log_info(sprintf(__('Integracja WC Optima: Faktura końcowa już istnieje dla zamówienia %s', 'optima-woocommerce'), $order->get_id()));
            return true;
        }

        // Get the RO document ID
        $ro_document_id = $order->get_meta('_ro_document_id', true);

        if (empty($ro_document_id)) {
            WC_Optima_Logs::log_info(sprintf(__('Integracja WC Optima: Brak dokumentu RO dla zamówienia %s', 'optima-woocommerce'), $order->get_id()));
            $order->add_order_note('Nie można utworzyć faktury końcowej - brak dokumentu RO przypisanego do zamówienia.');
            return false;
        }

        // Mark that aggregation is being attempted
        $order->update_meta_data('_optima_final_aggregation_attempted', 'yes');
        $order->save();

        $order_number = $order->get_order_number();
        WC_Optima_Logs::log_info(sprintf('[Optima Integration] Rozpoczęcie procesu agregacji do faktury końcowej dla zamówienia #%s.', $order_number));
        $order->add_order_note(sprintf('Rozpoczęcie procesu agregacji do faktury końcowej (302) dla zamówienia #%s.', $order_number));

        // Get the order total (gross value)
        $order_total = $order->get_total();

        // Prepare details for the aggregation request
        $aggregation_details = [
            'description' => sprintf('Agregacja dokumentu RO do faktury końcowej dla zamówienia WooCommerce #%s', $order_number),
            'targetDocumentAmount' => $order_total, // Add the gross order value
            'targetDocumentStatus' => 0 // Set document status to 0 as required
        ];



        // Call the API to aggregate the document
        $result = $this->api->aggregate_document((int)$ro_document_id, 302, $aggregation_details); // 302 is the targetType (Final Invoice)

        if ($result && (isset($result['targetDocumentId']) || isset($result['id']))) {
            // Aggregation successful - handle both old and new API response formats
            $final_invoice_id = isset($result['targetDocumentId']) ? $result['targetDocumentId'] : $result['id'];
            $final_invoice_number = isset($result['targetDocumentFullNumber']) ? $result['targetDocumentFullNumber'] : ($result['fullNumber'] ?? 'brak');

            // Save the new document ID and number in order meta
            $order->update_meta_data('_optima_final_invoice_id', $final_invoice_id);
            $order->update_meta_data('_optima_final_invoice_number', $final_invoice_number);
            $order->delete_meta_data('_optima_final_aggregation_attempted'); // Remove attempt flag on success
            $order->save();

            $note = sprintf(
                'Dokument RO (%d) został pomyślnie zagregowany do faktury końcowej (302). ID nowego dokumentu: %s, Numer: %s',
                $ro_document_id,
                $final_invoice_id,
                $final_invoice_number
            );

            $order->add_order_note($note);
            WC_Optima_Logs::log_info(sprintf('[Optima Integration] %s', $note));

            return true;
        } else {
            // Aggregation failed
            $error_message = isset($result['message']) ? $result['message'] : 'Nieznany błąd';
            WC_Optima_Logs::log_error(sprintf('[Optima Integration] Błąd podczas agregacji do faktury końcowej: %s', $error_message));

            $note = sprintf(
                'Nie udało się zagregować dokumentu RO (%d) do faktury końcowej. Błąd: %s',
                $ro_document_id,
                $error_message
            );
            $order->add_order_note($note);

            return false;
        }
    }
}
