<?php
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Class for handling document-related API communication with Optima
 *
 * @package Optima_WooCommerce
 */
class WC_Optima_API_Documents
{
    /**
     * API Base instance
     *
     * @var WC_Optima_API_Base
     */
    protected $api_base;

    /**
     * Constructor
     *
     * @param WC_Optima_API_Base $api_base API Base instance
     */
    public function __construct($api_base)
    {
        $this->api_base = $api_base;
    }

    /**
     * Get a specific RO document by ID from Optima API
     *
     * @param string $document_id Document ID
     * @return array|false Document data if found, false otherwise
     */
    public function get_ro_document_by_id($document_id)
    {
        $token = $this->api_base->get_access_token();
        $endpoint = '/Documents/' . $document_id;
        $method = 'GET';

        if (!$token) {
            $error_message = __('Integracja WC Optima: Nie udało się uzyskać tokena dostępu', 'optima-woocommerce');
            WC_Optima_Logs::log_error($error_message);
            WC_Optima_Logs::log_error_log($error_message);
            return false;
        }

        try {
            if (!class_exists('\\GuzzleHttp\\Client')) {
                return $this->get_ro_document_by_id_with_wp_http($token, $document_id);
            }

            $client = new \GuzzleHttp\Client();

            $options = [
                'headers' => [
                    'Authorization' => 'Bearer ' . $token
                ]
            ];

            WC_Optima_Logs::log_api_request($method, $endpoint, null);

            $response = $client->request($method, $this->api_base->api_url . $endpoint, $options);
            $status_code = $response->getStatusCode();
            $body = $response->getBody()->getContents();
            $document = json_decode($body, true);

            WC_Optima_Logs::log_api_request($method, $endpoint, null, $document);

            return $document;
        } catch (Exception $e) {
            $error_message = __('Integracja WC Optima: Błąd podczas pobierania dokumentu RO wg ID - ', 'optima-woocommerce') . $e->getMessage();
            WC_Optima_Logs::log_error($error_message);
            WC_Optima_Logs::log_error_log($error_message);

            WC_Optima_Logs::log_error(
                __('Błąd podczas pobierania dokumentu RO wg ID', 'optima-woocommerce'),
                ['document_id' => $document_id, 'error' => $e->getMessage()]
            );

            return $this->get_ro_document_by_id_with_wp_http($token, $document_id);
        }

        return false;
    }

    /**
     * Get RO document by ID using WordPress HTTP API as a fallback
     *
     * @param string $token The access token
     * @param string $document_id Document ID
     * @return array|false Document data if found, false otherwise
     */
    private function get_ro_document_by_id_with_wp_http($token, $document_id)
    {
        $endpoint = '/Documents/' . $document_id;
        $method = 'GET';
        $request_args = [
            'timeout' => 45,
            'redirection' => 5,
            'httpversion' => '1.0',
            'headers' => [
                'Authorization' => 'Bearer ' . $token
            ]
        ];

        WC_Optima_Logs::log_api_request($method, $endpoint, null);

        $response = wp_remote_get($this->api_base->api_url . $endpoint, $request_args);

        if (is_wp_error($response)) {
            $error_message = $response->get_error_message();
            WC_Optima_Logs::log_error(__('Integracja WC Optima - Błąd WP HTTP: ', 'optima-woocommerce') . $error_message);

            WC_Optima_Logs::log_error(
                __('Błąd WP HTTP podczas pobierania dokumentu RO', 'optima-woocommerce'),
                ['document_id' => $document_id, 'error' => $error_message]
            );

            return false;
        }

        $status_code = wp_remote_retrieve_response_code($response);
        $body = wp_remote_retrieve_body($response);
        $document = json_decode($body, true);

        WC_Optima_Logs::log_api_request($method, $endpoint, null, $document);

        return $document;
    }

    /**
     * Get documents by type from Optima API
     *
     * @param int|null $type Document type (e.g., 308 for RO, 302 for invoices), or null for all types
     * @return array|false Array of documents or false on failure
     */
    public function get_documents_by_type($type = null)
    {
        $token = $this->api_base->get_access_token();
        $endpoint = '/Documents';
        $method = 'GET';
        $query_params = [];

        if ($type !== null) {
            $query_params['type'] = $type;
        }

        if (!$token) {
            WC_Optima_Logs::log_error(__('Nie udało się uzyskać tokena dostępu do pobierania dokumentów.', 'optima-woocommerce'));
            return false;
        }

        $url = $this->api_base->api_url . $endpoint;
        if (!empty($query_params)) {
            $url .= '?' . http_build_query($query_params);
        }

        try {
            $request_args = [
                'timeout' => 45,
                'redirection' => 5,
                'httpversion' => '1.0',
                'headers' => [
                    'Authorization' => 'Bearer ' . $token
                ]
            ];

            WC_Optima_Logs::log_api_request($method, $endpoint, $query_params);

            $response = wp_remote_get($url, $request_args);

            if (is_wp_error($response)) {
                $error_message = $response->get_error_message();
                WC_Optima_Logs::log_error(__('Błąd WP HTTP podczas pobierania dokumentów: ', 'optima-woocommerce') . $error_message);
                return false;
            }

            $status_code = wp_remote_retrieve_response_code($response);
            $body = wp_remote_retrieve_body($response);

            WC_Optima_Logs::log_error('Raw API response: ' . $body, ['status_code' => $status_code]);

            $documents = json_decode($body, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                $error_message = 'JSON decode error: ' . json_last_error_msg() . '. Raw response: ' . substr($body, 0, 255);
                WC_Optima_Logs::log_error($error_message, ['status_code' => $status_code]);
                return false;
            }

            WC_Optima_Logs::log_api_request($method, $endpoint, $query_params, $documents);

            if ($status_code >= 200 && $status_code < 300 && is_array($documents)) {
                return $documents;
            } else {
                $error_message = __('Błąd API podczas pobierania dokumentów.', 'optima-woocommerce');
                if (is_array($documents) && isset($documents['message'])) {
                    $error_message = $documents['message'];
                } elseif (!empty($body)) {
                    $error_message = 'API error: ' . substr($body, 0, 255);
                }

                WC_Optima_Logs::log_error($error_message, ['status_code' => $status_code]);
                return false;
            }
        } catch (Exception $e) {
            WC_Optima_Logs::log_error(
                __('Wyjątek podczas pobierania dokumentów', 'optima-woocommerce'),
                ['error' => $e->getMessage(), 'type' => $type]
            );
            return false;
        }

        return false;
    }

    /**
     * Get RO documents from Optima API
     *
     * @return array|false Array of RO documents or false on failure
     */
    public function get_ro_documents()
    {
        return $this->get_documents_by_type(308);
    }

    /**
     * Create RO document in Optima
     *
     * @param array $order_data Order data in Optima format
     * @return array|false New document data if created, false or error array on failure
     */
    public function create_ro_document($order_data)
    {
        $token = $this->api_base->get_access_token();
        $endpoint = '/Documents';
        $method = 'POST';

        if (!$token) {
            WC_Optima_Logs::log_error(__('Integracja WC Optima: Nie udało się uzyskać tokena dostępu do tworzenia dokumentu RO', 'optima-woocommerce'));

            WC_Optima_Logs::log_error(
                __('Nie udało się uzyskać tokena dostępu do tworzenia dokumentu RO', 'optima-woocommerce'),
                ['order_data' => $order_data]
            );

            return [
                'error' => true,
                'message' => __('Nie udało się uzyskać tokena dostępu do API Optima', 'optima-woocommerce')
            ];
        }

        WC_Optima_Logs::log_api_request($method, $endpoint, $order_data);

        try {
            if (!class_exists('\\GuzzleHttp\\Client')) {
                return $this->create_ro_with_wp_http($token, $order_data);
            }

            $client = new \GuzzleHttp\Client();

            $options = [
                'headers' => [
                    'Authorization' => 'Bearer ' . $token,
                    'Content-Type' => 'application/json'
                ],
                'body' => json_encode($order_data)
            ];

            $max_retries = 3;
            $retry_delay = 2;
            $retry_count = 0;

            while ($retry_count <= $max_retries) {
                try {
                    $response = $client->request('POST', $this->api_base->api_url . $endpoint, $options);
                    $result = json_decode($response->getBody()->getContents(), true);

                    WC_Optima_Logs::log_api_request($method, $endpoint, $order_data, $result);
                    WC_Optima_Logs::log_info(
                        __('Dokument RO utworzony pomyślnie', 'optima-woocommerce'),
                        ['document_id' => $result['id'] ?? 'unknown', 'document_number' => $result['fullNumber'] ?? 'unknown']
                    );

                    return $result;
                } catch (\GuzzleHttp\Exception\ClientException $e) {
                    $response = $e->getResponse();
                    $status_code = $response->getStatusCode();
                    $body = $response->getBody()->getContents();
                    $result = json_decode($body, true);

                    $error_message = '';

                    if (is_array($result) && isset($result['Message'])) {
                        $error_message = $result['Message'];
                    } elseif (is_array($result) && isset($result['message'])) {
                        $error_message = $result['message'];
                    } elseif (is_array($result) && isset($result['error']) && isset($result['error']['message'])) {
                        $error_message = $result['error']['message'];
                    } elseif (is_array($result) && isset($result['ModelState'])) {
                        $validation_errors = [];
                        foreach ($result['ModelState'] as $field => $errors) {
                            if (is_array($errors)) {
                                foreach ($errors as $error) {
                                    $validation_errors[] = $field . ': ' . $error;
                                }
                            } else {
                                $validation_errors[] = $field . ': ' . $errors;
                            }
                        }
                        $error_message = 'Validation errors: ' . implode('; ', $validation_errors);
                    } elseif (!empty($body)) {
                        $error_message = $body;
                    } else {
                        $error_message = $e->getMessage();
                    }

                    WC_Optima_Logs::log_error_log('Optima RO request payload: ' . json_encode($order_data, JSON_PRETTY_PRINT));
                    WC_Optima_Logs::log_error_log('Optima RO response body: ' . $body);

                    WC_Optima_Logs::log_api_request($method, $endpoint, $order_data, $result);
                    WC_Optima_Logs::log_error(
                        sprintf(__('Błąd API (%d) podczas tworzenia dokumentu RO', 'optima-woocommerce'), $status_code),
                        [
                            'error_message' => $error_message,
                            'status_code' => $status_code,
                            'response' => $result
                        ]
                    );

                    if (is_array($result)) {
                        WC_Optima_Logs::log_error_log('Optima RO response details: ' . json_encode($result, JSON_PRETTY_PRINT));
                    }

                    WC_Optima_Logs::log_error_log(sprintf(__('Integracja WC Optima - Błąd API (%d): %s', 'optima-woocommerce'), $status_code, $error_message));

                    if ($status_code == 429 && $retry_count < $max_retries) {
                        $retry_count++;
                        WC_Optima_Logs::log_info(sprintf(
                            __('Ponowna próba (%d/%d) za %d sekund', 'optima-woocommerce'),
                            $retry_count,
                            $max_retries,
                            $retry_delay
                        ));

                        WC_Optima_Logs::log_info(
                            sprintf(
                                __('Ponowna próba (%d/%d) tworzenia dokumentu RO za %d sekund', 'optima-woocommerce'),
                                $retry_count,
                                $max_retries,
                                $retry_delay
                            )
                        );

                        sleep($retry_delay);
                        continue;
                    }

                    return [
                        'error' => true,
                        'status_code' => $status_code,
                        'message' => $error_message,
                        'retries' => $retry_count,
                        'optima_request' => $order_data,
                        'optima_response' => $body,
                        'optima_response_parsed' => $result
                    ];
                } catch (\GuzzleHttp\Exception\ServerException $e) {
                    $error_message = $e->getMessage();
                    WC_Optima_Logs::log_error(__('Integracja WC Optima: Błąd serwera podczas tworzenia dokumentu RO - ', 'optima-woocommerce') . $error_message);

                    WC_Optima_Logs::log_error(
                        __('Błąd serwera podczas tworzenia dokumentu RO', 'optima-woocommerce'),
                        ['error' => $error_message]
                    );

                    if ($retry_count < $max_retries) {
                        $retry_count++;
                        WC_Optima_Logs::log_info(sprintf(
                            __('Integracja WC Optima - Ponowna próba (%d/%d) za %d sekund', 'optima-woocommerce'),
                            $retry_count,
                            $max_retries,
                            $retry_delay
                        ));

                        WC_Optima_Logs::log_info(
                            sprintf(
                                __('Ponowna próba (%d/%d) tworzenia dokumentu RO za %d sekund', 'optima-woocommerce'),
                                $retry_count,
                                $max_retries,
                                $retry_delay
                            )
                        );

                        sleep($retry_delay);
                        continue;
                    }

                    return $this->create_ro_with_wp_http($token, $order_data);
                } catch (\GuzzleHttp\Exception\ConnectException $e) {
                    $error_message = $e->getMessage();
                    WC_Optima_Logs::log_error(__('Integracja WC Optima: Błąd połączenia podczas tworzenia dokumentu RO - ', 'optima-woocommerce') . $error_message);

                    WC_Optima_Logs::log_error(
                        __('Błąd połączenia podczas tworzenia dokumentu RO', 'optima-woocommerce'),
                        ['error' => $error_message]
                    );

                    if ($retry_count < $max_retries) {
                        $retry_count++;
                        WC_Optima_Logs::log_info(sprintf(
                            __('Integracja WC Optima - Ponowna próba (%d/%d) za %d sekund', 'optima-woocommerce'),
                            $retry_count,
                            $max_retries,
                            $retry_delay
                        ));

                        WC_Optima_Logs::log_info(
                            sprintf(
                                __('Ponowna próba (%d/%d) tworzenia dokumentu RO za %d sekund', 'optima-woocommerce'),
                                $retry_count,
                                $max_retries,
                                $retry_delay
                            )
                        );

                        sleep($retry_delay);
                        continue;
                    }

                    return $this->create_ro_with_wp_http($token, $order_data);
                }
            }
        } catch (Exception $e) {
            $error_message = $e->getMessage();
            WC_Optima_Logs::log_error(__('Integracja WC Optima: Błąd podczas tworzenia dokumentu RO - ', 'optima-woocommerce') . $error_message);

            WC_Optima_Logs::log_error(
                __('Wyjątek podczas tworzenia dokumentu RO', 'optima-woocommerce'),
                ['error' => $error_message]
            );

            return $this->create_ro_with_wp_http($token, $order_data);
        }
    }

    /**
     * Create RO document using WordPress HTTP API as a fallback
     *
     * @param string $token The access token
     * @param array $order_data Order data
     * @param int $retry_count Number of retries attempted (default 0)
     * @return array|false New document data if created, false or error array on failure
     */
    private function create_ro_with_wp_http($token, $order_data, $retry_count = 0)
    {
        $max_retries = 3;
        $retry_delay = 2;
        $endpoint = '/Documents';
        $method = 'POST';

        WC_Optima_Logs::log_api_request($method, $endpoint, $order_data);
        WC_Optima_Logs::log_info(
            __('Próba utworzenia dokumentu RO przez WP HTTP API', 'optima-woocommerce'),
            ['retry_count' => $retry_count]
        );

        $response = wp_remote_post($this->api_base->api_url . $endpoint, [
            'timeout' => 45,
            'redirection' => 5,
            'httpversion' => '1.0',
            'headers' => [
                'Authorization' => 'Bearer ' . $token,
                'Content-Type' => 'application/json'
            ],
            'body' => json_encode($order_data)
        ]);

        if (is_wp_error($response)) {
            $error_message = $response->get_error_message();
            WC_Optima_Logs::log_error(__('Integracja WC Optima - Błąd WP HTTP: ', 'optima-woocommerce') . $error_message);

            WC_Optima_Logs::log_error(
                __('Błąd WP HTTP podczas tworzenia dokumentu RO', 'optima-woocommerce'),
                ['error' => $error_message, 'retry_count' => $retry_count]
            );

            if ($retry_count < $max_retries) {
                WC_Optima_Logs::log_info(sprintf(
                    __('Integracja WC Optima - Ponowna próba (%d/%d) za %d sekund', 'optima-woocommerce'),
                    $retry_count + 1,
                    $max_retries,
                    $retry_delay
                ));

                WC_Optima_Logs::log_info(
                    sprintf(
                        __('Ponowna próba (%d/%d) tworzenia dokumentu RO za %d sekund', 'optima-woocommerce'),
                        $retry_count + 1,
                        $max_retries,
                        $retry_delay
                    )
                );

                sleep($retry_delay);

                return $this->create_ro_with_wp_http($token, $order_data, $retry_count + 1);
            }

            return false;
        }

        $status_code = wp_remote_retrieve_response_code($response);
        $body = wp_remote_retrieve_body($response);
        $result = json_decode($body, true);

        if ($status_code < 200 || $status_code >= 300) {
            $error_message = '';

            if (is_array($result) && isset($result['Message'])) {
                $error_message = $result['Message'];
            } elseif (is_array($result) && isset($result['message'])) {
                $error_message = $result['message'];
            } elseif (is_array($result) && isset($result['error']) && isset($result['error']['message'])) {
                $error_message = $result['error']['message'];
            } elseif (is_array($result) && isset($result['ModelState'])) {
                $validation_errors = [];
                foreach ($result['ModelState'] as $field => $errors) {
                    if (is_array($errors)) {
                        foreach ($errors as $error) {
                            $validation_errors[] = $field . ': ' . $error;
                        }
                    } else {
                        $validation_errors[] = $field . ': ' . $errors;
                    }
                }
                $error_message = 'Validation errors: ' . implode('; ', $validation_errors);
            } elseif (!empty($body)) {
                $error_message = $body;
            } else {
                $error_message = 'Unknown error (Status code: ' . $status_code . ')';
            }

            WC_Optima_Logs::log_error_log('Optima RO request payload (WP HTTP): ' . json_encode($order_data, JSON_PRETTY_PRINT));
            WC_Optima_Logs::log_error_log('Optima RO response body (WP HTTP): ' . $body);

            WC_Optima_Logs::log_api_request($method, $endpoint, $order_data, $result);
            WC_Optima_Logs::log_error(
                sprintf(__('Błąd API (%d) podczas tworzenia dokumentu RO (WP HTTP)', 'optima-woocommerce'), $status_code),
                [
                    'error_message' => $error_message,
                    'status_code' => $status_code,
                    'response' => $result,
                    'retry_count' => $retry_count
                ]
            );

            WC_Optima_Logs::log_error_log(sprintf(__('Integracja WC Optima - Błąd API (%d): %s', 'optima-woocommerce'), $status_code, $error_message));

            if ($status_code >= 500 && $retry_count < $max_retries) {
                WC_Optima_Logs::log_info(sprintf(
                    __('Ponowna próba (%d/%d) za %d sekund', 'optima-woocommerce'),
                    $retry_count + 1,
                    $max_retries,
                    $retry_delay
                ));

                WC_Optima_Logs::log_info(
                    sprintf(
                        __('Ponowna próba (%d/%d) tworzenia dokumentu RO za %d sekund', 'optima-woocommerce'),
                        $retry_count + 1,
                        $max_retries,
                        $retry_delay
                    )
                );

                sleep($retry_delay);

                return $this->create_ro_with_wp_http($token, $order_data, $retry_count + 1);
            }

            return [
                'error' => true,
                'status_code' => $status_code,
                'message' => $error_message,
                'retries' => $retry_count,
                'optima_request' => $order_data,
                'optima_response' => $body,
                'optima_response_parsed' => $result
            ];
        }

        WC_Optima_Logs::log_api_request($method, $endpoint, $order_data, $result);
        WC_Optima_Logs::log_info(
            __('Dokument RO utworzony pomyślnie (WP HTTP)', 'optima-woocommerce'),
            ['document_id' => $result['id'] ?? 'unknown', 'document_number' => $result['fullNumber'] ?? 'unknown']
        );

        return $result;
    }
    /**
     * Search for RO documents based on search parameters
     *
     * @param array $search_params Search parameters
     * @return array|false Array of documents or false on failure
     */
    public function search_ro_documents($search_params)
    {
        $token = $this->api_base->get_access_token();
        $query_params = [];

        if (!$token) {
            WC_Optima_Logs::log_error(__('Nie udało się uzyskać tokena dostępu do wyszukiwania dokumentów.', 'optima-woocommerce'));
            return false;
        }

        // Add type filter if provided
        if (isset($search_params['type']) && !empty($search_params['type'])) {
            $query_params['type'] = intval($search_params['type']);
        }

        // If searching by ID, use the specific endpoint
        if (isset($search_params['id']) && !empty($search_params['id'])) {
            $document = $this->get_ro_document_by_id($search_params['id']);
            return $document ? [$document] : false;
        }

        // Get all documents (filtered by type if specified)
        $documents = $this->get_documents_by_type($query_params['type'] ?? null);

        if (!$documents || !is_array($documents)) {
            return false;
        }

        // Filter documents based on search parameters
        $filtered_documents = [];
        foreach ($documents as $document) {
            $match = true;

            // Filter by foreign number
            if (isset($search_params['foreign_number']) && !empty($search_params['foreign_number'])) {
                if (!isset($document['foreignNumber']) || stripos($document['foreignNumber'], $search_params['foreign_number']) === false) {
                    $match = false;
                }
            }

            // Filter by full number
            if ($match && isset($search_params['full_number']) && !empty($search_params['full_number'])) {
                if (!isset($document['fullNumber']) || stripos($document['fullNumber'], $search_params['full_number']) === false) {
                    $match = false;
                }
            }

            // Filter by document date
            if ($match && isset($search_params['document_date']) && !empty($search_params['document_date'])) {
                // Check if documentSaleDate exists and matches the search date
                if (!isset($document['documentSaleDate'])) {
                    $match = false;
                } else {
                    // Convert API date format to Y-m-d for comparison
                    $doc_date = substr($document['documentSaleDate'], 0, 10);
                    $search_date = $search_params['document_date'];

                    if ($doc_date !== $search_date) {
                        $match = false;
                    }
                }
            }

            if ($match) {
                $filtered_documents[] = $document;
            }
        }

        // Log search results
        if (empty($filtered_documents)) {
            WC_Optima_Logs::log_info(sprintf(
                __('Wyszukiwanie dokumentów nie zwróciło wyników. Parametry: %s', 'optima-woocommerce'),
                json_encode($search_params)
            ));
            return false;
        }

        WC_Optima_Logs::log_info(sprintf(
            __('Wyszukiwanie dokumentów zwróciło %d wyników. Parametry: %s', 'optima-woocommerce'),
            count($filtered_documents),
            json_encode($search_params)
        ));

        return $filtered_documents;
    }

    /**
     * Aggregate document in Optima (create new document from existing one)
     *
     * @param int $source_document_id ID of the source document
     * @param int $target_type Type of the target document (e.g., 302 for final invoice)
     * @param array $additional_data Additional data for the aggregation
     * @return array|false Aggregation result or false on failure
     */
    public function aggregate_document($source_document_id, $target_type, $additional_data = [])
    {
        $token = $this->api_base->get_access_token();
        $endpoint = '/DocumentsAggregation';
        $method = 'POST';

        if (!$token) {
            WC_Optima_Logs::log_error(__('Nie udało się uzyskać tokena dostępu do agregacji dokumentu.', 'optima-woocommerce'));
            return false;
        }

        // Get source document to determine its type
        $source_document = $this->get_ro_document_by_id($source_document_id);
        if (!$source_document || !isset($source_document['type'])) {
            WC_Optima_Logs::log_error(sprintf(__('Nie można określić typu dokumentu źródłowego (ID: %d).', 'optima-woocommerce'), $source_document_id));
            return false;
        }

        $source_type = $source_document['type'];

        // Prepare the request body
        $body = [
            'sourceDocumentId' => (int)$source_document_id,
            'sourceType' => (int)$source_type,
            'targetType' => (int)$target_type
        ];

        // Add additional data if provided
        if (!empty($additional_data)) {
            $body = array_merge($body, $additional_data);
        }

        WC_Optima_Logs::log_info(sprintf(
            __('Rozpoczęcie agregacji dokumentu: źródło ID=%d (typ %d) -> cel typ %d', 'optima-woocommerce'),
            $source_document_id,
            $source_type,
            $target_type
        ));

        try {
            $client = new \GuzzleHttp\Client();
            $options = [
                'headers' => [
                    'Authorization' => 'Bearer ' . $token,
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json'
                ],
                'json' => $body,
                'timeout' => 30
            ];

            WC_Optima_Logs::log_api_request($method, $endpoint, $body);

            $response = $client->request($method, $this->api_base->api_url . $endpoint, $options);
            $status_code = $response->getStatusCode();
            $response_body = $response->getBody()->getContents();
            $result = json_decode($response_body, true);

            WC_Optima_Logs::log_api_request($method, $endpoint, $body, $result);

            if ($status_code === 201 && $result) {
                WC_Optima_Logs::log_info(sprintf(
                    __('Agregacja dokumentu zakończona pomyślnie: nowy dokument ID=%s', 'optima-woocommerce'),
                    $result['targetDocumentId'] ?? $result['id'] ?? 'unknown'
                ));
                return $result;
            } else {
                WC_Optima_Logs::log_error(sprintf(
                    __('Agregacja dokumentu zakończona niepowodzeniem: status %d', 'optima-woocommerce'),
                    $status_code
                ));
                return false;
            }
        } catch (\GuzzleHttp\Exception\ClientException $e) {
            $response = $e->getResponse();
            $status_code = $response->getStatusCode();
            $body = $response->getBody()->getContents();
            $result = json_decode($body, true);

            $error_message = '';
            if ($result && isset($result['message'])) {
                $error_message = $result['message'];
            } elseif ($result && isset($result['error'])) {
                $error_message = is_array($result['error']) ? ($result['error']['message'] ?? 'Unknown error') : $result['error'];
            } else {
                $error_message = $e->getMessage();
            }

            WC_Optima_Logs::log_error(sprintf(
                __('Błąd klienta podczas agregacji dokumentu (%d): %s', 'optima-woocommerce'),
                $status_code,
                $error_message
            ));

            return [
                'error' => true,
                'message' => $error_message,
                'status_code' => $status_code,
                'optima_response' => $body
            ];
        } catch (\GuzzleHttp\Exception\ServerException $e) {
            $response = $e->getResponse();
            $status_code = $response->getStatusCode();
            $body = $response->getBody()->getContents();

            WC_Optima_Logs::log_error(sprintf(
                __('Błąd serwera podczas agregacji dokumentu (%d): %s', 'optima-woocommerce'),
                $status_code,
                $e->getMessage()
            ));

            return [
                'error' => true,
                'message' => sprintf(__('Błąd serwera (%d): %s', 'optima-woocommerce'), $status_code, $e->getMessage()),
                'status_code' => $status_code,
                'optima_response' => $body
            ];
        } catch (Exception $e) {
            $error_message = __('Błąd podczas agregacji dokumentu: ', 'optima-woocommerce') . $e->getMessage();
            WC_Optima_Logs::log_error($error_message);

            return [
                'error' => true,
                'message' => $error_message
            ];
        }
    }
}
