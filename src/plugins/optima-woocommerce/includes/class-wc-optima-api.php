<?php
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Class for handling API communication with Optima
 *
 * @package Optima_WooCommerce
 */
class WC_Optima_API extends WC_Optima_API_Base
{
    /**
     * Documents API instance
     *
     * @var WC_Optima_API_Documents
     */
    private $documents_api;

    /**
     * Products API instance
     *
     * @var WC_Optima_API_Products
     */
    private $products_api;

    /**
     * Customers API instance
     *
     * @var WC_Optima_API_Customers
     */
    private $customers_api;

    /**
     * Categories API instance
     *
     * @var WC_Optima_API_Categories
     */
    private $categories_api;

    /**
     * Payments API instance
     *
     * @var WC_Optima_API_Payments
     */
    private $payments_api;

    /**
     * Constructor
     *
     * @param array $options Plugin options
     */
    public function __construct($options)
    {
        parent::__construct($options);

        // Load API component classes
        require_once plugin_dir_path(OPTIMA_WC_PLUGIN_FILE) . 'includes/api/class-wc-optima-api-documents.php';
        require_once plugin_dir_path(OPTIMA_WC_PLUGIN_FILE) . 'includes/api/class-wc-optima-api-products.php';
        require_once plugin_dir_path(OPTIMA_WC_PLUGIN_FILE) . 'includes/api/class-wc-optima-api-customers.php';
        require_once plugin_dir_path(OPTIMA_WC_PLUGIN_FILE) . 'includes/api/class-wc-optima-api-categories.php';
        require_once plugin_dir_path(OPTIMA_WC_PLUGIN_FILE) . 'includes/api/class-wc-optima-api-payments.php';

        // Initialize API components
        $this->documents_api = new WC_Optima_API_Documents($this);
        $this->products_api = new WC_Optima_API_Products($this);
        $this->customers_api = new WC_Optima_API_Customers($this);
        $this->categories_api = new WC_Optima_API_Categories($this);
        $this->payments_api = new WC_Optima_API_Payments($this);
    }

    // Documents API methods

    /**
     * Get a specific RO document by ID from Optima API
     *
     * @param string $document_id Document ID
     * @return array|false Document data if found, false otherwise
     */
    public function get_ro_document_by_id($document_id)
    {
        return $this->documents_api->get_ro_document_by_id($document_id);
    }

    /**
     * Get documents by type from Optima API
     *
     * @param int|null $type Document type (e.g., 308 for RO, 302 for invoices), or null for all types
     * @return array|false Array of documents or false on failure
     */
    public function get_documents_by_type($type = null)
    {
        return $this->documents_api->get_documents_by_type($type);
    }

    /**
     * Get RO documents from Optima API
     *
     * @return array|false Array of RO documents or false on failure
     */
    public function get_ro_documents()
    {
        return $this->documents_api->get_ro_documents();
    }

    /**
     * Create RO document in Optima
     *
     * @param array $order_data Order data in Optima format
     * @return array|false New document data if created, false or error array on failure
     */
    public function create_ro_document($order_data)
    {
        return $this->documents_api->create_ro_document($order_data);
    }

    /**
     * Aggregate document in Optima (create new document from existing one)
     *
     * @param int $source_document_id ID of the source document
     * @param int $target_type Type of the target document (e.g., 302 for final invoice)
     * @param array $additional_data Additional data for the aggregation
     * @return array|false Aggregation result or false on failure
     */
    public function aggregate_document($source_document_id, $target_type, $additional_data = [])
    {
        return $this->documents_api->aggregate_document($source_document_id, $target_type, $additional_data);
    }

    // Products API methods

    /**
     * Get products from Optima API
     *
     * @param int $offset Optional. Offset for pagination.
     * @param int $limit Optional. Number of products per page. Default 100.
     * @return array|false Array of products or false on failure
     */
    public function get_optima_products($offset = 0, $limit = 100)
    {
        return $this->products_api->get_optima_products($offset, $limit);
    }

    /**
     * Get product stock quantities from Optima API
     *
     * @return array|false Array of stock data or false on failure
     */
    public function get_optima_stock()
    {
        return $this->products_api->get_optima_stock();
    }

    // Customers API methods

    /**
     * Get customers from Optima API
     *
     * @param int $limit Optional. Maximum number of customers to return. Default 0 (all customers).
     * @return array|false Array of customers or false on failure
     */
    public function get_optima_customers($limit = 0)
    {
        return $this->customers_api->get_optima_customers($limit);
    }

    /**
     * Create a new customer in Optima
     *
     * @param array $customer_data Customer data in Optima format
     * @return array|false New customer data if created, false on failure
     */
    public function create_optima_customer($customer_data)
    {
        return $this->customers_api->create_optima_customer($customer_data);
    }

    /**
     * Get a specific customer by ID from Optima API
     *
     * @param string $customer_id Customer ID
     * @return array|false Customer data if found, false otherwise
     */
    public function get_optima_customer_by_id($customer_id)
    {
        return $this->customers_api->get_optima_customer_by_id($customer_id);
    }

    // Categories API methods

    /**
     * Get categories from Optima API
     *
     * @return array|false Array of categories or false on failure
     */
    public function get_optima_categories()
    {
        return $this->categories_api->get_optima_categories();
    }

    /**
     * Get items groups from Optima API
     *
     * @return array|false Array of items groups or false on failure
     */
    public function get_items_groups()
    {
        return $this->categories_api->get_items_groups();
    }

    // Payments API methods

    /**
     * Create payment in Optima
     *
     * @param array $payment_data Payment data
     * @return array|false Payment data if created successfully, false or error array on failure
     */
    public function create_payment($payment_data)
    {
        return $this->payments_api->create_payment($payment_data);
    }

    /**
     * Get invoices from Optima API
     *
     * @param array $filters Optional. Filters to apply to the invoice search.
     * @param int $limit Optional. Maximum number of invoices to return. Default 0 (all invoices).
     * @return array|false Array of invoices or false on failure
     */
    public function get_optima_invoices($filters = array(), $limit = 0)
    {
        // Delegate to documents API with appropriate filters
        $invoices = $this->get_documents_by_type(302); // 302 is the invoice type

        if (!$invoices) {
            return false;
        }

        // Apply filters if provided
        if (!empty($filters)) {
            $filtered_invoices = [];
            foreach ($invoices as $invoice) {
                $match = true;
                foreach ($filters as $key => $value) {
                    if (!isset($invoice[$key]) || $invoice[$key] != $value) {
                        $match = false;
                        break;
                    }
                }
                if ($match) {
                    $filtered_invoices[] = $invoice;
                }
            }
            $invoices = $filtered_invoices;
        }

        // Apply limit if provided
        if ($limit > 0 && is_array($invoices)) {
            $invoices = array_slice($invoices, 0, $limit);
        }

        return $invoices;
    }

    /**
     * Search for RO documents based on search parameters
     *
     * @param array $search_params Search parameters
     * @return array|false Array of documents or false on failure
     */
    public function search_ro_documents($search_params)
    {
        return $this->documents_api->search_ro_documents($search_params);
    }

    /**
     * Find RO document by foreign number
     *
     * @param string $foreign_number Foreign number to search for
     * @return array|false Document data if found, false otherwise
     */
    public function find_ro_document_by_foreign_number($foreign_number)
    {
        $search_params = [
            'foreign_number' => $foreign_number,
            'type' => 308 // RO document type
        ];

        $documents = $this->search_ro_documents($search_params);

        if (!$documents || !is_array($documents) || empty($documents)) {
            return false;
        }

        // Return the first matching document
        return $documents[0];
    }

    /**
     * Download document in PDF or HTML format
     *
     * @param int $document_id Document ID
     * @param string $format Format (pdf or html)
     * @return array Document content and content type or error information
     */
    public function download_document($document_id, $format = 'pdf')
    {
        $token = $this->get_access_token();

        if (!$token) {
            return [
                'error' => true,
                'message' => __('Nie udało się uzyskać tokena dostępu', 'optima-woocommerce')
            ];
        }

        if ($format !== 'pdf' && $format !== 'html') {
            return [
                'error' => true,
                'message' => __('Nieprawidłowy format dokumentu. Dozwolone formaty: pdf, html', 'optima-woocommerce')
            ];
        }

        $endpoint = "/Documents/{$document_id}/print";
        $query_params = ['format' => $format];
        $url = $this->api_url . $endpoint . '?' . http_build_query($query_params);

        $request_args = [
            'timeout' => 60,
            'headers' => [
                'Authorization' => "Bearer {$token}"
            ]
        ];

        WC_Optima_Logs::log_info(sprintf(
            __('Pobieranie dokumentu ID: %s w formacie %s', 'optima-woocommerce'),
            $document_id,
            $format
        ));

        $response = wp_remote_get($url, $request_args);

        if (is_wp_error($response)) {
            $error_message = $response->get_error_message();
            WC_Optima_Logs::log_error(sprintf(
                __('Błąd podczas pobierania dokumentu: %s', 'optima-woocommerce'),
                $error_message
            ));

            return [
                'error' => true,
                'message' => $error_message
            ];
        }

        $status_code = wp_remote_retrieve_response_code($response);

        if ($status_code !== 200) {
            $error_message = sprintf(
                __('Błąd API podczas pobierania dokumentu. Kod statusu: %s', 'optima-woocommerce'),
                $status_code
            );

            WC_Optima_Logs::log_error($error_message);

            return [
                'error' => true,
                'message' => $error_message
            ];
        }

        $content = wp_remote_retrieve_body($response);
        $content_type = $format === 'pdf' ? 'application/pdf' : 'text/html';

        return [
            'error' => false,
            'content' => $content,
            'content_type' => $content_type
        ];
    }

    /**
     * Map WooCommerce payment method to Optima payment method ID
     *
     * @param string $wc_payment_method WooCommerce payment method
     * @return int Optima payment method ID
     */
    public function map_wc_payment_method_to_optima($wc_payment_method)
    {
        $payment_method_map = [
            'bacs' => 1,           // Direct bank transfer
            'cheque' => 2,         // Check payments
            'cod' => 3,            // Cash on delivery
            'paypal' => 4,         // PayPal
            'stripe' => 5,         // Stripe
            'przelewy24' => 6,     // Przelewy24
            'dotpay' => 7,         // Dotpay
            'payu' => 8,           // PayU
            'tpay' => 9,           // Tpay
            'bluemedia' => 10      // Blue Media
        ];

        return $payment_method_map[$wc_payment_method] ?? 1; // Default to bank transfer
    }
}
