<?php

if (!defined('ABSPATH')) {
    exit;
}

/**
 * Admin class for handling documents-related functionality
 */
class WC_Optima_Admin_Documents
{
    /**
     * Plugin options
     *
     * @var array
     */
    private $options;

    /**
     * Constructor
     *
     * @param array $options Plugin options
     */
    public function __construct($options)
    {
        $this->options = $options;

        // Register AJAX handlers
        add_action('wp_ajax_wc_optima_search_ro_document', array($this, 'ajax_search_ro_document'));
        add_action('wp_ajax_wc_optima_get_ro_document_details', array($this, 'ajax_get_ro_document_details'));
        add_action('wp_ajax_wc_optima_download_document', array($this, 'ajax_download_document'));
    }

    /**
     * Render the Documents tab content
     */
    public function render_tab()
    {
?>
        <div class="optima-rco-section">
            <h2><?php _e('Dokumenty Optima', 'optima-woocommerce'); ?></h2>

            <?php if (empty($this->options['api_url']) || empty($this->options['username']) || empty($this->options['password'])): ?>
                <div class="notice notice-warning">
                    <p><?php printf(__('Proszę skonfigurować ustawienia API przed pobraniem dokumentów. Przejdź do zakładki <a href="%s">Ustawienia</a>.', 'optima-woocommerce'), '?page=wc-optima-integration&tab=settings'); ?></p>
                </div>
            <?php else: ?>
                <div class="optima-search-document">
                    <h3><?php _e('Wyszukiwanie dokumentów', 'optima-woocommerce'); ?></h3>
                    <p class="search-description"><?php _e('Wypełnij dowolne pole, aby wyszukać dokumenty. Możesz opcjonalnie wybrać typ dokumentu, aby zawęzić wyniki. Możesz użyć jednego lub wielu kryteriów jednocześnie.', 'optima-woocommerce'); ?></p>

                    <div class="search-fields-grid">
                        <div class="search-field-container">
                            <label for="wc-optima-document-type"><?php _e('Typ dokumentu (opcjonalnie):', 'optima-woocommerce'); ?></label>
                            <select id="wc-optima-document-type" name="document_type">
                                <option value=""><?php _e('-- Wszystkie typy dokumentów --', 'optima-woocommerce'); ?></option>
                                <option value="308"><?php _e('Zamówienia od klientów (RO)', 'optima-woocommerce'); ?></option>
                                <option value="301"><?php _e('Faktury zakupu', 'optima-woocommerce'); ?></option>
                                <option value="302"><?php _e('Faktury sprzedaży', 'optima-woocommerce'); ?></option>
                                <option value="303"><?php _e('Przyjęcia wewnętrzne', 'optima-woocommerce'); ?></option>
                                <option value="304"><?php _e('Wydania wewnętrzne', 'optima-woocommerce'); ?></option>
                                <option value="305"><?php _e('Paragony', 'optima-woocommerce'); ?></option>
                                <option value="306"><?php _e('Wydania zewnętrzne WZ', 'optima-woocommerce'); ?></option>
                                <option value="307"><?php _e('Przyjęcia zewnętrzne PZ', 'optima-woocommerce'); ?></option>
                                <option value="309"><?php _e('Zamówienia do dostawców', 'optima-woocommerce'); ?></option>
                                <option value="312"><?php _e('Przesunięcia międzymagazynowe', 'optima-woocommerce'); ?></option>
                                <option value="317"><?php _e('Przyjęcia wewnętrzne produktów', 'optima-woocommerce'); ?></option>
                            </select>
                        </div>

                        <div class="search-field-container">
                            <label for="wc-optima-document-id"><?php _e('ID Dokumentu:', 'optima-woocommerce'); ?></label>
                            <input type="text" id="wc-optima-document-id" name="document_id" placeholder="<?php _e('Wprowadź ID dokumentu', 'optima-woocommerce'); ?>">
                        </div>

                        <div class="search-field-container">
                            <label for="wc-optima-foreign-number"><?php _e('Numer obcy:', 'optima-woocommerce'); ?></label>
                            <input type="text" id="wc-optima-foreign-number" name="foreign_number" placeholder="<?php _e('Wprowadź numer obcy', 'optima-woocommerce'); ?>">
                        </div>

                        <div class="search-field-container">
                            <label for="wc-optima-full-number"><?php _e('Pełny numer:', 'optima-woocommerce'); ?></label>
                            <input type="text" id="wc-optima-full-number" name="full_number" placeholder="<?php _e('Wprowadź pełny numer dokumentu', 'optima-woocommerce'); ?>">
                        </div>

                        <div class="search-field-container">
                            <label for="wc-optima-document-date"><?php _e('Data dokumentu:', 'optima-woocommerce'); ?></label>
                            <input type="date" id="wc-optima-document-date" name="document_date">
                        </div>
                    </div>

                    <div class="search-actions">
                        <button id="wc-optima-search-ro-document" class="button button-primary"><?php _e('Wyszukaj dokumenty', 'optima-woocommerce'); ?></button>
                        <button id="wc-optima-clear-search" class="button"><?php _e('Wyczyść formularz', 'optima-woocommerce'); ?></button>
                    </div>
                </div>

                <div id="wc-optima-ro-documents-loading" style="display: none;">
                    <p><span class="spinner is-active" style="float: none;"></span> <?php _e('Ładowanie dokumentów...', 'optima-woocommerce'); ?></p>
                </div>

                <div id="wc-optima-ro-documents-results" style="margin-top: 20px;"></div>

                <div id="ro-details-modal" class="ro-modal" style="display: none;">
                    <div class="ro-modal-content">
                        <span class="ro-modal-close">&times;</span>
                        <h2><?php _e('Szczegóły Dokumentu', 'optima-woocommerce'); ?></h2>
                        <div id="ro-details-modal-content">
                            <p><?php _e('Ładowanie szczegółów...', 'optima-woocommerce'); ?></p>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
<?php
    }

    /**
     * AJAX handler for searching RO documents
     */
    public function ajax_search_ro_document()
    {
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'wc_optima_search_ro_document')) {
            wp_send_json_error(__('Nieprawidłowy token bezpieczeństwa', 'optima-woocommerce'));
            return;
        }

        $api = WC_Optima_Integration::get_api_instance();
        if (!$api) {
            wp_send_json_error(__('API nie zostało zainicjowane', 'optima-woocommerce'));
            return;
        }

        $search_params = [];
        $search_type = isset($_POST['search_type']) ? sanitize_text_field($_POST['search_type']) : '';
        $has_search_criteria = false;

        if (isset($_POST['document_type']) && !empty($_POST['document_type'])) {
            $search_params['type'] = intval($_POST['document_type']);
        }

        if (isset($_POST['document_id']) && !empty($_POST['document_id'])) {
            $search_params['id'] = sanitize_text_field($_POST['document_id']);
            $has_search_criteria = true;

            if (empty($search_type)) {
                $search_type = 'id';
            }
        }

        if (isset($_POST['foreign_number']) && !empty($_POST['foreign_number'])) {
            $search_params['foreign_number'] = sanitize_text_field($_POST['foreign_number']);
            $has_search_criteria = true;

            if (empty($search_type)) {
                $search_type = 'foreignNumber';
            }
        }

        if (isset($_POST['full_number']) && !empty($_POST['full_number'])) {
            $search_params['full_number'] = sanitize_text_field($_POST['full_number']);
            $has_search_criteria = true;

            if (empty($search_type)) {
                $search_type = 'fullNumber';
            }
        }

        if (isset($_POST['document_date']) && !empty($_POST['document_date'])) {
            $search_params['document_date'] = sanitize_text_field($_POST['document_date']);
            $has_search_criteria = true;

            if (empty($search_type)) {
                $search_type = 'date';
            }
        }

        $document_type_only = isset($_POST['search_type']) && $_POST['search_type'] === 'document_type_only';

        if (!$has_search_criteria && !$document_type_only) {
            wp_send_json_error(__('Proszę podać przynajmniej jedno kryterium wyszukiwania', 'optima-woocommerce'));
            return;
        }

        $documents = $api->search_ro_documents($search_params);

        if (!$documents) {
            $error_message = __('Nie znaleziono dokumentów spełniających podane kryteria wyszukiwania', 'optima-woocommerce');

            $criteria = [];

            if (isset($search_params['id'])) {
                $criteria[] = sprintf(__('ID: %s', 'optima-woocommerce'), $search_params['id']);
            }

            if (isset($search_params['foreign_number'])) {
                $criteria[] = sprintf(__('Numer obcy: %s', 'optima-woocommerce'), $search_params['foreign_number']);
            }

            if (isset($search_params['full_number'])) {
                $criteria[] = sprintf(__('Pełny numer: %s', 'optima-woocommerce'), $search_params['full_number']);
            }

            if (isset($search_params['document_date'])) {
                $criteria[] = sprintf(__('Data: %s', 'optima-woocommerce'), $search_params['document_date']);
            }

            if (!empty($criteria)) {
                $error_message .= ' (' . implode(', ', $criteria) . ')';
            }

            // Log the error for debugging
            WC_Optima_Logs::log_error(sprintf(
                __('Błąd wyszukiwania dokumentów: %s. Parametry: %s', 'optima-woocommerce'),
                $error_message,
                json_encode($search_params)
            ));

            wp_send_json_error($error_message);
            return;
        }

        // Log successful search
        WC_Optima_Logs::log_info(sprintf(
            __('Wyszukiwanie dokumentów zwróciło %d wyników. Parametry: %s', 'optima-woocommerce'),
            count($documents),
            json_encode($search_params)
        ));

        wp_send_json_success($documents);
    }

    /**
     * AJAX handler for fetching RO document details by ID
     */
    public function ajax_get_ro_document_details()
    {
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'wc_optima_get_ro_document_details')) {
            wp_send_json_error(__('Nieprawidłowy token bezpieczeństwa', 'optima-woocommerce'));
            return;
        }

        if (!isset($_POST['document_id']) || empty($_POST['document_id'])) {
            wp_send_json_error(__('ID dokumentu jest wymagane', 'optima-woocommerce'));
            return;
        }

        $document_id = sanitize_text_field($_POST['document_id']);

        $api = WC_Optima_Integration::get_api_instance();
        if (!$api) {
            wp_send_json_error(__('API nie zostało zainicjowane', 'optima-woocommerce'));
            return;
        }

        $document_details = $api->get_ro_document_by_id($document_id);

        if (!$document_details) {
            wp_send_json_error(sprintf(__('Nie znaleziono szczegółów dokumentu o ID: %s', 'optima-woocommerce'), $document_id));
            return;
        }

        wp_send_json_success($document_details);
    }

    /**
     * AJAX handler for downloading document in PDF or HTML format
     */
    public function ajax_download_document()
    {
        if (!isset($_REQUEST['nonce']) || !wp_verify_nonce($_REQUEST['nonce'], 'wc_optima_download_document')) {
            wp_send_json_error(__('Nieprawidłowy token bezpieczeństwa', 'optima-woocommerce'));
            return;
        }

        if (!isset($_REQUEST['document_id']) || empty($_REQUEST['document_id'])) {
            wp_send_json_error(__('ID dokumentu jest wymagane', 'optima-woocommerce'));
            return;
        }

        if (!isset($_REQUEST['format']) || empty($_REQUEST['format'])) {
            $format = 'pdf';
        } else {
            $format = sanitize_text_field($_REQUEST['format']);

            if ($format !== 'pdf' && $format !== 'html') {
                wp_send_json_error(__('Nieprawidłowy format dokumentu. Dozwolone formaty: pdf, html', 'optima-woocommerce'));
                return;
            }
        }

        $document_id = sanitize_text_field($_REQUEST['document_id']);

        $api = WC_Optima_Integration::get_api_instance();
        if (!$api) {
            wp_send_json_error(__('API nie zostało zainicjowane', 'optima-woocommerce'));
            return;
        }

        $result = $api->download_document($document_id, $format);

        if (isset($result['error']) && $result['error']) {
            wp_send_json_error($result['message']);
            return;
        }

        header("Content-Type: {$result['content_type']}");
        header("Content-Disposition: attachment; filename=\"document_{$document_id}.{$format}\"");
        header("Content-Length: " . strlen($result['content']));
        header('Cache-Control: private, max-age=0, must-revalidate');
        header('Pragma: public');

        echo $result['content'];
        exit;
    }
}
