<?php

if (!defined('ABSPATH')) {
    exit;
}

/**
 * Admin class for handling product-related functionality
 */
class WC_Optima_Admin_Product
{
    /**
     * Plugin options
     *
     * @var array
     */
    private $options;

    /**
     * Constructor
     *
     * @param array $options Plugin options
     */
    public function __construct($options)
    {
        $this->options = $options;

        // Register hooks
        add_filter('woocommerce_product_data_tabs', array($this, 'add_optima_product_data_tab'));
        add_action('woocommerce_product_data_panels', array($this, 'display_optima_meta_data_panel'));
        add_action('show_user_profile', array($this, 'display_optima_customer_id_field'));
        add_action('edit_user_profile', array($this, 'display_optima_customer_id_field'));
        add_action('woocommerce_admin_order_data_after_billing_address', array($this, 'display_optima_order_integration_data'));
    }

    /**
     * Add a new tab to the product data metabox
     *
     * @param array $tabs Product data tabs
     * @return array Modified tabs
     */
    public function add_optima_product_data_tab($tabs)
    {
        $tabs['optima'] = array(
            'label'    => __('Optima', 'optima-woocommerce'),
            'target'   => 'optima_product_data',
            'class'    => array(),
            'priority' => 90
        );
        return $tabs;
    }

    /**
     * Display Optima meta data in the product data panel
     */
    public function display_optima_meta_data_panel()
    {
        global $post;

        $product_id = $post->ID;

        $optima_id = get_post_meta($product_id, '_optima_id', true);
        $optima_type = get_post_meta($product_id, '_optima_type', true);
        $optima_vat_rate = get_post_meta($product_id, '_optima_vat_rate', true);
        $optima_unit = get_post_meta($product_id, '_optima_unit', true);
        $optima_barcode = get_post_meta($product_id, '_optima_barcode', true);
        $optima_catalog_number = get_post_meta($product_id, '_optima_catalog_number', true);
        $optima_default_group = get_post_meta($product_id, '_optima_default_group', true);
        $optima_sales_category = get_post_meta($product_id, '_optima_sales_category', true);
        $optima_stock_data = get_post_meta($product_id, '_optima_stock_data', true);

        if (empty($optima_id) && empty($optima_catalog_number) && empty($optima_barcode)) {
            return;
        }

?>
        <div id="optima_product_data" class="panel woocommerce_options_panel">
            <div class="options_group">
                <p class="form-field">
                    <label><?php _e('ID Optima', 'optima-woocommerce'); ?></label>
                    <span><?php echo esc_html($optima_id); ?></span>
                </p>
                <p class="form-field">
                    <label><?php _e('Numer katalogowy', 'optima-woocommerce'); ?></label>
                    <span><?php echo esc_html($optima_catalog_number); ?></span>
                </p>
                <p class="form-field">
                    <label><?php _e('Kod kreskowy', 'optima-woocommerce'); ?></label>
                    <span><?php echo esc_html($optima_barcode); ?></span>
                </p>
                <p class="form-field">
                    <label><?php _e('Typ', 'optima-woocommerce'); ?></label>
                    <span><?php echo esc_html($optima_type); ?></span>
                </p>
                <p class="form-field">
                    <label><?php _e('Stawka VAT', 'optima-woocommerce'); ?></label>
                    <span><?php echo esc_html($optima_vat_rate); ?></span>
                </p>
                <p class="form-field">
                    <label><?php _e('Jednostka', 'optima-woocommerce'); ?></label>
                    <span><?php echo esc_html($optima_unit); ?></span>
                </p>
                <p class="form-field">
                    <label><?php _e('Grupa domyślna', 'optima-woocommerce'); ?></label>
                    <span><?php echo esc_html($optima_default_group); ?></span>
                </p>
                <p class="form-field">
                    <label><?php _e('Kategoria sprzedaży', 'optima-woocommerce'); ?></label>
                    <span><?php echo esc_html($optima_sales_category); ?></span>
                </p>
                <?php if (!empty($optima_stock_data)) :
                    $stock_data = json_decode($optima_stock_data, true);
                ?>
                    <p class="form-field">
                        <label><?php _e('Ilość w magazynie', 'optima-woocommerce'); ?></label>
                        <span><?php echo isset($stock_data['quantity']) ? esc_html($stock_data['quantity']) : '0'; ?></span>
                    </p>
                    <p class="form-field">
                        <label><?php _e('Dostępne', 'optima-woocommerce'); ?></label>
                        <span><?php echo isset($stock_data['available']) ? esc_html($stock_data['available']) : '0'; ?></span>
                    </p>
                <?php endif; ?>
            </div>
        </div>
    <?php
    }

    /**
     * Display Optima customer ID field in user profile
     *
     * @param WP_User $user The user object being edited
     */
    public function display_optima_customer_id_field($user)
    {
        $optima_customer_id = get_user_meta($user->ID, '_optima_customer_id', true);

        if (empty($optima_customer_id)) {
            return;
        }

    ?>
        <h3><?php _e('Integracja Optima', 'optima-woocommerce'); ?></h3>
        <table class="form-table">
            <tr>
                <th><label for="optima_customer_id"><?php _e('ID Klienta Optima', 'optima-woocommerce'); ?></label></th>
                <td>
                    <input type="text" name="optima_customer_id" id="optima_customer_id" value="<?php echo esc_attr($optima_customer_id); ?>" class="regular-text" readonly />
                    <p class="description"><?php _e('To jest ID klienta w systemie Optima.', 'optima-woocommerce'); ?></p>
                </td>
            </tr>
        </table>
    <?php
    }

    /**
     * Display Optima integration data in order edit screen
     *
     * @param WC_Order $order Order object
     */
    public function display_optima_order_integration_data($order)
    {
        $optima_customer_id = $order->get_meta('_optima_customer_id', true);

        // Get document IDs from order metadata
        $ro_document_id = $order->get_meta('_ro_document_id', true);
        $final_invoice_id = $order->get_meta('_optima_final_invoice_id', true);

        // Get document numbers from order metadata
        $ro_document_number = $order->get_meta('_ro_document_number', true);
        $final_invoice_number = $order->get_meta('_optima_final_invoice_number', true);

        // If document numbers are not stored in metadata, try to fetch them from Optima API
        $api = WC_Optima_Integration::get_api_instance();

        if (!empty($ro_document_id) && empty($ro_document_number) && $api) {
            $ro_document = $api->get_ro_document_by_id($ro_document_id);
            if ($ro_document && isset($ro_document['fullNumber'])) {
                $ro_document_number = $ro_document['fullNumber'];
                $order->update_meta_data('_ro_document_number', $ro_document_number);
                $order->save();
            }
        }

        if (empty($optima_customer_id) && empty($ro_document_id) && empty($final_invoice_id)) {
            return;
        }
    ?>
        <div class="optima-integration-data">
            <h3><?php _e('Integracja Optima', 'optima-woocommerce'); ?></h3>
            <div class="optima-data-fields">
                <?php if (!empty($optima_customer_id)) : ?>
                    <p>
                        <strong><?php _e('ID Klienta Optima:', 'optima-woocommerce'); ?></strong>
                        <span><?php echo esc_html($optima_customer_id); ?></span>
                    </p>
                <?php endif; ?>

                <?php if (!empty($ro_document_id)) : ?>
                    <p>
                        <strong><?php _e('ID Dokumentu RO Optima:', 'optima-woocommerce'); ?></strong>
                        <a href="<?php echo esc_url(admin_url('admin-ajax.php') . '?action=wc_optima_download_document&nonce=' . wp_create_nonce('wc_optima_download_document') . '&document_id=' . esc_attr($ro_document_id) . '&format=pdf'); ?>" target="_blank"><?php echo esc_html($ro_document_id); ?></a>
                        <?php if (!empty($ro_document_number)) : ?>
                            <span>(<?php echo esc_html($ro_document_number); ?>)</span>
                        <?php endif; ?>
                    </p>
                <?php endif; ?>



                <?php if (!empty($final_invoice_id)) : ?>
                    <p>
                        <strong><?php _e('ID Faktury Końcowej:', 'optima-woocommerce'); ?></strong>
                        <a href="<?php echo esc_url(admin_url('admin-ajax.php') . '?action=wc_optima_download_document&nonce=' . wp_create_nonce('wc_optima_download_document') . '&document_id=' . esc_attr($final_invoice_id) . '&format=pdf'); ?>" target="_blank"><?php echo esc_html($final_invoice_id); ?></a>
                        <?php if (!empty($final_invoice_number)) : ?>
                            <span>(<?php echo esc_html($final_invoice_number); ?>)</span>
                        <?php endif; ?>
                    </p>
                <?php endif; ?>
            </div>
        </div>
<?php
    }
}
