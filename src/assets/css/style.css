/*!
Theme Name: GalBud
Theme URI: https://hofumarketing.pl/
Author: Hofu Marketing
Author URI: https://hofumarketing.pl/
Description: GalBud theme.
Version: 0.1
*/

@import url("tab-content.css");

/* Objectivity Font Family */
@font-face {
  font-family: "Objectivity";
  src: url("./fonts/objectivity/Objectivity-Thin.woff2") format("woff2"),
    url("./fonts/objectivity/Objectivity-Thin.otf") format("opentype");
  font-weight: 100;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Objectivity";
  src: url("./fonts/objectivity/Objectivity-ThinSlanted.woff2") format("woff2"),
    url("./fonts/objectivity/Objectivity-ThinSlanted.otf") format("opentype");
  font-weight: 100;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: "Objectivity";
  src: url("./fonts/objectivity/Objectivity-Light.woff2") format("woff2"),
    url("./fonts/objectivity/Objectivity-Light.otf") format("opentype");
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Objectivity";
  src: url("./fonts/objectivity/Objectivity-LightSlanted.woff2") format("woff2"),
    url("./fonts/objectivity/Objectivity-LightSlanted.otf") format("opentype");
  font-weight: 300;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: "Objectivity";
  src: url("./fonts/objectivity/Objectivity-Regular.woff2") format("woff2"),
    url("./fonts/objectivity/Objectivity-Regular.otf") format("opentype");
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Objectivity";
  src: url("./fonts/objectivity/Objectivity-RegularSlanted.woff2") format("woff2"),
    url("./fonts/objectivity/Objectivity-RegularSlanted.otf") format("opentype");
  font-weight: 400;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: "Objectivity";
  src: url("./fonts/objectivity/Objectivity-Medium.woff2") format("woff2"),
    url("./fonts/objectivity/Objectivity-Medium.otf") format("opentype");
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Objectivity";
  src: url("./fonts/objectivity/Objectivity-MediumSlanted.woff2") format("woff2"),
    url("./fonts/objectivity/Objectivity-MediumSlanted.otf") format("opentype");
  font-weight: 500;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: "Objectivity";
  src: url("./fonts/objectivity/Objectivity-Bold.woff2") format("woff2"),
    url("./fonts/objectivity/Objectivity-Bold.otf") format("opentype");
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Objectivity";
  src: url("./fonts/objectivity/Objectivity-BoldSlanted.woff2") format("woff2"),
    url("./fonts/objectivity/Objectivity-BoldSlanted.otf") format("opentype");
  font-weight: 700;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: "Objectivity";
  src: url("./fonts/objectivity/Objectivity-ExtraBold.woff2") format("woff2"),
    url("./fonts/objectivity/Objectivity-ExtraBold.otf") format("opentype");
  font-weight: 800;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Objectivity";
  src: url("./fonts/objectivity/Objectivity-ExtraBoldSlanted.woff2") format("woff2"),
    url("./fonts/objectivity/Objectivity-ExtraBoldSlanted.otf") format("opentype");
  font-weight: 800;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: "Objectivity";
  src: url("./fonts/objectivity/Objectivity-Black.woff2") format("woff2"),
    url("./fonts/objectivity/Objectivity-Black.otf") format("opentype");
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Objectivity";
  src: url("./fonts/objectivity/Objectivity-BlackSlanted.woff2") format("woff2"),
    url("./fonts/objectivity/Objectivity-BlackSlanted.otf") format("opentype");
  font-weight: 900;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: "Objectivity";
  src: url("./fonts/objectivity/Objectivity-Super.woff2") format("woff2"),
    url("./fonts/objectivity/Objectivity-Super.otf") format("opentype");
  font-weight: 950;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Objectivity";
  src: url("./fonts/objectivity/Objectivity-SuperSlanted.woff2") format("woff2"),
    url("./fonts/objectivity/Objectivity-SuperSlanted.otf") format("opentype");
  font-weight: 950;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: "Girloy";
  src: url("./fonts/girloy/Gilroy-Light.otf") format("opentype");
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Girloy";
  src: url("./fonts/girloy/Gilroy-ExtraBold.otf") format("opentype");
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}

:root {
  --primary-color: #01a0e2;
  --green-accent: rgba(77, 236, 148, 1);
  --container-width: 84rem;
  --second-font-color: #707070;
  --grey-color: #bebebe;
  --stroke: #bebebe;
  --white: #fff;
  --black: #000;
  --alert-color: #f5dc4b;
  --header-height: 80px; /* Used for navigation positioning */
  --mask-url: url("https://raw.githubusercontent.com/robin-dela/css-mask-animation/master/img/urban-sprite.png");
}

::selection {
  color: #fff;
  background-color: var(--primary-color);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;

  &:focus {
    outline: none;
  }
}

body {
  overflow-x: hidden;
  font-family: "Objectivity", sans-serif;
  font-size: 1rem;
}

p {
  font-size: 1rem;
}

.container {
  width: min(100%, var(--container-width));
  margin-inline: auto;
  padding-inline: 1rem;

  @media screen and (min-width: 1366px) {
    padding-inline: 0;
  }
}

.button,
.button.wc-backward,
.woocommerce:where(body:not(.woocommerce-block-theme-has-button-styles)) #respond input#submit,
.woocommerce:where(body:not(.woocommerce-block-theme-has-button-styles)) a.button,
.woocommerce:where(body:not(.woocommerce-block-theme-has-button-styles)) button.button,
.woocommerce:where(body:not(.woocommerce-block-theme-has-button-styles)) input.button,
:where(body:not(.woocommerce-block-theme-has-button-styles)):where(:not(.edit-post-visual-editor))
  .woocommerce
  #respond
  input#submit,
:where(body:not(.woocommerce-block-theme-has-button-styles)):where(:not(.edit-post-visual-editor))
  .woocommerce
  a.button,
:where(body:not(.woocommerce-block-theme-has-button-styles)):where(:not(.edit-post-visual-editor))
  .woocommerce
  button.button,
:where(body:not(.woocommerce-block-theme-has-button-styles)):where(:not(.edit-post-visual-editor))
  .woocommerce
  input.button {
  display: inline-flex;
  color: #fff;
  background-color: var(--primary-color);
  border: 1px solid var(--primary-color);
  border-radius: 0.25rem;
  padding: 1rem 1.125rem;
  font-size: 1rem;
  text-decoration: none;
  text-align: center;
  justify-content: center;
  transition: color 0.2s ease-in-out, background-color 0.2s ease-in-out;

  &:hover {
    background-color: #fff;
    color: var(--primary-color);
  }
}

/* Animated mask button */
.buttonn {
  position: relative;
  display: inline-flex;
  color: #fff;
  background-color: var(--primary-color);
  border: 1px solid var(--primary-color);
  border-radius: 0.25rem;
  padding: 1rem 1.125rem;
  font-size: 1rem;
  text-decoration: none;
  text-align: center;
  justify-content: center;
  overflow: hidden;
  cursor: pointer;

  span {
    position: relative;
    z-index: 1;
    color: var(--primary-color);
  }

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #fff;
    -webkit-mask: var(--mask-url);
    mask: var(--mask-url);
    -webkit-mask-size: 3000% 100%;
    mask-size: 3000% 100%;
    -webkit-animation: buttonMaskOut 0.7s steps(29) forwards;
    animation: buttonMaskOut 0.7s steps(29) forwards;
  }

  &:hover::before {
    -webkit-animation: buttonMaskIn 0.7s steps(29) forwards;
    animation: buttonMaskIn 0.7s steps(29) forwards;
  }

  &:hover span {
    color: #fff;
  }
}

@-webkit-keyframes buttonMaskIn {
  from {
    -webkit-mask-position: 0 0;
    mask-position: 0 0;
  }
  to {
    -webkit-mask-position: 100% 0;
    mask-position: 100% 0;
  }
}

@keyframes buttonMaskIn {
  from {
    -webkit-mask-position: 0 0;
    mask-position: 0 0;
  }
  to {
    -webkit-mask-position: 100% 0;
    mask-position: 100% 0;
  }
}

@-webkit-keyframes buttonMaskOut {
  from {
    -webkit-mask-position: 100% 0;
    mask-position: 100% 0;
  }
  to {
    -webkit-mask-position: 0 0;
    mask-position: 0 0;
  }
}

@keyframes buttonMaskOut {
  from {
    -webkit-mask-position: 100% 0;
    mask-position: 100% 0;
  }
  to {
    -webkit-mask-position: 0 0;
    mask-position: 0 0;
  }
}

.parallax {
  aspect-ratio: 4 / 3;
  overflow: hidden;

  img {
    position: absolute;
  }
}

.header {
  padding-top: 1.5rem;
  box-shadow: 0 10px 14px 0 rgba(0, 0, 0, 0.1);
  padding-bottom: 1.5rem;
  position: sticky;
  top: 0;
  z-index: 1000;
  background-color: #fff;
  transition: transform 0.3s ease;

  .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  &.header--hidden {
    transform: translateY(-100%);
  }
}

.mobile-menu-toggle > img {
  width: 24px;
  height: 24px;
  min-width: 24px;
}

.header-icons {
  display: flex;
  gap: 0.755rem;

  @media (width > 768px) {
    gap: 1.6rem;
  }

  svg {
    width: 1.25rem;
    height: 1.25rem;

    path {
      fill: var(--stroke);
      transition: fill 0.2s ease-in-out;
    }
  }

  a {
    position: relative;
    display: flex;
    align-items: center;

    &:hover svg path {
      fill: var(--primary-color);
    }

    span {
      position: absolute;
      width: 16px;
      height: 16px;
      background-color: var(--primary-color);
      color: #fff;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 50%;
      font-size: 0.5rem;
      z-index: 2;
      left: -50%;
      bottom: 0;
    }
  }
}

.menu-icons {
  img {
    width: 24px;
    height: 24px;
  }
}

.header-search {
  flex: 1;
  padding-inline: 2rem;
  display: none;

  @media (width < 768px) {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding-inline: 0;
    transform: translateY(100%);
  }

  @media (width > 878px) {
    padding-inline: 4rem;
  }
}

.header-search form {
  align-items: flex-start;

  input {
    padding: 0.5rem 1rem;
    width: 100%;
    border: 1px solid #bebebe;
    border-radius: 0.25rem;

    &:focus {
      border-color: var(--primary-color);
    }
  }

  button {
    width: 34px;
    height: 34px;
    display: flex;
    justify-content: flex-end;
    align-items: flex-start;
    background-color: transparent;
    border: 0;

    svg {
      width: 24px;
      stroke: #707070;
    }

    &:hover svg {
      stroke: var(--primary-color);
    }
  }
}

.header-contact {
  display: flex;
  flex-direction: column;
  display: none;

  @media a {
    font-weight: 700;
    text-decoration: none;
    font-size: 1.25rem;

    &:hover,
    &:focus {
      text-decoration: underline;
      color: var(--primary-color);
    }
  }

  span {
    font-size: 0.875rem;
    color: #bebebe;
    font-weight: 200;
  }
}

.site-description {
  font-size: 0.875rem;
  color: var(--grey-color);
}

.home-promo-section {
  margin-block: 5.25rem;
  border-radius: 0.5rem;
  overflow: hidden;
  padding: 3rem;
  position: relative;
  min-height: 388px;
  background-size: cover;
  background-position: center;

  .home-promo-section-price {
    color: #fff;
    background-color: var(--green-accent);
    top: 1.25rem;
    font-weight: bold;
    right: 1.25rem;
    font-size: 3rem;
    padding: 0.5rem 1.25rem;
    border-radius: 0.875rem;
    margin-bottom: 1rem;
    display: inline-block;

    @media (width > 762px) {
      position: absolute;
    }
  }

  .home-promo-section-title {
    color: #000;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 1.25rem;
  }

  .home-promo-section-text {
    margin-bottom: 2rem;

    @media (width > 768px) {
      margin-bottom: 7rem;
    }
  }

  .home-promo-section-text-buttons {
    width: 278px;
    max-width: 100%;
  }
}

.home-contact-section {
  margin-block: 5.25rem;
  padding: 5.25rem 3rem;
  border-radius: 0.5rem;
  overflow: hidden;
  background-size: cover;
  color: #fff;

  ::placeholder {
    color: #fff;
  }

  .home-contact-section__title {
    font-size: 2.5rem;
    margin-bottom: 2rem;
  }

  .home-contact-section__text {
    margin-bottom: 2rem;
  }

  textarea,
  input[type="text"],
  input[type="email"] {
    border: 1px solid #fff;
    border-radius: 0.25rem;
    background-color: rgba(249, 249, 249, 0.2);
    padding: 0.5rem 1.2rem;
    color: #fff;
    margin-bottom: 1rem;
    max-width: 100%;
    font-family: "Objectivity", sans-serif;
  }

  input[type="submit"] {
    display: flex;
    color: #fff;
    background-color: var(--primary-color);
    border: 1px solid var(--primary-color);
    border-radius: 0.25rem;
    padding: 1.125rem;
    text-decoration: none;
    text-align: center;
    justify-content: center;
  }
}

/* Tabs Styling */
.tabs-container {
  margin: 3rem 0;

  @media (width > 768px) {
    margin-bottom: 7rem;
  }
}

.tabs-header {
  display: flex;
  flex-direction: column;

  @media (width > 768px) {
    flex-direction: row;
  }
}

.tab-title {
  padding: 0.5rem 1rem;
  cursor: pointer;
  font-size: 1.25rem;
  font-weight: 500;
  transition: all 0.3s ease;
  border-bottom: 6px solid transparent;
  text-align: center;

  @media (width > 768px) {
    padding: 1.25rem 2rem;
    text-align: left;
  }
}

.tab-title:hover {
  color: var(--primary-color);
}

.tab-title.active {
  border-bottom-color: var(--primary-color);
}

.tab-content {
  opacity: 0;
  height: 0;
}

.tab-content.active {
  opacity: 1;
  height: auto;
}

.site-navigation {
  background-color: var(--primary-color);
  position: sticky;
  top: calc(var(--header-height, 80px)); /* Position below header by default */
  z-index: 999;
  transition: transform 0.3s ease, top 0.3s ease;

  &.nav--hidden {
    transform: translateY(-100%);
  }

  /* When header is hidden, nav should be at the top */
  .header--hidden + & {
    top: 0;
  }

  .header-menu {
    list-style: none;
    display: flex;
    gap: 0.5rem;

    @media (width > 1366px) {
      justify-content: center;
    }
  }

  > ul > li > a {
    font-size: 0.875rem;
    padding-inline: 1.25rem;
    text-decoration: none;
    color: #fff;
    display: flex;
    height: 4.375rem;
    align-items: center;
    font-weight: 400;
    justify-content: center;
    text-align: center;
    position: relative;
    font-family: "Girloy", sans-serif;

    &::before,
    &::after {
      content: "";
      display: block;
      position: absolute;
      left: 0;
      right: 0;
      height: 0;
      opacity: 0;
      background-color: #fff;
      transition: height 0.2s ease-in-out, opacity 0.2s ease-in-out;
    }

    &::before {
      top: 0;
    }

    &::after {
      bottom: 0;
    }

    &:hover::before,
    &:hover::after {
      height: 5px;
      opacity: 1;
    }
  }

  /* Mega Menu Styles */

  .mega-menu-item,
  .menu-item-has-children {
    position: relative; /* Needed for absolute positioning of submenu */
  }

  .mega-sub-menu,
  .menu-item-has-children > .sub-menu {
    display: none; /* Hide submenu by default */
    position: absolute;
    left: 0;
    top: 100%; /* Position below the parent item */
    right: 0;
    width: max-content;
    list-style: none;
    /* Full viewport width */
    background-color: #fff; /* Light background for the dropdown */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    z-index: 1000; /* Ensure it's above other content */
    border-top: 3px solid var(--primary-color);
  }

  .mega-menu-item:hover > .mega-sub-menu,
  .menu-item-has-children:hover > .sub-menu {
    display: block; /* Show on hover */
  }

  /* Remove hover effects from parent link when mega menu is open */

  .mega-menu-item:hover > a::before,
  .mega-menu-item:hover > a::after,
  .menu-item-has-children:hover > a::before,
  .menu-item-has-children:hover > a::after {
    height: 0;
    opacity: 0;
  }

  /* New Product Mega Menu Styles */

  .product-mega-menu .mega-menu-grid {
    display: flex;
    width: 100%;
    max-width: var(--container-width);
    margin: 0 auto;
    padding: 1rem;
  }

  .product-mega-menu .mega-menu-featured {
    width: 220px;
    padding-right: 2rem;
    border-right: 1px solid #eee;
    margin-right: 2rem;
  }

  .product-mega-menu .mega-menu-image {
    width: 100%;
    height: 160px;
    overflow: hidden;
    margin-bottom: 1rem;
    border-radius: 0.5rem;
    border: 1px solid #eee;
  }

  .product-mega-menu .mega-menu-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }

  .product-mega-menu .mega-menu-featured:hover .mega-menu-image img {
    transform: scale(1.05);
  }

  .product-mega-menu .mega-menu-featured h3 {
    font-size: 1.2rem;
    margin-bottom: 1rem;
    font-weight: 600;
    color: #333;
  }

  .product-mega-menu .mega-menu-view-all {
    display: inline-block;
    padding: 0.5rem 1rem;
    background-color: var(--primary-color);
    color: white;
    text-decoration: none;
    border-radius: 0.25rem;
    font-size: 0.9rem;
    transition: background-color 0.2s ease;
  }

  .product-mega-menu .mega-menu-view-all:hover {
    background-color: #0085c5;
  }

  .product-mega-menu .mega-menu-columns,
  .product-mega-menu .sub-menu > .mega-menu-grid {
    display: flex;
    flex: 1;
    gap: 2rem;
  }

  .product-mega-menu .mega-menu-column,
  .product-mega-menu .sub-menu > .mega-menu-grid > li {
    flex: 1;
  }

  .product-mega-menu .mega-menu-item-wrapper {
    margin-bottom: 1.5rem;
  }

  .product-mega-menu .mega-menu-item-link {
    display: block;
    font-weight: 600;
    color: #333;
    text-decoration: none;
    padding: 0.5rem 0;
    border-bottom: 1px solid #eee;
    transition: color 0.2s ease;
  }

  .product-mega-menu .mega-menu-item-link:hover {
    color: var(--primary-color);
  }

  .product-mega-menu .mega-menu-grandchildren,
  .product-mega-menu .sub-menu .sub-menu {
    list-style: none;
    padding: 0.5rem 0 0 1rem;
    margin: 0;
  }

  .product-mega-menu .mega-menu-grandchildren li,
  .product-mega-menu .sub-menu .sub-menu li {
    margin-bottom: 0.5rem;
  }

  .product-mega-menu .mega-menu-grandchildren a,
  .product-mega-menu .sub-menu .sub-menu a {
    color: #666;
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.2s ease;
    display: block;
    padding: 0.25rem 0;
  }

  .product-mega-menu .mega-menu-grandchildren a:hover,
  .product-mega-menu .sub-menu .sub-menu a:hover {
    color: var(--primary-color);
  }

  /* Mobile adjustments */
  @media (max-width: 992px) {
    .product-mega-menu .mega-menu-grid,
    .product-mega-menu .sub-menu > .mega-menu-grid {
      flex-direction: column;
    }

    .product-mega-menu .mega-menu-featured {
      width: 100%;
      padding-right: 0;
      border-right: none;
      margin-right: 0;
      margin-bottom: 2rem;
      padding-bottom: 1rem;
      border-bottom: 1px solid #eee;
    }

    .product-mega-menu .mega-menu-columns,
    .product-mega-menu .sub-menu > .mega-menu-grid {
      flex-direction: column;
    }

    /* Make submenus position relative on mobile for better layout */
    .product-mega-menu .mega-sub-menu,
    .product-mega-menu .menu-item-has-children > .sub-menu {
      position: relative;
      width: 100%;
      box-shadow: none;
      display: none; /* Hide by default, will be shown via JS */
    }

    /* Add open state for mobile menu items */
    .product-mega-menu .menu-open > .mega-sub-menu,
    .product-mega-menu .menu-open > .sub-menu {
      display: block;
    }
  }
}

.slick-slide {
  height: auto !important;
}

.galbud-promo-slider {
  position: relative;

  .slick-dotted.slick-slider {
    margin-bottom: 0;
  }

  .slick-arrow {
    position: absolute;
    bottom: 0;
    border: 0;
    background-color: transparent;
    display: flex;
    transition: opacity 0.2s ease-in-out;
    z-index: 5;

    &.slick-disabled {
      opacity: 0.5;
    }

    &.galbud-promo-slider__arrow-next {
      left: 30px;
    }
  }

  .slick-track {
    padding-block: 2rem;

    &::before {
      display: none;
    }
  }

  .slick-dots {
    position: absolute;
    bottom: 5px;
    left: 5rem;
    display: flex;
    gap: 0.5rem;
  }

  .slick-dots li {
    width: 10px;
    height: 10px;
    display: flex;
    margin: 0;
  }

  .slick-dots li button {
    width: 10px;
    height: 10px;
    background-color: var(--grey-color);
    border: 0;
    border-radius: 2px;
  }

  .slick-dots li:not(.slick-active) button:hover {
    border-radius: 50%;
  }

  .slick-dots li.slick-active button {
    background-color: var(--primary-color);
  }

  .slick-dots li button::before {
    display: none;
  }

  .galbud-promo-slider__item {
    border-radius: 0.75rem;
    position: relative;
    border: 1px solid var(--grey-color);
    transition: transform 0.2s ease-in-out;
    margin-inline: 1rem;

    &:hover {
      transform: translateY(-0.5rem);
    }
  }

  .galbud-promo-slider__item-link {
    display: flex;
    border-radius: 0.75rem 0.75rem 0 0;
    overflow: hidden;
    height: 280px;
    margin-inline: auto;
    justify-content: center;
  }

  .galbud-promo-slider__item-content {
    padding: 1.25rem;
  }

  .galbud-promo-slider__item-title {
    font-weight: bold;
    line-height: 150%;
    font-size: 1rem;
    display: block;
    letter-spacing: -1px;
    margin-bottom: 0.75rem;
  }

  .galbud-promo-slider__item-sku {
    display: block;
    font-size: 0.875rem;
    color: var(--grey-color);
    margin-bottom: 1rem;
  }

  .galbud-promo-slider__item-price {
    margin-bottom: 1.25rem;
    font-size: 1rem;

    span.price-label {
      font-size: 0.9rem;
      margin-right: 5px;
    }

    .price-with-tax,
    .price-without-tax {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin: 5px 0;
    }

    .price-with-tax {
      color: #333;
      font-weight: bold;
    }

    .price-without-tax {
      color: #666;
    }

    del {
      color: #999;
      margin-right: 5px;
      font-size: 0.85rem;
    }

    ins {
      text-decoration: none;
      font-weight: bold;
    }
  }

  .galbud-promo-slider__badges {
    position: absolute;
    left: 1.25rem;
    top: 0;
  }
}

.galbud-promo-slider-badges {
  list-style: none;
  display: flex;
  gap: 0.75rem;
  transform: translateY(-50%);

  .galbud-promo-slider-badges__item {
    border-radius: 0.5rem;
    font-weight: bold;
    color: #fff;
    font-size: 0.75rem;
    padding: 0.5rem 0.75rem;
  }

  .galbud-promo-slider-badges__item--featured {
    background-color: #000;
  }

  .galbud-promo-slider-badges__item--promo {
    background-color: var(--green-accent);
  }
}

.home-showcase-products {
  margin-bottom: 7rem;
  display: flex;
  flex-direction: column;
  gap: 2rem;

  @media (width > 768px) {
    flex-direction: row;
  }

  .category-name {
    padding: 0.75rem;
    background-color: var(--black);
    color: var(--white);
    border-radius: 0.25rem;
    position: absolute;
    top: 0;
    left: 1.25rem;
    display: inline-flex;
    transform: translateY(-50%);
    font-size: 0.75rem;
    font-weight: 700;
  }

  * {
    position: relative;
    z-index: 1;
  }

  .home-showcase-products__item {
    flex: 1;
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.1);
    padding: 3.5rem 0 1.25rem 0;
    position: relative;

    > div {
      overflow: hidden;
      padding-inline: 1.25rem;
    }
  }

  .home-showcase-products__image {
    position: absolute;
    z-index: 0;
    top: 0;
    bottom: 0;
    height: 100%;
    transform: translateX(60%);

    @media (width > 768px) {
      transform: translateX(20%);
    }

    @media (width > 1366px) {
      transform: translateX(60%);
    }
  }

  .home-showcase-products__title {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 1.25rem;
  }

  .home-showcase-products__description {
    max-width: 50%;
    font-size: 1.25rem;
    font-weight: 200;
    margin-bottom: 1.25rem;
  }

  .home-showcase-products__price {
    font-size: 1.5rem;
    margin-bottom: 140px;
    display: block;

    span {
      font-size: 2rem;
      font-weight: 500;
      color: var(--green-accent);
    }
  }

  .home-showcase-products__price-container {
    margin-bottom: 140px;

    .product-listing-price {
      margin-bottom: 0;
      padding: 10px;
      background-color: #f9f9f9;
      border-radius: 5px;
      border-left: 3px solid #01a0e2;
    }
  }

  .home-showcase-products__footer {
    width: 278px;
    max-width: 100%;
  }
}

.section-title {
  margin-block: 1.5rem;

  .section-title__container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--grey-color);

    @media (width > 768px) {
      flex-direction: row;
      margin-bottom: 2.4rem;
    }

    @media (width > 1366px) {
      gap: 8rem;
    }
  }

  .section-title__text {
    font-size: 1.5rem;

    @media (width > 768px) {
      font-size: 2.5rem;
    }
  }

  .section-title__subtitle {
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.25rem;
    color: #707070;
  }
}

.promo-categories-slider {
  .promo-categories-slider__item {
    box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.1);
    border-radius: 1rem;
    border: 1px solid var(--stroke);
    margin-inline: 1rem;
    transition: transform 0.3s ease, box-shadow 0.3s ease, border-color 0.3s ease;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
      border-color: var(--primary-color);
    }
  }

  .promo-categories-slider__title {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
  }

  .promo-categories-slider__link {
    border-radius: 1rem 1rem 0 0;
    display: block;
    overflow: hidden;
    aspect-ratio: 1 / 1;

    img {
      width: min(100%, 100%);
      width: 100%;
      height: 100%;
      object-fit: cover;
      display: block;
      object-position: center;
      transition: transform 0.3s ease;
    }
  }

  .promo-categories-slider__item:hover .promo-categories-slider__link img {
    transform: scale(1.05);
  }

  .promo-categories-slider__content {
    padding: 1.75rem 1.25rem;
  }

  .promo-categories-slider__button {
    padding: 0.75rem 0.5rem;
    border: 1px solid var(--primary-color);
    text-decoration: none;
    border-radius: 0.25rem;
    display: inline-block;
    font-size: 0.75rem;
    color: var(--primary-color);
    transition: background-color 0.3s ease, color 0.3s ease;

    &:hover {
      background-color: var(--primary-color);
      color: white;
    }
  }
}

.footer {
  border-top: 5px solid var(--primary-color);
  padding-top: 5rem;
  padding-bottom: 2rem;

  .footer__sections {
    display: flex;
    justify-content: flex-end;
  }
}

.main-slider {
  margin-bottom: 2rem;

  @media (width > 768px) {
    margin-bottom: 3rem;
  }

  @media (width > 1366px) {
    margin-bottom: 8rem;
  }

  .main-slider__row {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 0.5rem;
    @media (width > 568px) {
      flex-direction: row;
    }
  }

  .main-slider__col:nth-child(1) {
    flex: 40%;
    display: flex;
    align-items: center;
  }

  .main-slider__col:nth-child(2) {
    flex: 60%;
  }

  .main-slider__title {
    margin-top: 1rem;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 1rem;

    @media (width > 1366px) {
      font-size: 4rem;
    }
  }

  .main-slider__description {
    width: min(100%, 380px);
    color: var(--second-font-color);
    margin-bottom: 1rem;

    @media (width > 1366px) {
      margin-bottom: 2rem;
    }
  }

  .main-slider__subtitle {
    font-size: 1.5rem;
    font-weight: 500;
    margin-bottom: 1rem;

    @media (width > 1366px) {
      margin-bottom: 2rem;
    }
  }

  .main-slider__main-image {
    display: block;
    width: 100%;
    height: 100%;
    max-width: 100%;
    max-height: 613px;
    object-fit: cover;

    @media (width > 568px) {
      border-bottom-left-radius: 5rem;
    }
  }

  button {
    margin-bottom: 1rem;
    padding-inline: 2rem;

    @media (width > 768px) {
      padding-inline: 3rem;
    }
  }
}

.notification-area {
  position: relative;

  .notification-area__alert {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    transform: translateY(100%);
    align-items: center;
    background-color: var(--alert-color);
    display: flex;
    justify-content: space-between;
    padding: 1rem 0.75rem 1rem 2.5rem;
    border-radius: 0 0 0.5rem 0.5rem;
    font-size: 0.75rem;
    font-weight: 400;
  }

  .notification-area__close-button {
    display: flex;
    justify-content: center;
    align-items: center;
    background: transparent;
    border: 0;
  }
}

.home-new-slider {
  margin-bottom: 3rem;

  @media (width > 768px) {
    margin-bottom: 7rem;
  }
}

.stars-list {
  display: flex;
  list-style: none;
  gap: 0.25rem;

  .inactive {
    filter: grayscale(100%);
  }
}

.testimonials {
  .testimonials-item {
    width: 840px;
    max-width: 100%;
    margin-inline: auto;
    padding-block: 1.5rem;

    &:not(:last-of-type) {
      border-bottom: 1px solid var(--grey-color);
    }

    @media (width > 768px) {
      display: flex;
      gap: 2rem;
    }
  }

  .testimonials-image {
    border-radius: 50%;
    width: 82px;
    height: 82px;
  }

  h4 {
    font-size: 1.25rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
  }

  span {
    color: var(--grey-color);
    font-size: 1rem;
    font-weight: 400;
    display: block;
    margin-bottom: 0.5rem;
  }

  .verified-purchase {
    display: inline-block;
    background-color: var(--green-accent);
    color: white;
    font-size: 0.75rem;
    font-weight: 700;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    margin-left: 0.5rem;
    margin-top: 0.5rem;
  }
}

/* WooCommerce Reviews Form Styling */
#review_form_wrapper {
  margin-top: 2rem;
  padding: 1.5rem;
  background-color: #f9f9f9;
  border-radius: 0.5rem;
}

#review_form .comment-form-rating {
  margin-bottom: 1rem;
}

/* Custom WooCommerce Notification */
.custom-wc-notification {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background-color: #4caf50; /* Green background */
  color: white;
  padding: 15px 25px;
  border-radius: 5px;
  z-index: 10000; /* Ensure it's on top */
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  font-size: 1rem;
  opacity: 1;
  transition: opacity 0.3s ease-in-out;
}

.custom-wc-notification:empty {
  display: none; /* Hide if empty */
}

#review_form .comment-form-rating label {
  display: block;
  margin-bottom: 0.5rem;
}

#review_form p.stars a {
  color: var(--primary-color);
}

#review_form .comment-form-comment label {
  display: block;
  margin-bottom: 0.5rem;
}

#review_form textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--stroke);
  border-radius: 0.25rem;
}

#review_form .form-submit input[type="submit"] {
  background-color: var(--primary-color);
  color: white;
  border: 1px solid var(--primary-color);
  padding: 0.75rem 1.5rem;
  border-radius: 0.25rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

#review_form .form-submit input[type="submit"]:hover {
  background-color: white;
  color: var(--primary-color);
}

.woocommerce-noreviews {
  text-align: center;
  padding: 2rem;
  background-color: #f9f9f9;
  border-radius: 0.5rem;
  margin-bottom: 2rem;
}

.specyfication-table {
  width: 60%;

  tr:nth-child(odd) {
    background-color: #f9f9f9;
  }

  td {
    padding: 1rem;
  }
}

.single-product-wide-image {
  margin-block: 4rem;
}

.two-columns {
  @media (width > 768px) {
    display: flex;
    gap: 2rem;
  }

  p {
    margin-bottom: 1rem;
  }

  ul {
    padding-left: 1rem;
  }

  > div {
    flex: 1;
  }
}

.product-details {
  display: flex;
  gap: 3rem;

  strong {
    display: block;
    font-weight: bold;
    margin-bottom: 0.5rem;
  }

  .sizes {
    white-space: nowrap;
    min-width: 275px;

    span {
      min-width: 80px;
      display: inline-block;
    }
  }
}

.woocommerce ul.products {
  li.product {
    background-color: #fff;
    border: 1px solid var(--stroke);
    border-radius: 0.5rem;
    padding: 1.25rem;
  }
}

.woocommerce ul.products li.product,
.woocommerce-page ul.products li.product {
  background-color: #fff;
  border: 1px solid var(--stroke);
  border-radius: 1rem;

  .galbud-promo-slider__item-title {
    font-size: 1rem;
    font-weight: 800;
    line-height: 1.5rem;
    margin-bottom: 1.125rem;
  }

  .galbud-promo-slider__item-content {
    padding: 1.5rem;
  }

  .galbud-promo-slider__badges {
    position: absolute;
  }

  .galbud-promo-slider__item-sku {
    font-size: 0.875rem;
    margin-bottom: 1rem;
    display: block;
  }

  .galbud-promo-slider__item-price {
    font-size: 1.5rem;
  }

  .galbud-promo-slider__item-price span {
    font-weight: bold;
  }

  .button__primary {
    background-color: var(--primary-color);
  }
}

.products.columns-4 {
  display: grid;

  gap: 2rem;

  @media (width > 768px) {
    grid-template-columns: repeat(4, 1fr);
  }

  &::before {
    display: none !important;
  }
}

.product-listing-item {
  position: relative;
  border: 1px solid var(--stroke);
  border-radius: 0.75rem;
  background-color: #fff;
  transition: all 0.2s ease-in-out;

  &:hover {
    transform: translateY(-0.5rem);
    border-color: var(--primary-color);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  }

  .thumbnail-link {
    display: block;
    border-radius: 0.75rem 0.75rem 0 0;
    overflow: hidden;
  }

  img {
    width: 100%;
    height: auto;
    object-fit: cover;
  }

  .product-content-wrapper {
    padding: 1.25rem;
  }

  .name {
    font-size: 1rem;
    font-weight: bold;
    margin-bottom: 0.75rem;
    min-height: 2rem;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .sku {
    font-size: 0.875rem;
    color: var(--grey-color);
    margin-bottom: 1rem;
    display: block;
  }

  .price {
    display: block;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--black) !important;
  }

  .button {
    padding: 0.75rem 0.5rem;
    border: 1px solid var(--primary-color);
    text-decoration: none;
    border-radius: 0.25rem;
    display: inline-block;
    font-size: 0.75rem;
  }
}

.single-produt-header {
  @media (width > 768px) {
    display: grid;
    grid-template-columns: 565px 1fr;
    gap: 2rem;
  }

  .single-product-header-image {
    border-top: 0;
    overflow: hidden;
  }
}

.badges {
  display: flex;
  gap: 0.75rem;

  .badge {
    color: var(--white);
    font-size: 0.75rem;
    font-weight: 700;
    background-color: #dedede;
    display: inline-block;
    padding: 0.5rem;
    border-radius: 0.25rem;
  }

  .badge-featured {
    background-color: var(--black);
  }

  .badge-promo {
    background-color: var(--green-accent);
  }
}

.product-stats {
  list-style: none;
  padding: 0;
  display: flex;
  gap: 1.2rem;
  margin-bottom: 2rem;

  span {
    color: #707070;
    font-size: 0.875rem;
  }
}

.single-product-header-description {
  padding-top: 2.5rem;

  .badges {
    margin-bottom: 1rem;
  }

  h1 {
    font-size: 3rem;
    font-weight: 500;
    margin-bottom: 1.25rem;
  }
}

.galbud-button,
.button.alt,
.woocommerce:where(body:not(.woocommerce-block-theme-has-button-styles)) #respond input#submit.alt,
.woocommerce:where(body:not(.woocommerce-block-theme-has-button-styles)) a.button.alt,
.woocommerce:where(body:not(.woocommerce-block-theme-has-button-styles)) button.button.alt,
.woocommerce:where(body:not(.woocommerce-block-theme-has-button-styles)) input.button.alt,
:where(body:not(.woocommerce-block-theme-has-button-styles)):where(:not(.edit-post-visual-editor))
  .woocommerce
  #respond
  input#submit.alt,
:where(body:not(.woocommerce-block-theme-has-button-styles)):where(:not(.edit-post-visual-editor))
  .woocommerce
  a.button.alt,
:where(body:not(.woocommerce-block-theme-has-button-styles)):where(:not(.edit-post-visual-editor))
  .woocommerce
  button.button.alt,
:where(body:not(.woocommerce-block-theme-has-button-styles)):where(:not(.edit-post-visual-editor))
  .woocommerce
  input.button.alt {
  padding: 0.75rem 0.5rem;
  border: 1px solid var(--primary-color);
  text-decoration: none;
  border-radius: 0.25rem;
  font-weight: 700;
  text-align: center;
  display: block;
  font-size: 1rem;
}

.galbud-button--primary,
.button.alt,
.woocommerce:where(body:not(.woocommerce-block-theme-has-button-styles)) #respond input#submit.alt,
.woocommerce:where(body:not(.woocommerce-block-theme-has-button-styles)) a.button.alt,
.woocommerce:where(body:not(.woocommerce-block-theme-has-button-styles)) button.button.alt,
.woocommerce:where(body:not(.woocommerce-block-theme-has-button-styles)) input.button.alt,
:where(body:not(.woocommerce-block-theme-has-button-styles)):where(:not(.edit-post-visual-editor))
  .woocommerce
  #respond
  input#submit.alt,
:where(body:not(.woocommerce-block-theme-has-button-styles)):where(:not(.edit-post-visual-editor))
  .woocommerce
  a.button.alt,
:where(body:not(.woocommerce-block-theme-has-button-styles)):where(:not(.edit-post-visual-editor))
  .woocommerce
  button.button.alt,
:where(body:not(.woocommerce-block-theme-has-button-styles)):where(:not(.edit-post-visual-editor))
  .woocommerce
  input.button.alt {
  background-color: var(--primary-color);
  color: var(--white);
  transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out;

  &:hover {
    background-color: var(--white);
    color: var(--primary-color);
  }
}

.single-product-header-description {
  margin-bottom: 2rem;
}

.single-product {
  .cart {
    display: flex;
    justify-content: flex-start;
    gap: 1.2rem;

    input[type="number"] {
      height: 100%;
      padding-inline: 1rem;
      border-radius: 0.25rem;
      border: 1px solid var(--stroke);
      width: 5rem;
      text-align: center;
    }

    button[type="submit"] {
      padding-inline: 2.5rem;
    }
  }

  .product-unavailable-message {
    display: inline-block;
    padding: 1rem 1.5rem;
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
    border-radius: 0.25rem;
    font-weight: 500;
    margin-bottom: 1rem;
  }
}

.single-product-header-price {
  font-size: 1.5rem;
  font-weight: 400;
  margin-bottom: 1.25rem;
}

.cart-page {
  h1 {
    font-size: 2rem;
    font-weight: 500;
    margin-bottom: 2.5rem;
  }

  table.shop_table {
    border: 0;
  }

  .woocommerce-cart-wrapper {
    @media (width > 768px) {
      display: grid;
      grid-template-columns: 1fr 325px;
      gap: 2rem;
    }

    .qty {
      height: 1.5rem;
      border-radius: 0.25rem;
      border: 1px solid var(--stroke);
    }

    .cart-collaterals {
      padding: 1rem;
      background: #ffffff;
      box-shadow: 0 4px 24px rgba(0, 0, 0, 0.1);
      border-radius: 10px;

      &::before,
      &::after {
        display: none;
      }

      h2 {
        font-size: 1.5rem;
        font-weight: 500;
        margin-bottom: 1.25rem;
      }

      .cart_totals {
        float: none;
        width: 100%;
      }

      /* Coupon form styling */

      .show-coupon-form {
        width: 100%;
        background-color: transparent;
        border: 0;
        text-align: left;
      }

      .show-coupon-form:hover {
        color: var(--primary-color);
      }

      .woocommerce-form-coupon {
        padding: 1rem;
        background-color: #f9f9f9;
        border-radius: 0.25rem;
        margin-bottom: 1rem;

        .form-row {
          padding: 0;
          margin: 0 0 0.75rem 0;
        }

        .form-row-first,
        .form-row-last {
          width: 100%;
          float: none;
        }

        input[type="text"] {
          width: 100%;
          padding: 0.5rem;
          border: 1px solid var(--stroke);
          border-radius: 0.25rem;
        }

        button {
          width: 100%;
          padding: 0.5rem;
          background-color: var(--primary-color);
          color: white;
          border: 1px solid var(--primary-color);
          border-radius: 0.25rem;
          cursor: pointer;
          transition: all 0.2s ease;
        }

        button:hover {
          background-color: white;
          color: var(--primary-color);
        }
      }
    }
  }
}

.woocommerce form .form-row .input-text,
.woocommerce form .form-row select {
  padding: 0.75rem 0.5rem;
  font-size: 1rem;
  border-radius: 0.25rem;
  border: 1px solid var(--stroke);
  color: #707070;
}

#order_review_heading {
  margin-top: 2rem;
  margin-bottom: 1rem;
}

.register-landing-page {
  padding-top: 2.5rem;

  h1 {
    font-size: 2.5rem;
    font-weight: 400;
    margin-bottom: 4rem;
  }

  .register-boxes {
    @media (width > 768px) {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 2rem;
    }

    margin-bottom: 3rem;
  }

  .register-box {
    position: relative;
    height: 214px;
    border-radius: 0.5rem;
    overflow: hidden;
    display: flex;
    align-items: center;
    padding: 1.25rem;
    text-decoration: none;
    background-size: cover;
    background-position: center;

    &:hover h2 {
      letter-spacing: 3px;
    }

    h2 {
      font-size: 2rem;
      color: var(--white);
      font-weight: 800;
      transition: letter-spacing 0.2s ease-in-out;
    }
  }
}

.news-page {
  padding-block: 4rem;

  h1 {
    font-size: 2.5rem;
    font-weight: 500;
    margin-bottom: 2rem;
  }
}

.news-list {
  @media (width > 768px) {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 3rem;
  }

  article {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    padding: 1rem;

    .date {
      font-weight: 600;
      font-size: 0.875rem;
      color: var(--grey-color);
      margin-bottom: 1.25rem;
    }

    img {
      border-radius: 0.5rem;
      overflow: hidden;
      max-width: 100%;
      height: auto;
      margin-bottom: 1.25rem;
      transition: transform 0.3s ease-in-out; /* Dodanie płynnego przejścia dla obrazka */
    }

    h2 {
      font-size: 1.5rem;
      font-weight: 500;
      color: #000;
      margin-bottom: 1.25rem;
    }

    p {
      margin-bottom: 1.5rem;
    }

    .news-button {
      display: inline-block;
      padding: 0.5rem 0.75rem;
      border: 1px solid var(--primary-color);
      color: #707070;
      border-radius: 0.25rem;
      text-decoration: none;
      transition: color 0.2s ease-in-out;

      &:hover {
        color: var(--primary-color);
      }
    }
  }

  article {
    transition: transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out; /* Płynne przejście */
  }

  article:hover {
    transform: translateY(-5px); /* Lekkie uniesienie */
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1); /* Subtelny cień */
  }
}

.news-list article:hover img {
  transform: scale(1.05); /* Lekkie powiększenie obrazka */
}

p:empty {
  display: none;
}

.single-post {
  img {
    max-width: 100%;
  }

  h2,
  h3 {
    margin-block: 2rem;
  }

  .container {
    p {
      margin-bottom: 1rem;
    }

    ul,
    ol {
      margin-block: 1rem;

      padding-left: 2rem;
    }
  }
}

.woocommerce-order {
  .container {
    position: relative;
    padding-top: 5rem;
  }

  .woocommerce-order-image {
    position: absolute;
    top: 0;
    right: 0;
    max-width: 100%;
  }

  .order-box {
    width: 808px;
    max-width: 100%;
    background-color: #fff;
    border: 1px solid var(--stroke);
    border-radius: 0.5rem;
    padding: 2.5rem 1.25rem;
    z-index: 1;
    position: relative;
    margin-bottom: 2rem;
  }

  h2 {
    margin-bottom: 3rem;
  }

  h3 {
    font-size: 2.5rem;
  }

  .current-status {
    display: flex;
    gap: 1.2rem;
    align-items: center;
  }
}

.order-statuses {
  list-style: none;
  position: relative;
  padding-block: 1.5rem;
  margin-top: 1.5rem;
  border-top: 1px solid var(--stroke);
  padding-left: 3.75rem;

  li {
    position: relative;
    padding-left: 2rem;

    &:not(:last-child) {
      min-height: 103px;

      &::after {
        content: "";
        display: block;
        position: absolute;
        left: 7px;
        z-index: 0;
        top: 1.5rem;
        bottom: 0.5rem;
        width: 1px;
        background-color: var(--stroke);
      }
    }

    &::before {
      content: "";
      display: block;
      width: 14px;
      z-index: 1;
      height: 14px;
      border: 1px solid var(--stroke);
      border-radius: 0.25rem;
      background-color: var(--white);
      position: absolute;
      left: 0;
      top: 1px;
    }
  }

  .current {
    &::before {
      border-color: var(--primary-color);
      background-color: var(--primary-color);
    }

    p {
      font-weight: 500;
    }
  }
}

.order-map {
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.woocommerce-error {
  margin-bottom: 0;
}

.container.cart,
.container.my-account {
  padding-block: 4rem;
}

.container.my-account {
  padding-top: 0;
}

.contact-page {
  p {
    margin-bottom: 1rem;
  }

  input[type="text"],
  input[type="email"],
  textarea {
    padding: 0.5rem 0.75rem;
    border-radius: 0.25rem;
    border: 1px solid var(--stroke);
  }

  input[type="submit"] {
    padding: 1rem 1.5rem;
    border: 1px solid var(--primary-color);
    text-decoration: none;
    border-radius: 0.25rem;
    font-weight: 700;
    text-align: center;
    display: block;
    font-size: 1rem;
    background-color: var(--primary-color);
    color: var(--white);
    transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out;

    &:hover {
      background-color: var(--white);
      color: var(--primary-color);
    }
  }
}

.contact-columns {
  display: flex;
  flex-wrap: wrap;
  gap: 3.75rem;
  margin-bottom: 3.5rem;

  .contact-column {
    flex: 1;
  }

  h2 {
    font-size: 1.5rem;
    font-weight: 500;
    margin-bottom: 3.5rem;
  }

  ul {
    list-style: none;
    line-height: 1rem;

    li {
      margin-bottom: 0.625rem;

      a {
        text-decoration: none;

        &:hover,
        &:focus {
          text-decoration: underline;
        }
      }
    }
  }
}

.listing {
  @media (width > 768px) {
    display: grid;
    grid-template-columns: 275px 1fr;
    gap: 2rem;
  }
}

.widget-area {
  padding-bottom: 60px;

  .widget_block {
    margin-bottom: 2rem;
  }
}

.wc-block-product-categories-list {
  list-style: none;

  li {
    margin-bottom: 0.5rem;
  }
}

/* Import WooCommerce Sidebar Styles */

.single-product-header-price {
  margin: 20px 0;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 5px;
  border-left: 3px solid #01a0e2;
}

.price-with-tax,
.price-without-tax {
  margin: 5px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 300px;
}

.price-with-tax {
  color: #333;
}

.price-without-tax {
  color: #666;
}

.price-label {
  margin-right: 10px;
}

@media (max-width: 768px) {
  .price-with-tax,
  .price-without-tax {
    flex-direction: column;
    align-items: flex-start;
  }

  .price-label {
    margin-bottom: 5px;
  }
}

.rating-count {
  font-size: 1rem;
}

/* Product Gallery Styles */
.product-gallery {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.product-gallery-main {
  width: 100%;
  border: 1px solid var(--stroke);
  border-top: 0;
  border-radius: 0 0 0.5rem 0.5rem;
  overflow: hidden;
}

.product-gallery-main-image {
  width: 100%;
  position: relative;
  overflow: hidden;
}

.product-gallery-main-image img {
  width: 100%;
  height: auto;
  display: block;
  transition: transform 0.3s ease;
}

/* Zoom effect */
.zoom-container {
  cursor: zoom-in;
}

.zoom-container.zoomed {
  cursor: move;
}

.zoom-container.zoomed img {
  transform: scale(1.5);
}

/* Thumbnails slider */
.product-gallery-thumbs {
  display: flex;
  justify-content: center;
  flex-wrap: nowrap;
  overflow-x: auto;
  gap: 0.5rem;
  padding: 0.5rem 0;
  scrollbar-width: thin;
  scrollbar-color: var(--primary-color) var(--stroke);
}

.product-gallery-thumbs::-webkit-scrollbar {
  height: 6px;
}

.product-gallery-thumbs::-webkit-scrollbar-track {
  background: var(--stroke);
  border-radius: 3px;
}

.product-gallery-thumbs::-webkit-scrollbar-thumb {
  background-color: var(--primary-color);
  border-radius: 3px;
}

.product-gallery-thumb-item {
  flex: 0 0 auto;
  width: 80px;
  height: 80px;
  border: 1px solid var(--stroke);
  border-radius: 0.25rem;
  overflow: hidden;
  cursor: pointer;
  opacity: 0.7;
  transition: opacity 0.3s ease, border-color 0.3s ease;
}

.product-gallery-thumb-item:hover {
  opacity: 0.9;
}

.product-gallery-thumb-item.active {
  opacity: 1;
  border-color: var(--primary-color);
}

.product-gallery-thumb-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Responsive styles */
@media (min-width: 768px) {
  .product-gallery-thumbs {
    max-width: 100%;
  }
}

/* Slick slider styles for thumbnails when enabled */
.product-gallery-thumbs.slick-initialized {
  overflow: visible;
}

.product-gallery-thumbs .slick-track {
  display: flex;
  gap: 0.5rem;
}

.product-gallery-thumbs .slick-slide {
  margin: 0 5px;
}

.product-gallery-thumbs .slick-arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1;
  width: 30px;
  height: 30px;
  background-color: rgba(255, 255, 255, 0.8);
  border: 1px solid var(--stroke);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.product-gallery-thumbs .slick-arrow:hover {
  background-color: var(--primary-color);
  color: white;
}

.product-gallery-thumbs .slick-prev {
  left: -15px;
}

.product-gallery-thumbs .slick-next {
  right: -15px;
}

.mobile-menu-container {
  position: absolute;
  background-color: var(--white);
  right: 0;
  top: 0;
  padding: 1rem;
  z-index: 3;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  display: none;
  opacity: 0;
  transition: opacity 0.3s ease-in-out;

  .mobile-menu {
    padding: 0;
  }

  &.active {
    opacity: 1;
  }

  > button {
    position: absolute;
    width: 24px;
    border: 0;
    background-color: transparent;
    height: 24px;
    right: 0;
    top: 0;
    display: flex;
    justify-content: center;
    align-items: center;

    svg {
      width: 18px;
    }
  }

  ul {
    list-style: none;
    width: fit-content;
  }

  li {
    padding: 1rem;
    white-space: nowrap;
  }

  li:not(:last-of-type) {
    border-bottom: 1px solid var(--primary-color);
  }

  a {
    text-decoration: none;
    display: block;
    text-align: right;
    font-size: 1.25rem;

    &:hover {
      color: var(--primary-color);
    }
  }
}

/* Page Kategorie Styles */
.page-kategorie-container {
  padding-block: 4rem;
}

.page-kategorie-container h1 {
  font-size: 2.5rem;
  font-weight: 500;
  text-align: left;
}

.category-controls {
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--stroke);
  display: flex;
  justify-content: space-between;

  align-items: center;
}

.category-sorting {
  display: none;
  @media (width > 768px) {
    display: block;
  }
}

.category-sorting span {
  margin-right: 0.5rem;
  color: var(--second-font-color);
}

.category-sorting a {
  text-decoration: none;
  color: var(--primary-color);
  margin: 0 0.25rem;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s ease, color 0.2s ease;
}

.category-sorting a:hover {
  background-color: var(--primary-color);
  color: var(--white);
}

.category-sorting a.active {
  background-color: var(--primary-color);
  color: var(--white);
  font-weight: bold;
}

.product-category-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.category-item {
  border: 1px solid var(--stroke);
  border-radius: 0.5rem;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease, border-color 0.3s ease;
  background-color: var(--white);
}

.category-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  border-color: var(--primary-color);
}

.category-item-link {
  display: block;
  text-decoration: none;
  color: inherit;
}

.category-image-wrapper {
  height: 180px;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f9f9f9; /* Light background for images */
}

.category-image {
  width: 100%;
  height: 100%;
  object-fit: cover; /* Changed to cover to fill the space */
  transition: transform 0.3s ease;
}

.category-item:hover .category-image {
  transform: scale(1.05); /* Slight zoom on hover */
}

.category-name {
  font-size: 1.1rem;
  font-weight: 500;
  padding: 0.75rem 1rem 0.25rem;
  text-align: center;
  min-height: 3em; /* Ensure consistent height */
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.category-count {
  display: block;
  font-size: 0.85rem;
  color: var(--second-font-color);
  text-align: center;
  padding: 0 1rem 0.75rem;
}

/* Pagination Styles */
.pagination {
  text-align: center;
  margin-top: 2rem;
}

.pagination .page-numbers {
  display: inline-block;
  padding: 0.5rem 1rem;
  margin: 0 0.25rem;
  border: 1px solid var(--stroke);
  border-radius: 0.25rem;
  text-decoration: none;
  color: var(--primary-color);
  transition: background-color 0.2s ease, color 0.2s ease;
}

.pagination .page-numbers:hover,
.pagination .page-numbers.current {
  background-color: var(--primary-color);
  color: var(--white);
  border-color: var(--primary-color);
}

.pagination .page-numbers.dots {
  border: none;
  background: none;
  color: var(--second-font-color);
}

/* Related Products Section on Cart Page */
.related-products-section {
  margin-top: 4rem; /* Add space above the section */
  padding-top: 2rem; /* Add some padding inside */
  border-top: 1px solid var(--stroke); /* Separator line */
}

/* Style the heading within the related products section */
.related-products-section .related.products > h2 {
  font-size: 1.8rem; /* Adjust heading size */
  font-weight: 500;
  margin-bottom: 2rem; /* Space below heading */
  text-align: center; /* Center the heading */
}

/* Related products styling */
.related-products .products-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2rem;
  margin-bottom: 3rem;
}

@media (max-width: 1200px) {
  .related-products .products-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 768px) {
  .related-products .products-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .related-products .products-grid {
    grid-template-columns: 1fr;
  }
}

/* Ensure the ::before pseudo-element doesn't interfere */
.related-products-section .related.products ul.products::before {
  display: none !important;
}

.woocommerce ul.products.columns-2 li.product,
.woocommerce-page ul.products.columns-2 li.product {
  width: 100%;
}

.woocommerce-cart-form {
  img.wp-post-image {
    border: 1px solid var(--stroke);
  }

  @media screen and (min-width: 768px) {
    img.wp-post-image {
      width: 120px !important;
      height: auto !important;
    }
  }
}

.woocommerce-MyAccount-navigation,
.woocommerce-MyAccount-content {
  margin-top: 4rem;
}

.woocommerce-MyAccount-navigation {
  ul {
    list-style: none;
  }
}

.woocommerce img,
.woocommerce-page img {
  min-width: 60px;
}

/* Footer Widget Styles */
.footer__widgets {
  display: flex;
  flex-wrap: wrap;
  gap: 2rem; /* Adjust gap between columns */
  margin-top: 2rem; /* Space between logo and widgets */
}

.footer__column {
  flex: 1 1 calc(16.666% - 2rem); /* Base for 6 columns, adjusting for gap */
  min-width: 150px; /* Minimum width for smaller screens */
}

/* Responsive adjustments for footer columns */
@media (max-width: 1200px) {
  .footer__column {
    flex: 1 1 calc(33.333% - 2rem); /* 3 columns on medium screens */
  }
}

@media (max-width: 768px) {
  .footer__column {
    flex: 1 1 calc(50% - 2rem); /* 2 columns on smaller screens */
  }
}

@media (max-width: 480px) {
  .footer__column {
    flex: 1 1 100%; /* 1 column on very small screens */
  }
}

.footer-widget {
  margin-bottom: 1.5rem; /* Space below each widget */
}

.footer-widget-title {
  font-size: 1.1rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #333; /* Adjust title color if needed */
}

.footer-widget ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-widget ul li {
  margin-bottom: 0.5rem;
}

.footer-widget ul li a {
  text-decoration: none;
  color: var(--second-font-color);
  transition: color 0.2s ease;
}

.footer-widget ul li a:hover {
  color: var(--primary-color);
}

.wc-block-components-price-slider__range-text {
  color: var(--second-font-color);
}

/* Import Search Results Styles */
@import "./search-results.css";

.wp-singular {
  .wp-block-heading {
    margin-block: 2rem;
  }
}

.searchform {
  position: relative;
}

#searchsubmit {
  position: absolute;
  top: 0.25rem;
  right: 0.25rem;
}

/* Lost Password Form Styles */
.woocommerce-lost-password {
  max-width: 100%;
  margin: 0 auto;
}

.woocommerce-lost-password h2 {
  margin-bottom: 1.5rem;
  font-size: 1.75rem;
  font-weight: 600;
  color: #333;
}

.woocommerce-lost-password p {
  margin-bottom: 1.5rem;
  color: var(--second-font-color);
}

.woocommerce-form-lost-password {
  background-color: #fff;
  padding: 1.5rem;
  border-radius: 0.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.woocommerce-form-lost-password label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.woocommerce-form-lost-password input[type="text"],
.woocommerce-form-lost-password input[type="password"] {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 0.25rem;
  margin-bottom: 1rem;
  transition: border-color 0.2s ease;
}

.woocommerce-form-lost-password input[type="text"]:focus,
.woocommerce-form-lost-password input[type="password"]:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(1, 160, 226, 0.1);
}

.reset-password .required {
  color: #e2401c;
}

.woocommerce-form-lost-password button {
  width: 100%;
  padding: 0.75rem 1rem;
  background-color: var(--primary-color);
  color: white;
  border: 1px solid var(--primary-color);
  border-radius: 0.25rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease, color 0.2s ease;
}

.woocommerce-form-lost-password button:hover {
  background-color: #fff;
  color: var(--primary-color);
}

.return-to-login {
  margin-top: 1rem;
  text-align: center;
}

.return-to-login a {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
}

.return-to-login a:hover {
  text-decoration: underline;
}

.woocommerce-message {
  background-color: #f0f9ff;
  border-left: 4px solid var(--primary-color);
  padding: 1rem;
  margin-bottom: 1.5rem;
  border-radius: 0.25rem;
  color: #333;
  border-top-color: transparent;

  &::before {
    display: none;
  }
}

/* Standard Login Form Styles */
.woocommerce-login-wrapper {
  max-width: 100%;
  margin: 0 auto;
}

.woocommerce-login-wrapper h2 {
  margin-bottom: 1.5rem;
  font-size: 1.75rem;
  font-weight: 600;
  color: #333;
}

.woocommerce-form-login {
  background-color: #fff;
  padding: 1.5rem;
  border-radius: 0.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  margin-bottom: 1.5rem;
}

.woocommerce-form-login label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.woocommerce-form-login input[type="text"],
.woocommerce-form-login input[type="password"],
.woocommerce-form-login input[type="email"] {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 0.25rem;
  margin-bottom: 1rem;
  transition: border-color 0.2s ease;
}

.woocommerce-form-login input[type="text"]:focus,
.woocommerce-form-login input[type="password"]:focus,
.woocommerce-form-login input[type="email"]:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(1, 160, 226, 0.1);
}

.woocommerce-form-login button {
  width: 100%;
  padding: 0.75rem 1rem;
  background-color: var(--primary-color);
  color: white;
  border: 1px solid var(--primary-color);
  border-radius: 0.25rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease, color 0.2s ease;
}

.woocommerce-form-login button:hover {
  background-color: #fff;
  color: var(--primary-color);
}

.woocommerce-form-login .woocommerce-form-login__rememberme {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
}

.woocommerce-form-login .woocommerce-form-login__rememberme input[type="checkbox"] {
  margin-right: 0.5rem;
}

.woocommerce-LostPassword.lost_password {
  margin-top: 1rem;
  text-align: center;
}

.woocommerce-LostPassword.lost_password a {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
}

.woocommerce-LostPassword.lost_password a:hover {
  text-decoration: underline;
}

.woocommerce-register-link {
  margin-top: 1rem;
  text-align: center;
  padding-top: 1rem;
  border-top: 1px solid #eee;
}

.woocommerce-register-link a {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
}

.woocommerce-register-link a:hover {
  text-decoration: underline;
}

/* Form field styles */
.woocommerce-form-row {
  margin-bottom: 1rem;
}

.form-row-wide {
  width: 100%;
}

.required {
  color: #e2401c;
}

/* Error and message styles */
.woocommerce-error {
  background-color: #fff6f6;
  border-left: 4px solid #e2401c;
  padding: 1rem;
  margin-bottom: 1.5rem;
  border-radius: 0.25rem;
  color: #333;
}

.woocommerce-info {
  background-color: #f0f9ff;
  border-left: 4px solid var(--primary-color);
  padding: 1rem 1rem 1rem 3rem;
  margin-bottom: 1.5rem;
  border-radius: 0.25rem;
  color: #333;
  border-top: 0;
}

/* Responsive styles for the forms */
@media (max-width: 768px) {
  .woocommerce-form-lost-password,
  .woocommerce-form-login {
    padding: 1rem;
  }

  .woocommerce-lost-password h2,
  .woocommerce-login-wrapper h2 {
    font-size: 1.5rem;
  }

  .woocommerce-form-login .woocommerce-form-login__rememberme {
    margin-bottom: 0.75rem;
  }
}

.woocommerce form .form-row-first,
.woocommerce-page form .form-row-first,
.woocommerce form .form-row-last,
.woocommerce-page form .form-row-last {
  float: none;
  width: 100%;
}

.form-row + .lost_password {
  display: none !important;
}

.site-header .site-branding {
  background-color: transparent;
}

.woocommerce-error li,
.woocommerce-info li,
.woocommerce-message li {
  padding-left: 2rem !important;
}

.shipping-calculator-button {
  display: none !important;
}

/**
 * WooCommerce My Account Styles
 * Modern styling for WooCommerce my-account pages
 */

/* Main My Account Container */
.woocommerce-account .woocommerce {
  display: flex;
  flex-wrap: wrap;
  gap: 2rem;
  margin-top: 2rem;
}

/* Navigation Sidebar */
.woocommerce-account .woocommerce-MyAccount-navigation {
  width: 100%;
  max-width: 250px;
  margin-top: 0;
}

.woocommerce-account .woocommerce-MyAccount-navigation ul {
  padding: 0;
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  background-color: #fff;
}

.woocommerce-account .woocommerce-MyAccount-navigation ul li {
  margin: 0;
  padding: 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.woocommerce-account .woocommerce-MyAccount-navigation ul li:last-child {
  border-bottom: none;
}

.woocommerce-account .woocommerce-MyAccount-navigation ul li a {
  display: block;
  padding: 1rem 1.25rem;
  color: #333;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.2s ease;
}

.woocommerce-account .woocommerce-MyAccount-navigation ul li a:hover {
  background-color: #f9f9f9;
  color: var(--primary-color);
}

.woocommerce-account .woocommerce-MyAccount-navigation ul li.is-active a {
  background-color: var(--primary-color);
  color: #fff;
}

/* Content Area */
.woocommerce-account .woocommerce-MyAccount-content {
  flex: 1;
  min-width: 0;
  background-color: #fff;
  padding: 2rem;
  border-radius: 0.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  margin-top: 0;
}

.woocommerce-account .woocommerce-MyAccount-content h2,
.woocommerce-account .woocommerce-MyAccount-content h3 {
  margin-top: 0;
  margin-bottom: 1.5rem;
  color: #333;
}

.woocommerce-account .woocommerce-MyAccount-content p {
  margin-bottom: 1.5rem;
  color: var(--second-font-color);
}

.woocommerce-account .woocommerce-MyAccount-content a:hover {
  color: var(--primary-color);
  text-decoration: none;
}

/* Dashboard Welcome Message */
.woocommerce-account .woocommerce-MyAccount-content .woocommerce-welcome-message {
  margin-bottom: 2rem;
}

/* Orders Table */
.woocommerce-account .woocommerce-MyAccount-content .woocommerce-orders-table,
.woocommerce-account .woocommerce-MyAccount-content .woocommerce-table--order-details {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 1.5rem;
}

.woocommerce-account .woocommerce-MyAccount-content .woocommerce-orders-table th,
.woocommerce-account .woocommerce-MyAccount-content .woocommerce-table--order-details th {
  background-color: #f9f9f9;
  padding: 0.75rem 1rem;
  text-align: left;
  font-weight: 600;
  color: #333;
}

.woocommerce-account .woocommerce-MyAccount-content .woocommerce-orders-table td,
.woocommerce-account .woocommerce-MyAccount-content .woocommerce-table--order-details td {
  padding: 1rem;
  border-bottom: 1px solid #f0f0f0;
}

.woocommerce-account .woocommerce-MyAccount-content .woocommerce-orders-table tr:last-child td {
  border-bottom: none;
}

/* View Order Button */
.woocommerce-account
  .woocommerce-MyAccount-content
  .woocommerce-orders-table
  .woocommerce-orders-table__cell-order-actions
  .button,
.woocommerce-account .woocommerce-MyAccount-content .woocommerce-button.button.view {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  background-color: var(--primary-color);
  color: white;
  border: 1px solid var(--primary-color);
  border-radius: 0.25rem;
  transition: all 0.2s ease;
}

.woocommerce-account
  .woocommerce-MyAccount-content
  .woocommerce-orders-table
  .woocommerce-orders-table__cell-order-actions
  .button:hover,
.woocommerce-account .woocommerce-MyAccount-content .woocommerce-button.button.view:hover {
  background-color: white;
  color: var(--primary-color);
}

/* Order Status */
.woocommerce-account
  .woocommerce-MyAccount-content
  .woocommerce-orders-table
  .woocommerce-orders-table__cell-order-status {
  text-transform: capitalize;
}

.woocommerce-account
  .woocommerce-MyAccount-content
  .woocommerce-orders-table
  .woocommerce-orders-table__cell-order-status
  .status-completed {
  color: #4caf50;
}

.woocommerce-account
  .woocommerce-MyAccount-content
  .woocommerce-orders-table
  .woocommerce-orders-table__cell-order-status
  .status-processing {
  color: #2196f3;
}

.woocommerce-account
  .woocommerce-MyAccount-content
  .woocommerce-orders-table
  .woocommerce-orders-table__cell-order-status
  .status-on-hold {
  color: #ff9800;
}

.woocommerce-account
  .woocommerce-MyAccount-content
  .woocommerce-orders-table
  .woocommerce-orders-table__cell-order-status
  .status-cancelled {
  color: #f44336;
}

/* Address Book */
.woocommerce-account .woocommerce-MyAccount-content .woocommerce-Addresses {
  display: flex;
  flex-wrap: wrap;
  gap: 2rem;
  margin-top: 1.5rem;
}

.woocommerce-account .woocommerce-MyAccount-content .woocommerce-Addresses .woocommerce-Address {
  flex: 1;
  min-width: 250px;
  padding: 1.5rem;
  border: 1px solid #f0f0f0;
  border-radius: 0.5rem;
}

.woocommerce-account
  .woocommerce-MyAccount-content
  .woocommerce-Addresses
  .woocommerce-Address-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.woocommerce-account
  .woocommerce-MyAccount-content
  .woocommerce-Addresses
  .woocommerce-Address-title
  h3 {
  margin: 0;
  font-size: 1.25rem;
}

.woocommerce-account
  .woocommerce-MyAccount-content
  .woocommerce-Addresses
  .woocommerce-Address-title
  .edit {
  font-size: 0.875rem;
  color: var(--primary-color);
}

/* Forms */
.woocommerce-account .woocommerce-MyAccount-content form .form-row {
  margin-bottom: 1.5rem;
}

.woocommerce-account .woocommerce-MyAccount-content form .form-row label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.woocommerce-account .woocommerce-MyAccount-content form .form-row input,
.woocommerce-account .woocommerce-MyAccount-content form .form-row textarea,
.woocommerce-account .woocommerce-MyAccount-content form fieldset,
.woocommerce-account .woocommerce-MyAccount-content form .form-row select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 0.25rem;
  font-size: 1rem;
}

.woocommerce-Address {
  position: relative !important;
}

.woocommerce-account
  .woocommerce-MyAccount-content
  .woocommerce-Addresses
  .woocommerce-Address-title
  .edit {
  position: absolute;
  bottom: 0.5rem;
  right: 0.5rem;
}

.woocommerce-account .woocommerce-MyAccount-content form .form-row input:focus,
.woocommerce-account .woocommerce-MyAccount-content form .form-row textarea:focus,
.woocommerce-account .woocommerce-MyAccount-content form .form-row select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 1px var(--primary-color);
}

/* Company Data Section */
.woocommerce-account .woocommerce-MyAccount-content .woocommerce-company-data {
  background-color: #f9f9f9;
  padding: 1.5rem;
  border-radius: 0.5rem;
  margin-bottom: 2rem;
}

.woocommerce-account .woocommerce-MyAccount-content .woocommerce-company-data h3 {
  margin-top: 0;
  margin-bottom: 1rem;
}

.woocommerce-account .woocommerce-MyAccount-content .woocommerce-company-data-table {
  background-color: #fff;
  border-radius: 0.25rem;
  overflow: hidden;
}

.woocommerce-account .woocommerce-MyAccount-content .woocommerce-company-data-actions {
  margin-top: 1.5rem;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .woocommerce-account .woocommerce {
    flex-direction: column;
  }

  .woocommerce-account .woocommerce-MyAccount-navigation {
    max-width: 100%;
    margin-bottom: 1.5rem;
  }

  .woocommerce-account .woocommerce-MyAccount-content {
    padding: 1.5rem;
  }

  .woocommerce-account .woocommerce-MyAccount-content .woocommerce-Addresses {
    flex-direction: column;
  }

  .woocommerce-account .woocommerce-MyAccount-content .woocommerce-Addresses .woocommerce-Address {
    margin-bottom: 1.5rem;
  }
}

