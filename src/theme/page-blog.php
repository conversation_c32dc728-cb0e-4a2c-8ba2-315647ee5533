<?php
get_header();
?>

	<div class="container news-page">
		<h1><PERSON>jn<PERSON><PERSON> wpisy</h1>


		<?php
		// Define custom query parameters
		$paged = (get_query_var('paged')) ? get_query_var('paged') : 1;

		$args = array(
			'post_type' => 'post',
			'posts_per_page' => 12, // Number of posts per page
			'paged' => $paged
		);

		$custom_query = new WP_Query($args);

		if ($custom_query->have_posts()) :
			?>
			<div class="news-list">

				<?php
				while ($custom_query->have_posts()) : $custom_query->the_post(); ?>

					<article>
						<span class="date"><?php echo get_the_date('F j, Y'); ?></span>
						<a href="<?php the_permalink(); ?>">
							<?php the_post_thumbnail('full'); ?>
						</a>
						<h2><?php the_title(); ?></h2>
						<p><?php the_excerpt(); ?></p>

						<a href="<?php the_permalink(); ?>" class="news-button"><PERSON><PERSON><PERSON><PERSON> wi<PERSON>cej</a>
					</article>


				<?php endwhile; ?>
			</div>
			<!-- Pagination -->
			<div class="pagination">
				<?php
				echo paginate_links(array(
					'total' => $custom_query->max_num_pages
				));
				?>
			</div>

		<?php
		else :
			echo '<p>No posts found.</p>';
		endif;

		// Reset post data
		wp_reset_postdata();
		?>


	</div>

<?php
get_footer();
