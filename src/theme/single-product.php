<?php
get_header();
global $post;
$product = wc_get_product($post->ID);

?>

<div class="container">
	<section class="single-produt-header">
		<div class="single-product-header-description">
			<div class="badges">
				<?php
				if ($product && $product->is_featured()) {
					echo '<span class="badge badge-featured">Polecamy</span>';
				}

				if ($product && $product->is_on_sale()) {
					$regular_price = $product->get_regular_price();
					$sale_price    = $product->get_sale_price();
					$percentage    = round((($regular_price - $sale_price) / $regular_price) * 100);
					echo '<span class="badge badge-promo">-' . $percentage . '%</span>';
				}
				?>
			</div>

			<div style="display: flex; gap: 1.2rem; align-items: center;">

				<?php if (wc_review_ratings_enabled()) :
					$average_rating = $product->get_average_rating();
					$rating_count = $product->get_rating_count();
				?>
					<ul class="stars-list">
						<?php for ($i = 1; $i <= 5; $i++) : ?>
							<li class="<?php echo ($i <= $average_rating) ? '' : 'inactive'; ?>">
								<svg width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
									<path
										d="M10.0253 1.12402L12.4619 6.04688C12.5634 6.24884 12.7157 6.36245 12.9314 6.38769L18.3756 7.1703C18.6294 7.20817 18.7943 7.34702 18.8832 7.59947C18.972 7.85193 18.9086 8.05389 18.7309 8.24323L14.7969 12.0679C14.6446 12.2194 14.5812 12.4087 14.6192 12.6107L15.5456 18.0132C15.5837 18.2657 15.5075 18.4676 15.2918 18.6191C15.0761 18.7706 14.8603 18.7832 14.6319 18.6696L9.74612 16.1198C9.55577 16.0188 9.35272 16.0188 9.16237 16.1198L4.27658 18.6696C4.04815 18.7958 3.83242 18.7706 3.61668 18.6191C3.40095 18.4676 3.3248 18.2657 3.36288 18.0132L4.28927 12.6107C4.32734 12.3961 4.26389 12.2068 4.11161 12.0679L0.190287 8.23061C-6.82781e-05 8.05389 -0.0508297 7.8393 0.0380028 7.58685C0.126835 7.3344 0.29181 7.19555 0.545617 7.15768L6.00247 6.37507C6.21821 6.3372 6.37049 6.2236 6.47201 6.03426L8.90856 1.12402C9.02277 0.896808 9.21313 0.783203 9.46694 0.783203C9.72074 0.783203 9.9111 0.896808 10.0253 1.12402Z"
										fill="#01A0E2" />
								</svg>
							</li>
						<?php endfor; ?>
					</ul>
					<?php if ($rating_count > 0) : ?>
						<span class="rating-count">(<?php echo $rating_count; ?> <?php echo _n('opinia', 'opinii', $rating_count, 'woocommerce'); ?>)</span>
					<?php endif; ?>
				<?php endif; ?>
			</div>
			<ul class="product-stats">
				<li>
					<svg width="14" height="9" viewBox="0 0 14 9" fill="none" xmlns="http://www.w3.org/2000/svg">
						<mask id="path-1-outside-1_85_5458" maskUnits="userSpaceOnUse" x="0" y="-0.289062" width="14" height="9"
							fill="black">
							<rect fill="white" y="-0.289062" width="14" height="9" />
							<path fill-rule="evenodd" clip-rule="evenodd"
								d="M2.37297 7.00512C2.37297 6.91474 2.29976 6.84139 2.20956 6.84139V6.84106H1.64906C1.56082 6.84106 1.4801 6.80471 1.4216 6.7461C1.3631 6.68748 1.32682 6.60693 1.32682 6.51818V1.36128C1.32682 1.27287 1.3631 1.19198 1.4216 1.13337C1.4801 1.07475 1.56049 1.0384 1.64906 1.0384H8.29623C8.38447 1.0384 8.4652 1.07475 8.5237 1.13337C8.5822 1.19198 8.61847 1.27254 8.61847 1.36128V6.84106H5.38362C5.29342 6.84106 5.22021 6.91441 5.22021 7.00479C5.22021 7.09517 5.29342 7.16853 5.38362 7.16853H8.92316C8.92947 7.16927 8.93589 7.16965 8.94241 7.16965H9.25681C9.34701 7.16965 9.42022 7.0963 9.42022 7.00592C9.42022 6.91554 9.34701 6.84219 9.25681 6.84219H8.94529V2.46617L8.95156 2.46629V2.46694H11.7959C11.8776 2.46694 11.9465 2.50067 11.9991 2.55536C12.0652 2.62347 12.109 2.72335 12.1256 2.83468L12.6685 6.47641C12.673 6.50555 12.6747 6.53273 12.6747 6.55795C12.6737 6.63523 12.6551 6.7076 12.6194 6.76163C12.592 6.80355 12.5528 6.83335 12.5038 6.84055C12.4917 6.84251 12.4809 6.84317 12.4714 6.84317L12.359 6.84186C12.2691 6.84121 12.1956 6.91357 12.1949 7.00363C12.1943 7.09368 12.2665 7.16736 12.3564 7.16801L12.4688 7.16932C12.4992 7.16965 12.5266 7.16769 12.5511 7.16408C12.7008 7.14149 12.8155 7.05799 12.8927 6.94043C12.962 6.83466 12.9989 6.70007 13.0002 6.56024C13.0005 6.51472 12.9976 6.47084 12.9914 6.4286L12.4485 2.78687C12.422 2.60972 12.3475 2.44566 12.2328 2.32711C12.1204 2.21086 11.9723 2.13882 11.7959 2.13882H8.95156L8.94529 2.13894V1.36128C8.94529 1.18249 8.87241 1.01974 8.75476 0.901849C8.63743 0.783962 8.475 0.710938 8.29623 0.710938H1.64906C1.47062 0.710938 1.30819 0.783962 1.19054 0.901849C1.07288 1.01941 1 1.18216 1 1.36128V6.51851C1 6.69731 1.07288 6.86006 1.19054 6.97794C1.30786 7.09583 1.47029 7.16885 1.64906 7.16885H2.20956C2.29976 7.16885 2.37297 7.0955 2.37297 7.00512ZM4.45719 6.32712C4.28431 6.1539 4.0454 6.04681 3.78166 6.04681C3.51792 6.04681 3.27901 6.1539 3.10613 6.32712C2.93324 6.50035 2.82637 6.73973 2.82637 7.00399C2.82637 7.26826 2.93324 7.50763 3.10613 7.68086C3.27901 7.85409 3.51792 7.96117 3.78166 7.96117C4.0454 7.96117 4.28431 7.85409 4.45719 7.68086C4.63008 7.50763 4.73695 7.26826 4.73695 7.00399C4.73695 6.73973 4.63008 6.50035 4.45719 6.32712ZM3.78166 5.71935C4.1356 5.71935 4.45621 5.86311 4.68826 6.09561C4.9203 6.32811 5.06377 6.64935 5.06377 7.00399C5.06377 7.35864 4.9203 7.67988 4.68826 7.91238C4.45621 8.14488 4.1356 8.28863 3.78166 8.28863C3.42771 8.28863 3.10711 8.14488 2.87506 7.91238C2.64302 7.67988 2.49955 7.35864 2.49955 7.00399C2.49955 6.64935 2.64302 6.32811 2.87506 6.09561C3.10711 5.86311 3.42771 5.71935 3.78166 5.71935ZM10.8296 6.04681C11.0933 6.04681 11.3322 6.1539 11.5051 6.32712C11.678 6.50035 11.7849 6.73973 11.7849 7.00399C11.7849 7.26826 11.678 7.50763 11.5051 7.68086C11.3322 7.85409 11.0933 7.96117 10.8296 7.96117C10.5658 7.96117 10.3269 7.85409 10.1541 7.68086C9.98116 7.50763 9.87429 7.26826 9.87429 7.00399C9.87429 6.73973 9.98116 6.50035 10.1541 6.32712C10.3269 6.1539 10.5658 6.04681 10.8296 6.04681ZM11.7362 6.09561C11.5041 5.86311 11.1835 5.71935 10.8296 5.71935C10.4756 5.71935 10.155 5.86311 9.92299 6.09561C9.69095 6.32811 9.54748 6.64935 9.54748 7.00399C9.54748 7.35864 9.69095 7.67988 9.92299 7.91238C10.155 8.14488 10.4756 8.28863 10.8296 8.28863C11.1835 8.28863 11.5041 8.14488 11.7362 7.91238C11.9682 7.67988 12.1117 7.35864 12.1117 7.00399C12.1117 6.64935 11.9682 6.32811 11.7362 6.09561Z" />
						</mask>
						<path fill-rule="evenodd" clip-rule="evenodd"
							d="M2.37297 7.00512C2.37297 6.91474 2.29976 6.84139 2.20956 6.84139V6.84106H1.64906C1.56082 6.84106 1.4801 6.80471 1.4216 6.7461C1.3631 6.68748 1.32682 6.60693 1.32682 6.51818V1.36128C1.32682 1.27287 1.3631 1.19198 1.4216 1.13337C1.4801 1.07475 1.56049 1.0384 1.64906 1.0384H8.29623C8.38447 1.0384 8.4652 1.07475 8.5237 1.13337C8.5822 1.19198 8.61847 1.27254 8.61847 1.36128V6.84106H5.38362C5.29342 6.84106 5.22021 6.91441 5.22021 7.00479C5.22021 7.09517 5.29342 7.16853 5.38362 7.16853H8.92316C8.92947 7.16927 8.93589 7.16965 8.94241 7.16965H9.25681C9.34701 7.16965 9.42022 7.0963 9.42022 7.00592C9.42022 6.91554 9.34701 6.84219 9.25681 6.84219H8.94529V2.46617L8.95156 2.46629V2.46694H11.7959C11.8776 2.46694 11.9465 2.50067 11.9991 2.55536C12.0652 2.62347 12.109 2.72335 12.1256 2.83468L12.6685 6.47641C12.673 6.50555 12.6747 6.53273 12.6747 6.55795C12.6737 6.63523 12.6551 6.7076 12.6194 6.76163C12.592 6.80355 12.5528 6.83335 12.5038 6.84055C12.4917 6.84251 12.4809 6.84317 12.4714 6.84317L12.359 6.84186C12.2691 6.84121 12.1956 6.91357 12.1949 7.00363C12.1943 7.09368 12.2665 7.16736 12.3564 7.16801L12.4688 7.16932C12.4992 7.16965 12.5266 7.16769 12.5511 7.16408C12.7008 7.14149 12.8155 7.05799 12.8927 6.94043C12.962 6.83466 12.9989 6.70007 13.0002 6.56024C13.0005 6.51472 12.9976 6.47084 12.9914 6.4286L12.4485 2.78687C12.422 2.60972 12.3475 2.44566 12.2328 2.32711C12.1204 2.21086 11.9723 2.13882 11.7959 2.13882H8.95156L8.94529 2.13894V1.36128C8.94529 1.18249 8.87241 1.01974 8.75476 0.901849C8.63743 0.783962 8.475 0.710938 8.29623 0.710938H1.64906C1.47062 0.710938 1.30819 0.783962 1.19054 0.901849C1.07288 1.01941 1 1.18216 1 1.36128V6.51851C1 6.69731 1.07288 6.86006 1.19054 6.97794C1.30786 7.09583 1.47029 7.16885 1.64906 7.16885H2.20956C2.29976 7.16885 2.37297 7.0955 2.37297 7.00512ZM4.45719 6.32712C4.28431 6.1539 4.0454 6.04681 3.78166 6.04681C3.51792 6.04681 3.27901 6.1539 3.10613 6.32712C2.93324 6.50035 2.82637 6.73973 2.82637 7.00399C2.82637 7.26826 2.93324 7.50763 3.10613 7.68086C3.27901 7.85409 3.51792 7.96117 3.78166 7.96117C4.0454 7.96117 4.28431 7.85409 4.45719 7.68086C4.63008 7.50763 4.73695 7.26826 4.73695 7.00399C4.73695 6.73973 4.63008 6.50035 4.45719 6.32712ZM3.78166 5.71935C4.1356 5.71935 4.45621 5.86311 4.68826 6.09561C4.9203 6.32811 5.06377 6.64935 5.06377 7.00399C5.06377 7.35864 4.9203 7.67988 4.68826 7.91238C4.45621 8.14488 4.1356 8.28863 3.78166 8.28863C3.42771 8.28863 3.10711 8.14488 2.87506 7.91238C2.64302 7.67988 2.49955 7.35864 2.49955 7.00399C2.49955 6.64935 2.64302 6.32811 2.87506 6.09561C3.10711 5.86311 3.42771 5.71935 3.78166 5.71935ZM10.8296 6.04681C11.0933 6.04681 11.3322 6.1539 11.5051 6.32712C11.678 6.50035 11.7849 6.73973 11.7849 7.00399C11.7849 7.26826 11.678 7.50763 11.5051 7.68086C11.3322 7.85409 11.0933 7.96117 10.8296 7.96117C10.5658 7.96117 10.3269 7.85409 10.1541 7.68086C9.98116 7.50763 9.87429 7.26826 9.87429 7.00399C9.87429 6.73973 9.98116 6.50035 10.1541 6.32712C10.3269 6.1539 10.5658 6.04681 10.8296 6.04681ZM11.7362 6.09561C11.5041 5.86311 11.1835 5.71935 10.8296 5.71935C10.4756 5.71935 10.155 5.86311 9.92299 6.09561C9.69095 6.32811 9.54748 6.64935 9.54748 7.00399C9.54748 7.35864 9.69095 7.67988 9.92299 7.91238C10.155 8.14488 10.4756 8.28863 10.8296 8.28863C11.1835 8.28863 11.5041 8.14488 11.7362 7.91238C11.9682 7.67988 12.1117 7.35864 12.1117 7.00399C12.1117 6.64935 11.9682 6.32811 11.7362 6.09561Z"
							fill="#707070" />
						<path
							d="M2.20956 6.84139H2.00956V7.04139H2.20956V6.84139ZM2.20956 6.84106H2.40956V6.64106H2.20956V6.84106ZM1.4216 6.7461L1.28004 6.88738L1.28004 6.88738L1.4216 6.7461ZM8.5237 1.13337L8.66526 0.992084L8.66526 0.992084L8.5237 1.13337ZM8.61847 6.84106V7.04106H8.81847V6.84106H8.61847ZM8.92316 7.16853L8.94654 6.9699L8.93489 6.96853H8.92316V7.16853ZM8.94529 6.84219H8.74529V7.04219H8.94529V6.84219ZM8.94529 2.46617L8.94907 2.2662L8.74529 2.26236V2.46617H8.94529ZM8.95156 2.46629H9.15156V2.27003L8.95533 2.26632L8.95156 2.46629ZM8.95156 2.46694H8.75156V2.66694H8.95156V2.46694ZM11.9991 2.55536L11.855 2.69403L11.8555 2.69455L11.9991 2.55536ZM12.1256 2.83468L12.3234 2.8052L12.3234 2.80507L12.1256 2.83468ZM12.6685 6.47641L12.4706 6.5059L12.4709 6.50743L12.6685 6.47641ZM12.6747 6.55795L12.8747 6.56049V6.55795H12.6747ZM12.6194 6.76163L12.4525 6.65154L12.4521 6.65205L12.6194 6.76163ZM12.5038 6.84055L12.4747 6.64265L12.4717 6.64314L12.5038 6.84055ZM12.4714 6.84317L12.4691 7.04317H12.4714V6.84317ZM12.359 6.84186L12.3613 6.64187L12.3604 6.64187L12.359 6.84186ZM12.1949 7.00363L11.9949 7.00217V7.00218L12.1949 7.00363ZM12.3564 7.16801L12.3587 6.96803L12.3578 6.96802L12.3564 7.16801ZM12.4688 7.16932L12.4665 7.36931L12.4666 7.36931L12.4688 7.16932ZM12.5511 7.16408L12.5802 7.36196L12.581 7.36184L12.5511 7.16408ZM12.8927 6.94043L13.0599 7.05014L13.06 7.05002L12.8927 6.94043ZM13.0002 6.56024L13.2002 6.56211L13.2002 6.56167L13.0002 6.56024ZM12.9914 6.4286L13.1892 6.39951L13.1892 6.39911L12.9914 6.4286ZM12.4485 2.78687L12.6463 2.75739L12.6463 2.75732L12.4485 2.78687ZM12.2328 2.32711L12.0891 2.46615L12.0891 2.4662L12.2328 2.32711ZM8.95156 2.13882V1.93879L8.94778 1.93886L8.95156 2.13882ZM8.94529 2.13894H8.74529V2.34275L8.94907 2.3389L8.94529 2.13894ZM8.75476 0.901849L8.613 1.04293L8.6132 1.04313L8.75476 0.901849ZM1.19054 0.901849L1.3319 1.04333L1.3321 1.04313L1.19054 0.901849ZM1.19054 6.97794L1.33229 6.83686L1.3321 6.83666L1.19054 6.97794ZM4.45719 6.32712L4.59875 6.18584V6.18584L4.45719 6.32712ZM3.10613 6.32712L2.96456 6.18584L2.96456 6.18584L3.10613 6.32712ZM3.10613 7.68086L2.96456 7.82214L2.96456 7.82214L3.10613 7.68086ZM4.45719 7.68086L4.59875 7.82214V7.82214L4.45719 7.68086ZM4.68826 6.09561L4.54669 6.23689L4.54669 6.23689L4.68826 6.09561ZM4.68826 7.91238L4.54669 7.77109L4.54669 7.7711L4.68826 7.91238ZM2.87506 7.91238L3.01662 7.7711V7.77109L2.87506 7.91238ZM2.87506 6.09561L3.01662 6.23689V6.23689L2.87506 6.09561ZM11.5051 6.32712L11.3636 6.46841V6.46841L11.5051 6.32712ZM11.5051 7.68086L11.3636 7.53958V7.53958L11.5051 7.68086ZM10.1541 7.68086L10.2956 7.53958V7.53958L10.1541 7.68086ZM10.1541 6.32712L10.2956 6.46841V6.46841L10.1541 6.32712ZM11.7362 6.09561L11.5946 6.23689V6.23689L11.7362 6.09561ZM9.92299 6.09561L10.0646 6.23689V6.23689L9.92299 6.09561ZM9.92299 7.91238L10.0646 7.7711V7.77109L9.92299 7.91238ZM11.7362 7.91238L11.5946 7.77109V7.7711L11.7362 7.91238ZM2.20956 7.04139C2.18893 7.04139 2.17297 7.02483 2.17297 7.00512H2.57297C2.57297 6.80466 2.41059 6.64139 2.20956 6.64139V7.04139ZM2.00956 6.84106V6.84139H2.40956V6.84106H2.00956ZM1.64906 7.04106H2.20956V6.64106H1.64906V7.04106ZM1.28004 6.88738C1.37408 6.98161 1.50497 7.04106 1.64906 7.04106V6.64106C1.61667 6.64106 1.58612 6.62782 1.56316 6.60482L1.28004 6.88738ZM1.12682 6.51818C1.12682 6.66262 1.18608 6.79324 1.28004 6.88738L1.56316 6.60482C1.54011 6.58173 1.52682 6.55123 1.52682 6.51818H1.12682ZM1.12682 1.36128V6.51818H1.52682V1.36128H1.12682ZM1.28004 0.992084C1.186 1.0863 1.12682 1.21728 1.12682 1.36128H1.52682C1.52682 1.32845 1.54019 1.29766 1.56316 1.27465L1.28004 0.992084ZM1.64906 0.838401C1.50454 0.838401 1.374 0.897938 1.28004 0.992084L1.56316 1.27465C1.5862 1.25156 1.61645 1.2384 1.64906 1.2384V0.838401ZM8.29623 0.838401H1.64906V1.2384H8.29623V0.838401ZM8.66526 0.992084C8.57122 0.897858 8.44032 0.838401 8.29623 0.838401V1.2384C8.32862 1.2384 8.35918 1.25164 8.38214 1.27465L8.66526 0.992084ZM8.81847 1.36128C8.81847 1.21684 8.75921 1.08623 8.66526 0.992084L8.38214 1.27465C8.40518 1.29774 8.41847 1.32823 8.41847 1.36128H8.81847ZM8.81847 6.84106V1.36128H8.41847V6.84106H8.81847ZM5.38362 7.04106H8.61847V6.64106H5.38362V7.04106ZM5.42021 7.00479C5.42021 7.0245 5.40425 7.04106 5.38362 7.04106V6.64106C5.18259 6.64106 5.02021 6.80433 5.02021 7.00479H5.42021ZM5.38362 6.96853C5.40425 6.96853 5.42021 6.98509 5.42021 7.00479H5.02021C5.02021 7.20526 5.18259 7.36853 5.38362 7.36853V6.96853ZM8.92316 6.96853H5.38362V7.36853H8.92316V6.96853ZM8.94241 6.96965C8.94373 6.96965 8.94512 6.96973 8.94654 6.9699L8.89977 7.36715C8.91382 7.36881 8.92806 7.36965 8.94241 7.36965V6.96965ZM9.25681 6.96965H8.94241V7.36965H9.25681V6.96965ZM9.22022 7.00592C9.22022 6.98622 9.23618 6.96965 9.25681 6.96965V7.36965C9.45784 7.36965 9.62022 7.20638 9.62022 7.00592H9.22022ZM9.25681 7.04219C9.23618 7.04219 9.22022 7.02562 9.22022 7.00592H9.62022C9.62022 6.80545 9.45784 6.64219 9.25681 6.64219V7.04219ZM8.94529 7.04219H9.25681V6.64219H8.94529V7.04219ZM8.74529 2.46617V6.84219H9.14529V2.46617H8.74529ZM8.95533 2.26632L8.94907 2.2662L8.94152 2.66613L8.94778 2.66625L8.95533 2.26632ZM9.15156 2.46694V2.46629H8.75156V2.46694H9.15156ZM11.7959 2.26694H8.95156V2.66694H11.7959V2.26694ZM12.1433 2.41669C12.0569 2.32696 11.9374 2.26694 11.7959 2.26694V2.66694C11.8177 2.66694 11.8361 2.67438 11.855 2.69403L12.1433 2.41669ZM12.3234 2.80507C12.3022 2.66333 12.2446 2.52129 12.1428 2.41616L11.8555 2.69455C11.8857 2.72565 11.9157 2.78336 11.9278 2.86429L12.3234 2.80507ZM12.8663 6.44692L12.3234 2.8052L11.9278 2.86417L12.4707 6.5059L12.8663 6.44692ZM12.8747 6.55795C12.8747 6.52397 12.8725 6.48628 12.866 6.44539L12.4709 6.50743C12.4736 6.52483 12.4747 6.5415 12.4747 6.55795H12.8747ZM12.7864 6.87172C12.8483 6.77782 12.8733 6.66531 12.8747 6.56049L12.4747 6.55541C12.4741 6.60515 12.4618 6.63737 12.4525 6.65154L12.7864 6.87172ZM12.5328 7.03843C12.6504 7.02115 12.7353 6.94981 12.7868 6.87121L12.4521 6.65205C12.4503 6.65487 12.4506 6.65333 12.4548 6.6504C12.4595 6.64709 12.4666 6.64386 12.4747 6.64268L12.5328 7.03843ZM12.4714 7.04317C12.4903 7.04317 12.5119 7.04185 12.5358 7.03796L12.4717 6.64314L12.4714 6.64317V7.04317ZM12.3566 7.04185L12.4691 7.04316L12.4737 6.64318L12.3613 6.64187L12.3566 7.04185ZM12.3949 7.00508C12.3948 7.0255 12.3781 7.042 12.3575 7.04185L12.3604 6.64187C12.1601 6.64041 11.9964 6.80164 11.9949 7.00217L12.3949 7.00508ZM12.3578 6.96802C12.3792 6.96818 12.395 6.98545 12.3949 7.00508L11.9949 7.00218C11.9935 7.20191 12.1538 7.36654 12.3549 7.36801L12.3578 6.96802ZM12.4711 6.96934L12.3587 6.96803L12.354 7.368L12.4665 7.36931L12.4711 6.96934ZM12.5221 6.96621C12.5084 6.96821 12.4914 6.96956 12.4709 6.96934L12.4666 7.36931C12.5069 7.36975 12.5448 7.36716 12.5802 7.36196L12.5221 6.96621ZM12.7254 6.83071C12.6758 6.90638 12.6084 6.95318 12.5213 6.96632L12.581 7.36184C12.7932 7.3298 12.9553 7.20959 13.0599 7.05014L12.7254 6.83071ZM12.8002 6.55837C12.7992 6.66596 12.7706 6.76182 12.7254 6.83084L13.06 7.05002C13.1533 6.90749 13.1986 6.73418 13.2002 6.56211L12.8002 6.55837ZM12.7935 6.45769C12.7981 6.48919 12.8005 6.52284 12.8002 6.55881L13.2002 6.56167C13.2006 6.50661 13.197 6.4525 13.1892 6.39951L12.7935 6.45769ZM12.2507 2.81636L12.7936 6.45809L13.1892 6.39911L12.6463 2.75739L12.2507 2.81636ZM12.0891 2.4662C12.1704 2.55026 12.2294 2.67386 12.2507 2.81643L12.6463 2.75732C12.6147 2.54557 12.5246 2.34105 12.3765 2.18803L12.0891 2.4662ZM11.7959 2.33882C11.9131 2.33882 12.0107 2.38512 12.0891 2.46615L12.3766 2.18808C12.2301 2.03661 12.0316 1.93882 11.7959 1.93882V2.33882ZM8.95156 2.33882H11.7959V1.93882H8.95156V2.33882ZM8.94907 2.3389L8.95533 2.33879L8.94778 1.93886L8.94152 1.93898L8.94907 2.3389ZM8.74529 1.36128V2.13894H9.14529V1.36128H8.74529ZM8.6132 1.04313C8.69514 1.12523 8.74529 1.23783 8.74529 1.36128H9.14529C9.14529 1.12714 9.04969 0.91424 8.89632 0.760567L8.6132 1.04313ZM8.29623 0.910938C8.4195 0.910938 8.53156 0.961102 8.613 1.04293L8.89651 0.760764C8.7433 0.606822 8.5305 0.510937 8.29623 0.510937V0.910938ZM1.64906 0.910938H8.29623V0.510937H1.64906V0.910938ZM1.3321 1.04313C1.41403 0.961036 1.52622 0.910938 1.64906 0.910938V0.510937C1.41502 0.510937 1.20235 0.606888 1.04897 0.760567L1.3321 1.04313ZM1.2 1.36128C1.2 1.2374 1.25022 1.12494 1.3319 1.04333L1.04917 0.76037C0.895539 0.913879 0.8 1.12692 0.8 1.36128H1.2ZM1.2 6.51851V1.36128H0.8V6.51851H1.2ZM1.3321 6.83666C1.25016 6.75456 1.2 6.64196 1.2 6.51851H0.8C0.8 6.75265 0.895604 6.96555 1.04897 7.11922L1.3321 6.83666ZM1.64906 6.96885C1.52579 6.96885 1.41374 6.91869 1.33229 6.83686L1.04878 7.11903C1.20199 7.27297 1.41479 7.36885 1.64906 7.36885V6.96885ZM2.20956 6.96885H1.64906V7.36885H2.20956V6.96885ZM2.17297 7.00512C2.17297 6.98542 2.18893 6.96885 2.20956 6.96885V7.36885C2.41059 7.36885 2.57297 7.20559 2.57297 7.00512H2.17297ZM3.78166 6.24681C3.99014 6.24681 4.17872 6.33122 4.31563 6.46841L4.59875 6.18584C4.3899 5.97657 4.10067 5.84682 3.78166 5.84682V6.24681ZM3.24769 6.46841C3.3846 6.33122 3.57318 6.24681 3.78166 6.24681V5.84682C3.46265 5.84682 3.17342 5.97657 2.96456 6.18584L3.24769 6.46841ZM3.02637 7.00399C3.02637 6.79473 3.11076 6.6056 3.24769 6.46841L2.96456 6.18584C2.75571 6.3951 2.62637 6.68472 2.62637 7.00399H3.02637ZM3.24769 7.53958C3.11076 7.40238 3.02637 7.21325 3.02637 7.00399H2.62637C2.62637 7.32326 2.75571 7.61288 2.96456 7.82214L3.24769 7.53958ZM3.78166 7.76117C3.57318 7.76117 3.3846 7.67677 3.24769 7.53958L2.96456 7.82214C3.17342 8.03141 3.46265 8.16117 3.78166 8.16117V7.76117ZM4.31563 7.53958C4.17872 7.67677 3.99014 7.76117 3.78166 7.76117V8.16117C4.10067 8.16117 4.3899 8.03141 4.59875 7.82214L4.31563 7.53958ZM4.53695 7.00399C4.53695 7.21325 4.45256 7.40238 4.31563 7.53958L4.59875 7.82214C4.80761 7.61288 4.93695 7.32326 4.93695 7.00399H4.53695ZM4.31563 6.46841C4.45256 6.6056 4.53695 6.79473 4.53695 7.00399H4.93695C4.93695 6.68472 4.80761 6.3951 4.59875 6.18584L4.31563 6.46841ZM4.82982 5.95433C4.56182 5.6858 4.19089 5.51935 3.78166 5.51935V5.91935C4.08032 5.91935 4.35061 6.04041 4.54669 6.23689L4.82982 5.95433ZM5.26377 7.00399C5.26377 6.59432 5.0978 6.22284 4.82982 5.95433L4.54669 6.23689C4.74279 6.43337 4.86377 6.70438 4.86377 7.00399H5.26377ZM4.82982 8.05366C5.0978 7.78514 5.26377 7.41366 5.26377 7.00399H4.86377C4.86377 7.30361 4.74279 7.57461 4.54669 7.77109L4.82982 8.05366ZM3.78166 8.48863C4.19089 8.48863 4.56182 8.32218 4.82982 8.05366L4.54669 7.7711C4.35061 7.96757 4.08032 8.08863 3.78166 8.08863V8.48863ZM2.7335 8.05366C3.0015 8.32218 3.37243 8.48863 3.78166 8.48863V8.08863C3.483 8.08863 3.21271 7.96757 3.01662 7.7711L2.7335 8.05366ZM2.29955 7.00399C2.29955 7.41366 2.46552 7.78514 2.7335 8.05366L3.01662 7.77109C2.82053 7.57461 2.69955 7.30361 2.69955 7.00399H2.29955ZM2.7335 5.95433C2.46552 6.22284 2.29955 6.59432 2.29955 7.00399H2.69955C2.69955 6.70438 2.82053 6.43337 3.01662 6.23689L2.7335 5.95433ZM3.78166 5.51935C3.37243 5.51935 3.0015 5.6858 2.7335 5.95433L3.01662 6.23689C3.21271 6.04041 3.483 5.91935 3.78166 5.91935V5.51935ZM11.6467 6.18584C11.4378 5.97657 11.1486 5.84682 10.8296 5.84682V6.24681C11.0381 6.24681 11.2266 6.33122 11.3636 6.46841L11.6467 6.18584ZM11.9849 7.00399C11.9849 6.68472 11.8555 6.3951 11.6467 6.18584L11.3636 6.46841C11.5005 6.6056 11.5849 6.79473 11.5849 7.00399H11.9849ZM11.6467 7.82214C11.8555 7.61288 11.9849 7.32326 11.9849 7.00399H11.5849C11.5849 7.21325 11.5005 7.40238 11.3636 7.53958L11.6467 7.82214ZM10.8296 8.16117C11.1486 8.16117 11.4378 8.03141 11.6467 7.82214L11.3636 7.53958C11.2266 7.67677 11.0381 7.76117 10.8296 7.76117V8.16117ZM10.0125 7.82214C10.2213 8.03141 10.5106 8.16117 10.8296 8.16117V7.76117C10.6211 7.76117 10.4325 7.67677 10.2956 7.53958L10.0125 7.82214ZM9.67429 7.00399C9.67429 7.32326 9.80364 7.61288 10.0125 7.82214L10.2956 7.53958C10.1587 7.40238 10.0743 7.21325 10.0743 7.00399H9.67429ZM10.0125 6.18584C9.80364 6.3951 9.67429 6.68472 9.67429 7.00399H10.0743C10.0743 6.79473 10.1587 6.6056 10.2956 6.46841L10.0125 6.18584ZM10.8296 5.84682C10.5106 5.84682 10.2213 5.97657 10.0125 6.18584L10.2956 6.46841C10.4325 6.33122 10.6211 6.24681 10.8296 6.24681V5.84682ZM10.8296 5.91935C11.1282 5.91935 11.3985 6.04041 11.5946 6.23689L11.8777 5.95433C11.6097 5.6858 11.2388 5.51935 10.8296 5.51935V5.91935ZM10.0646 6.23689C10.2606 6.04041 10.5309 5.91935 10.8296 5.91935V5.51935C10.4204 5.51935 10.0494 5.6858 9.78143 5.95433L10.0646 6.23689ZM9.74748 7.00399C9.74748 6.70438 9.86846 6.43337 10.0646 6.23689L9.78143 5.95433C9.51344 6.22284 9.34748 6.59432 9.34748 7.00399H9.74748ZM10.0646 7.77109C9.86846 7.57461 9.74748 7.30361 9.74748 7.00399H9.34748C9.34748 7.41366 9.51344 7.78514 9.78143 8.05366L10.0646 7.77109ZM10.8296 8.08863C10.5309 8.08863 10.2606 7.96757 10.0646 7.7711L9.78143 8.05366C10.0494 8.32218 10.4204 8.48863 10.8296 8.48863V8.08863ZM11.5946 7.7711C11.3985 7.96757 11.1282 8.08863 10.8296 8.08863V8.48863C11.2388 8.48863 11.6097 8.32218 11.8777 8.05366L11.5946 7.7711ZM11.9117 7.00399C11.9117 7.30361 11.7907 7.57461 11.5946 7.77109L11.8777 8.05366C12.1457 7.78514 12.3117 7.41366 12.3117 7.00399H11.9117ZM11.5946 6.23689C11.7907 6.43337 11.9117 6.70438 11.9117 7.00399H12.3117C12.3117 6.59432 12.1457 6.22284 11.8777 5.95433L11.5946 6.23689Z"
							fill="#707070" mask="url(#path-1-outside-1_85_5458)" />
					</svg>
					<span>Wysyłka w 48h</span>
				</li>
				<li>
					<?php if ($product->is_in_stock()) : ?>
						<svg width="8" height="9" viewBox="0 0 8 9" fill="none" xmlns="http://www.w3.org/2000/svg">
							<circle cx="4.00024" cy="4.5" r="4" fill="#4DEC94" />
						</svg>
						<span>Produkt dostępny</span>
					<?php else : ?>
						<svg width="8" height="9" viewBox="0 0 8 9" fill="none" xmlns="http://www.w3.org/2000/svg">
							<circle cx="4.00024" cy="4.5" r="4" fill="#FF0000" />
						</svg>
						<span>Produkt niedostępny</span>
					<?php endif; ?>
				</li>
			</ul>

			<h1><?php the_title(); ?></h1>

			<div class="single-product-header-price">
				<?php
				// Display price with and without tax
				$price_excluding_tax = wc_get_price_excluding_tax($product);
				$price_including_tax = wc_get_price_including_tax($product);

				?>
				<?php if ($product->is_on_sale()) : ?>
					<div class="price-with-tax">
						<span class="price-label">Cena z VAT:</span>
						<del><?php echo wc_price(wc_get_price_including_tax($product, array('qty' => 1, 'price' => $product->get_regular_price()))); ?></del>
						<ins><?php echo wc_price($price_including_tax); ?></ins>
					</div>
					<div class="price-without-tax">
						<span class="price-label">Cena netto:</span>
						<del><?php echo wc_price(wc_get_price_excluding_tax($product, array('qty' => 1, 'price' => $product->get_regular_price()))); ?></del>
						<ins><?php echo wc_price($price_excluding_tax); ?></ins>
					</div>
				<?php else : ?>
					<div class="price-with-tax">
						<span class="price-label">Cena z VAT:</span>
						<?php echo wc_price($price_including_tax); ?>
					</div>
					<div class="price-without-tax">
						<span class="price-label">Cena netto:</span>
						<?php echo wc_price($price_excluding_tax); ?>
					</div>
				<?php endif; ?>
			</div>

			<?php

			if ($product && is_a($product, 'WC_Product') && $product->is_purchasable() && $product->is_in_stock()) {
				echo '<form class="cart" action="' . esc_url(apply_filters('woocommerce_add_to_cart_form_action', $product->get_permalink())) . '" method="post" enctype="multipart/form-data">';

				if ($product->is_type('simple')) {
					echo '<button type="submit" name="add-to-cart" value="' . esc_attr($product->get_id()) . '" class="single_add_to_cart_button galbud-button galbud-button--primary">Dodaj do koszyka</button>';
					woocommerce_quantity_input(array(), $product, true);
				}
				echo '</form>';
			} elseif ($product && is_a($product, 'WC_Product') && !$product->is_in_stock()) {
				echo '<div class="product-unavailable-message">Produkt nie jest obecnie dostępny</div>';
			}

			echo do_shortcode('[yith_wcwl_add_to_wishlist]');

			?>

		</div>
		<!-- AI TODO: display product main photo and gallery, it should be woocommerce style with slider for thumbnails and soom on main photo -->
		<div class="single-product-header-image">
			<div class="product-gallery">
				<?php
				// Get product gallery images
				$attachment_ids = $product->get_gallery_image_ids();
				$has_gallery = !empty($attachment_ids);
				$main_image_id = $product->get_image_id();

				// Main image with zoom
				echo '<div class="product-gallery-main">';
				echo '<div class="product-gallery-main-image zoom-container">';

				if ($main_image_id) {
					$image_data = wp_get_attachment_image_src($main_image_id, 'full');
					if ($image_data) {
						$image_url = $image_data[0]; // The URL is the first item in the returned array
						$width = $image_data[1];     // Width is the second item
						$height = $image_data[2];    // Height is the third item

						echo '<img src="' . $image_url . '" class="main-image">';
					}
				} else {
					// Use WooCommerce placeholder image if no product image exists
					$placeholder_url = wc_placeholder_img_src('full');
					echo '<img src="' . $placeholder_url . '" class="main-image" alt="' . esc_attr(get_the_title($product->get_id())) . '">';
				}

				echo '</div>';
				echo '</div>';

				// Thumbnails slider
				if ($has_gallery || $main_image_id || !$has_gallery) {
					echo '<div class="product-gallery-thumbs">';

					// Add main image to thumbnails
					if ($main_image_id) {
						$main_full = wp_get_attachment_image_src($main_image_id, 'full');
						$main_full_url = $main_full ? $main_full[0] : '';
						echo '<div class="product-gallery-thumb-item active">';
						echo wp_get_attachment_image($main_image_id, 'thumbnail', false, array('class' => 'thumb-image', 'data-full' => esc_url($main_full_url)));
						echo '</div>';
					} else {
						// Add placeholder thumbnail if no main image
						$placeholder_url = wc_placeholder_img_src('thumbnail');
						$placeholder_full = wc_placeholder_img_src('full');
						echo '<div class="product-gallery-thumb-item active">';
						echo '<img src="' . esc_url($placeholder_url) . '" class="thumb-image" data-full="' . esc_url($placeholder_full) . '" alt="' . esc_attr(get_the_title($product->get_id())) . '">';
						echo '</div>';
					}

					// Add gallery images to thumbnails
					foreach ($attachment_ids as $attachment_id) {
						$full = wp_get_attachment_image_src($attachment_id, 'full');
						$full_url = $full ? $full[0] : '';
						echo '<div class="product-gallery-thumb-item">';
						echo wp_get_attachment_image($attachment_id, 'thumbnail', false, array('class' => 'thumb-image', 'data-full' => esc_url($full_url)));
						echo '</div>';
					}

					echo '</div>';
				}
				?>
			</div>
		</div>
	</section>

</div>

<section class="section-title">
	<div class="section-title__container container">
		<h2 class="section-title__text">Szczegóły</h2>
	</div>
</section>

<div class="container">
	<div class="product-details">
		<div>
			<strong>Marka:</strong>
			<?php
			$terms = get_the_terms($product->get_id(), 'product_brand');
			if ($terms && !is_wp_error($terms)) {
				$brand_names = array();
				foreach ($terms as $term) {
					$brand_names[] = $term->name;
				}
				echo implode(', ', $brand_names);
			} else {
				echo '<p>Brak informacji o marce</p>';
			}
			?>
		</div>
		<div class="sizes">
			<strong>Wymiary</strong>
			<?php
			$length = $product->get_length();
			$width = $product->get_width();
			$height = $product->get_height();
			$weight = $product->get_weight();
			$dimension_unit = get_option('woocommerce_dimension_unit');
			$weight_unit = get_option('woocommerce_weight_unit');

			if ($length) {
				echo '<p><span>Dł:</span> ' . $length . ' <span>' . $dimension_unit . '</span></p>';
			}

			if ($width) {
				echo '<p><span>Szer:</span> ' . $width . ' <span>' . $dimension_unit . '</span></p>';
			}

			if ($height) {
				echo '<p><span>Wys:</span> ' . $height . ' <span>' . $dimension_unit . '</span></p>';
			}

			if ($weight) {
				echo '<p><span>Waga:</span> ' . $weight . ' <span>' . $weight_unit . '</span></p>';
			}

			if (!$length && !$width && !$height && !$weight) {
				echo '<p>Brak informacji o wymiarach</p>';
			}
			?>
		</div>

		<div>
			<strong>Przeznaczenie:</strong>
			<?php
			$purpose = get_field('product_purpose', $product->get_id());
			if ($purpose) {
				echo '<p>' . $purpose . '</p>';
			} else {
				echo '<p>Brak informacji o przeznaczeniu</p>';
			}
			?>
		</div>

	</div>
</div>


<section class="section-title">
	<div class="section-title__container container">
		<h2 class="section-title__text">Opis</h2>
	</div>
</section>

<div class="container">
	<div class="two-columns">
		<div>
			<h2><?php echo get_the_title($product->get_id()); ?></h2>
		</div>
		<div>
			<?php echo $product->get_description(); ?>
		</div>
	</div>
</div>

<?php
$wide_image = get_field('product_wide_image', $product->get_id());
if ($wide_image) : ?>
	<div class="container single-product-wide-image">
		<img src="<?php echo esc_url($wide_image['url']); ?>" alt="<?php echo esc_attr($wide_image['alt']); ?>">
	</div>
<?php endif; ?>

<section class="section-title">
	<div class="section-title__container container">
		<h2 class="section-title__text">Specyfikacja</h2>
	</div>
</section>

<div class="container">
	<table class="specyfication-table">
		<?php
		$specifications = get_field('product_specification', $product->get_id());
		if ($specifications) {
			foreach ($specifications as $spec) {
				echo '<tr>';
				echo '<td>' . esc_html($spec['name']) . '</td>';
				echo '<td>' . esc_html($spec['value']) . '</td>';
				echo '</tr>';
			}
		} else {
			echo '<tr><td colspan="2">Brak specyfikacji produktu</td></tr>';
		}
		?>
	</table>
</div>


<section class="section-title">
	<div class="section-title__container container">
		<h2 class="section-title__text">Opinie</h2>
	</div>
</section>

<div class="container">
	<?php
	// Display product reviews using the WooCommerce template
	comments_template('single-product-reviews.php');
	?>
</div>

<section class="section-title">
	<div class="section-title__container container">
		<h2 class="section-title__text">Podobne produkty</h2>
	</div>
</section>
<?php
// Get related products
$related_products = wc_get_related_products($product->get_id(), 4); // Get up to 8 related products

if ($related_products) :
?>
	<div class="container">
		<div class="related-products">
			<div class="products-grid">
				<?php
				// Loop through related products
				foreach ($related_products as $related_product_id) :
					// Set up the global product object for each related product
					global $post, $product;
					$post = get_post($related_product_id);
					setup_postdata($post);
					$product = wc_get_product($related_product_id);

					// Use the same template as product listing
					wc_get_template_part('content', 'product-listing');
				endforeach;

				// Reset post data
				wp_reset_postdata();
				?>
			</div>
		</div>
	</div>
<?php endif; ?>


<div class="container">
	<section class="home-contact-section"
		style="background-image: url('<?php echo get_template_directory_uri(); ?>/img/contact.jpg');">
		<h2 class="home-contact-section__title">Kontakt</h2>
		<p class="home-contact-section__text">Zapisz się do naszego newslettera i otrzymuj informacje na temat nowych
			produktów i specjalnych promocji.</p>
		<?php echo do_shortcode('[contact-form-7 id="61ab135" title="Home Page Formularz kontaktowy"]'); ?>
	</section>
</div>
<?php
get_footer();
