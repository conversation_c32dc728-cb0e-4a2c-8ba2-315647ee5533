<!DOCTYPE html>
<html <?php language_attributes(); ?>>

<head>
	<meta charset="<?php bloginfo('charset'); ?>">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="profile" href="https://gmpg.org/xfn/11">
	<?php wp_head(); ?>

</head>

<body <?php body_class(); ?>>
	<?php wp_body_open(); ?>

	<header class="header">
		<div class="container">
			<div class="top">
				<div class="logo">
					<?php if (has_custom_logo()) :
						the_custom_logo(); ?>
					<?php endif; ?>

					<?php
					$description = get_bloginfo('description', 'display');
					if ($description || is_customize_preview()) :
					?>
						<p
							class="site-description"><?php echo $description; // phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped
														?></p>
					<?php endif; ?>

				</div>

			</div>

			<div class="header-search">
				<form role="search" method="get" id="searchform" class="searchform"
					action="<?php echo esc_url(home_url('/')); ?>">
					<input type="text" value="<?php echo get_search_query(); ?>" name="s" id="s" placeholder="Szukaj" />
					<input type="hidden" name="post_type" value="product" />
					<button type="submit" id="searchsubmit" title="Wyszukaj">
						<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
							stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
							class="lucide lucide-search">
							<circle cx="11" cy="11" r="8" />
							<path d="m21 21-4.3-4.3" />
						</svg>
					</button>

				</form>
			</div>

			<div class="header-icons">
				<a href="#" class="toggle-search">
					<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><!--!Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free Copyright 2025 Fonticons, Inc.-->
						<path d="M416 208c0 45.9-14.9 88.3-40 122.7L502.6 457.4c12.5 12.5 12.5 32.8 0 45.3s-32.8 12.5-45.3 0L330.7 376c-34.4 25.2-76.8 40-122.7 40C93.1 416 0 322.9 0 208S93.1 0 208 0S416 93.1 416 208zM208 352a144 144 0 1 0 0-288 144 144 0 1 0 0 288z" />
					</svg>
				</a>
				<a href="/my-account/">
					<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><!--!Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free Copyright 2025 Fonticons, Inc.-->
						<path d="M304 128a80 80 0 1 0 -160 0 80 80 0 1 0 160 0zM96 128a128 128 0 1 1 256 0A128 128 0 1 1 96 128zM49.3 464l349.5 0c-8.9-63.3-63.3-112-129-112l-91.4 0c-65.7 0-120.1 48.7-129 112zM0 482.3C0 383.8 79.8 304 178.3 304l91.4 0C368.2 304 448 383.8 448 482.3c0 16.4-13.3 29.7-29.7 29.7L29.7 512C13.3 512 0 498.7 0 482.3z" />
					</svg> </a>
				<a href="/cart/">
					<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512"><!--!Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free Copyright 2025 Fonticons, Inc.-->
						<path d="M0 24C0 10.7 10.7 0 24 0L69.5 0c22 0 41.5 12.8 50.6 32l411 0c26.3 0 45.5 25 38.6 50.4l-41 152.3c-8.5 31.4-37 53.3-69.5 53.3l-288.5 0 5.4 28.5c2.2 11.3 12.1 19.5 23.6 19.5L488 336c13.3 0 24 10.7 24 24s-10.7 24-24 24l-288.3 0c-34.6 0-64.3-24.6-70.7-58.5L77.4 54.5c-.7-3.8-4-6.5-7.9-6.5L24 48C10.7 48 0 37.3 0 24zM128 464a48 48 0 1 1 96 0 48 48 0 1 1 -96 0zm336-48a48 48 0 1 1 0 96 48 48 0 1 1 0-96z" />
					</svg> <?php echo display_woocommerce_cart_item_count(); ?>
				</a>
				<div>
					<div class="mobile-menu-toggle" style="position: relative;">
						<img src="<?php echo get_template_directory_uri(); ?>/img/menu.svg" alt="Menu" class="toggle-dropdown-menu">
						<div class="mobile-menu-container">
							<button class="toggle-dropdown-menu">
								<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
									<line x1="2.82843" y1="2.48444" x2="21.0317" y2="20.6877" stroke="black" stroke-width="4" stroke-linecap="round" />
									<line x1="2.96826" y1="20.6874" x2="21.1715" y2="2.48414" stroke="black" stroke-width="4" stroke-linecap="round" />
								</svg>
							</button>
							<?php display_mobile_menu(); ?>
						</div>
					</div>
				</div>
			</div>
		</div>


	</header>
	<?php display_my_menu(); ?>

	<div class="container">
		<p>
			<?php
			if (function_exists('wc_print_notices')) {
				wc_print_notices();
			}
			?>
		</p>
	</div>