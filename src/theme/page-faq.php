<?php get_header(); ?>

<div class="container faq-page">
    <div class="section-title">
        <div class="section-title__container">
            <?php if (get_field('faq_title')) : ?>
                <h1 class="section-title__text"><?php echo get_field('faq_title'); ?></h1>
            <?php endif; ?>
        </div>
    </div>

    <?php if (have_rows('faq_items')) : ?>
        <div class="faq-accordion">
            <?php while (have_rows('faq_items')) : the_row(); ?>
                <div class="faq-item">
                    <div class="faq-question">
                        <?php echo get_sub_field('question'); ?>
                        <span class="faq-toggle">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <line x1="12" y1="5" x2="12" y2="19"></line>
                                <line x1="5" y1="12" x2="19" y2="12" class="vertical-line"></line>
                            </svg>
                        </span>
                    </div>
                    <div class="faq-answer">
                        <?php echo get_sub_field('answer'); ?>
                    </div>
                </div>
            <?php endwhile; ?>
        </div>
    <?php endif; ?>
</div>

<style>
    .faq-page {
        padding-block: 4rem;
    }

    .faq-accordion {
        max-width: 900px;
        margin: 0 auto;
    }

    .faq-item {
        border-bottom: 1px solid var(--stroke);
        margin-bottom: 0.5rem;
    }

    .faq-question {
        padding: 1.5rem 1rem;
        cursor: pointer;
        position: relative;
        font-size: 1.125rem;
        font-weight: 500;
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: var(--black);
        transition: color 0.3s ease;
    }

    .faq-question:hover {
        color: var(--primary-color);
    }

    .faq-toggle {
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 24px;
        height: 24px;
        transition: transform 0.3s ease;
    }

    .faq-question.active .faq-toggle {
        transform: rotate(45deg);
    }

    .faq-question.active {
        color: var(--primary-color);
    }

    .faq-answer {
        padding: 0 1rem;
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease, padding 0.3s ease;
        line-height: 1.6;
    }

    .faq-answer.active {
        padding: 0 1rem 1.5rem 1rem;
        max-height: 1000px;
    }

    .faq-answer h3 {
        margin-top: 1rem;
        margin-bottom: 0.5rem;
        font-size: 1.1rem;
        font-weight: 600;
    }

    .faq-answer p {
        margin-bottom: 1rem;
    }

    .faq-answer a {
        color: var(--primary-color);
        text-decoration: none;
    }

    .faq-answer a:hover {
        text-decoration: underline;
    }

    .faq-answer ul,
    .faq-answer ol {
        margin-left: 1.5rem;
        margin-bottom: 1rem;
    }
</style>

<script>
    jQuery(document).ready(function($) {
        $('.faq-question').on('click', function() {
            $(this).toggleClass('active');
            $(this).next('.faq-answer').toggleClass('active');

            // Zamknij inne otwarte pytania (opcjonalnie)
            $('.faq-question').not(this).removeClass('active');
            $('.faq-answer').not($(this).next('.faq-answer')).removeClass('active');
        });

        // Otwórz pierwsze pytanie automatycznie
        $('.faq-question:first').addClass('active');
        $('.faq-answer:first').addClass('active');
    });
</script>

<?php get_footer(); ?>