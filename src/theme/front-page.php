<?php
get_header();

/**
 * Define allowed HTML tags for wp_kses
 */
$allowed_html = array(
	'a'      => array(
		'href'   => array(),
		'title'  => array(),
		'class'  => array(),
		'target' => array(),
		'rel'    => array(),
	),
	'br'     => array(),
	'em'     => array(),
	'strong' => array(),
	'p'      => array(
		'class' => array(),
	),
	'h1'     => array(
		'class' => array(),
	),
	'h2'     => array(
		'class' => array(),
	),
	'h3'     => array(
		'class' => array(),
	),
	'h4'     => array(
		'class' => array(),
	),
	'h5'     => array(
		'class' => array(),
	),
	'h6'     => array(
		'class' => array(),
	),
	'ul'     => array(
		'class' => array(),
	),
	'ol'     => array(
		'class' => array(),
	),
	'li'     => array(
		'class' => array(),
	),
	'span'   => array(
		'class' => array(),
	),
	'div'    => array(
		'class' => array(),
	),
	'img'    => array(
		'src'    => array(),
		'alt'    => array(),
		'class'  => array(),
		'width'  => array(),
		'height' => array(),
	),
);

// Check if ACF is active and if we have flexible content fields
if (function_exists('have_rows') && have_rows('front_page_sections')) {
	// Loop through the flexible content layouts
	while (have_rows('front_page_sections')) {
		the_row();

		// Get the current layout
		$layout = get_row_layout();

		// Construct the path to the template part
		$template_part_path = 'template-parts/front-page/' . $layout;

		// Pass $allowed_html to the template part
		set_query_var('allowed_html', $allowed_html);
		set_query_var('section_data', get_row(true)); // Pass all row data

		// Load the template part for the current layout
		get_template_part($template_part_path);
	}
}

get_footer();
