<?php
/**
 * The template for displaying search results
 */

get_header();

// Get the search query
$search_query = get_search_query();
?>

<div class="container search-results-container">
    <header class="search-header">
        <h1 class="search-title">
            <?php
            printf(
                /* translators: %s: search query */
                esc_html__('Wyniki wyszukiwania dla: %s', 'galbud'),
                '<span>' . esc_html($search_query) . '</span>'
            );
            ?>
        </h1>
    </header>

    <div class="row listing">
        <div class="col-md-3">
            <?php get_sidebar('woocommerce'); ?>
        </div>
        <div class="col-md-9">
            <?php
            // Check if we're searching for products
            if (isset($_GET['post_type']) && $_GET['post_type'] === 'product') {
                // This is a product search
                
                // Set up the product query
                $paged = (get_query_var('paged')) ? get_query_var('paged') : 1;
                $args = array(
                    'post_type' => 'product',
                    's' => $search_query,
                    'posts_per_page' => 12,
                    'paged' => $paged,
                );
                
                $product_query = new WP_Query($args);
                
                if ($product_query->have_posts()) {
                    // Start the product loop
                    woocommerce_product_loop_start();
                    
                    while ($product_query->have_posts()) {
                        $product_query->the_post();
                        wc_get_template_part('content', 'product-listing');
                    }
                    
                    woocommerce_product_loop_end();
                    
                    // Pagination
                    echo '<div class="pagination">';
                    echo paginate_links(array(
                        'total' => $product_query->max_num_pages,
                        'current' => max(1, get_query_var('paged')),
                    ));
                    echo '</div>';
                    
                    wp_reset_postdata();
                } else {
                    // No products found
                    echo '<p>' . esc_html__('Nie znaleziono produktów pasujących do Twojego wyszukiwania.', 'galbud') . '</p>';
                }
            } else {
                // Regular search (posts, pages, etc.)
                if (have_posts()) {
                    while (have_posts()) {
                        the_post();
                        ?>
                        <article id="post-<?php the_ID(); ?>" <?php post_class('search-result-item'); ?>>
                            <header class="entry-header">
                                <?php the_title(sprintf('<h2 class="entry-title"><a href="%s" rel="bookmark">', esc_url(get_permalink())), '</a></h2>'); ?>
                            </header>

                            <div class="entry-summary">
                                <?php the_excerpt(); ?>
                            </div>

                            <footer class="entry-footer">
                                <a href="<?php echo esc_url(get_permalink()); ?>" class="galbud-button galbud-button--primary">
                                    <?php esc_html_e('Czytaj więcej', 'galbud'); ?>
                                </a>
                            </footer>
                        </article>
                        <?php
                    }
                    
                    // Pagination
                    the_posts_pagination(array(
                        'mid_size' => 2,
                        'prev_text' => __('Poprzednia', 'galbud'),
                        'next_text' => __('Następna', 'galbud'),
                    ));
                } else {
                    ?>
                    <div class="no-results">
                        <h2><?php esc_html_e('Nie znaleziono wyników', 'galbud'); ?></h2>
                        <p><?php esc_html_e('Przepraszamy, ale nie znaleziono wyników pasujących do Twojego wyszukiwania. Spróbuj ponownie z innymi słowami kluczowymi.', 'galbud'); ?></p>
                    </div>
                    <?php
                }
            }
            ?>
        </div>
    </div>
</div>

<?php
get_footer();
