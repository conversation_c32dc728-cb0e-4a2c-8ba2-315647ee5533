<?php
defined( 'ABSPATH' ) || exit;

if ( WC()->cart->is_empty() ) {
	return;
}
?>

<div class="cart_totals <?php if ( WC()->customer->has_calculated_shipping() ) echo 'calculated_shipping'; ?>">

	<h2><?php esc_html_e( 'Podsumowanie zamówienia', 'woocommerce' ); ?></h2>

	<?php do_action( 'woocommerce_before_cart_totals' ); ?>

	<table cellspacing="0" class="shop_table shop_table_responsive">
		<tr class="cart-subtotal">
			<th><?php esc_html_e( 'Subtotal', 'woocommerce' ); ?></th>
			<td data-title="<?php esc_attr_e( 'Subtotal', 'woocommerce' ); ?>">
				<?php wc_cart_totals_subtotal_html(); ?>
			</td>
		</tr>

		<tr class="order-total">
			<th><?php esc_html_e( 'Total', 'woocommerce' ); ?></th>
			<td data-title="<?php esc_attr_e( 'Total', 'woocommerce' ); ?>">
				<?php wc_cart_totals_order_total_html(); ?>
			</td>
		</tr>
	</table>

	<?php do_action( 'woocommerce_after_cart_totals' ); ?>

	<div class="wc-proceed-to-checkout">
		<?php do_action( 'woocommerce_proceed_to_checkout' ); ?>
	</div>

</div>
