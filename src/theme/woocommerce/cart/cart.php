<?php
defined('ABSPATH') || exit;

do_action('woocommerce_before_cart');
?>
<div class="cart-page">
	<h1>Twój k<PERSON><PERSON></h1>

	<div class="woocommerce-cart-wrapper">

		<form class="woocommerce-cart-form" action="<?php echo esc_url(wc_get_cart_url()); ?>" method="post">
			<table class="shop_table shop_table_responsive cart woocommerce-cart-form__contents" cellspacing="0">
				<thead>
					<tr>
						<th class="product-thumbnail">Produkt</th>
						<th class="product-name">Nazwa</th>
						<th class="product-price">Cena</th>
						<th class="product-quantity"><PERSON><PERSON><PERSON><PERSON></th>
						<th class="product-subtotal">Łącznie</th>
						<th class="product-remove">&nbsp;</th>

					</tr>
				</thead>
				<tbody>
					<?php
					foreach (WC()->cart->get_cart() as $cart_item_key => $cart_item) {
						$_product   = $cart_item['data'];
						$product_id = $cart_item['product_id'];

						if ($_product && $_product->exists() && $cart_item['quantity'] > 0) :
							$product_permalink = $_product->is_visible() ? $_product->get_permalink($cart_item) : '';
					?>
							<tr
								class="woocommerce-cart-form__cart-item <?php echo esc_attr(apply_filters('woocommerce_cart_item_class', 'cart_item', $cart_item, $cart_item_key)); ?>">


								<td class="product-thumbnail">
									<?php
									$thumbnail = $_product->get_image();
									echo $thumbnail;
									?>
								</td>

								<td class="product-name" data-title="Product">
									<?php
									echo $_product->get_name();
									?>
								</td>

								<td class="product-price" data-title="Price">
									<?php
									echo WC()->cart->get_product_price($_product);
									?>
								</td>

								<td class="product-quantity" data-title="Quantity">
									<?php
									if ($_product->is_sold_individually()) {
										echo '1';
									} else {
										woocommerce_quantity_input([
											'input_name'  => "cart[{$cart_item_key}][qty]",
											'input_value' => $cart_item['quantity'],
											'max_value'   => $_product->get_max_purchase_quantity(),
											'min_value'   => '0',
										], $_product); // Pass the product object here

									}
									?>
								</td>

								<td class="product-subtotal" data-title="Total">
									<?php
									echo WC()->cart->get_product_subtotal($_product, $cart_item['quantity']);
									?>
								</td>

								<td class="product-remove">
									<?php
									echo apply_filters('woocommerce_cart_item_remove_link', sprintf(
										'<a href="%s"  aria-label="%s" data-product_id="%s" data-product_sku="%s"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none"><path stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.5 1.5h7M1.5 5h21m-2.333 0-.819 12.273c-.122 1.84-.184 2.762-.581 3.46a3.5 3.5 0 0 1-1.515 1.417c-.723.35-1.646.35-3.491.35h-3.522c-1.845 0-2.768 0-3.49-.35a3.5 3.5 0 0 1-1.516-1.417c-.397-.698-.459-1.62-.581-3.46L3.833 5m5.834 5.25v5.833m4.666-5.833v5.833"/></svg></a>',
										esc_url(wc_get_cart_remove_url($cart_item_key)),
										esc_html__('Usuń ten produkt', 'woocommerce'),
										esc_attr($product_id),
										esc_attr($_product->get_sku())
									), $cart_item_key);
									?>
								</td>
							</tr>
					<?php
						endif;
					}
					?>

					<tr>
						<td colspan="6" class="actions">

							<button type="submit" class="button" name="update_cart"
								value="<?php esc_attr_e('Zaktualizuj koszyk', 'woocommerce'); ?>">
								<?php esc_html_e('Zaktualizuj koszyk', 'woocommerce'); ?>
							</button>

							<?php do_action('woocommerce_cart_actions'); ?>

							<?php wp_nonce_field('woocommerce-cart', 'woocommerce-cart-nonce'); ?>
						</td>
					</tr>
				</tbody>
			</table>
		</form>

		<div class="cart-collaterals">
			<?php
			do_action('woocommerce_cart_collaterals');
			?>
		</div>

		<?php do_action('woocommerce_after_cart'); ?>

	</div>

	<div class="related-products-section">
		<?php galbud_display_cart_related_products(); ?>
	</div>
</div>