<?php global $product; ?>
<div <?php wc_product_class('product-listing-item', $product); ?>>
	<style>
		.availability-indicator {
			position: absolute;
			top: 10px;
			right: 10px;
			z-index: 10;
			width: 12px;
			height: 12px;
			border-radius: 50%;
			cursor: help;
		}

		.availability-indicator svg {
			width: 12px;
			height: 12px;
		}
	</style>
	<?php
	$name = $product->get_name();
	$sku = $product->get_sku();
	$link = get_permalink();

	// Get prices with and without VAT
	$price_excluding_tax = wc_get_price_excluding_tax($product);
	$price_including_tax = wc_get_price_including_tax($product);

	// Get product image or use placeholder
	$image_id = $product->get_image_id();
	if ($image_id) {
		$image_data = wp_get_attachment_image_src($image_id, 'full');
		$image = $image_data ? $image_data[0] : wc_placeholder_img_src();
	} else {
		$image = wc_placeholder_img_src();
	}

	?>
	<div class="badges">
		<?php
		$product = wc_get_product(get_the_ID());
		if ($product && $product->is_featured()) {
			echo '<span class="badge badge-featured">Polecamy</span>';
		}

		if ($product && $product->is_on_sale()) {
			$regular_price = $product->get_regular_price();
			$sale_price    = $product->get_sale_price();
			$percentage    = round((($regular_price - $sale_price) / $regular_price) * 100);
			echo '<span class="badge badge-promo">-' . $percentage . '%</span>';
		}

		// Add availability indicator only for out of stock products
		if (!$product->is_in_stock()) {
			$availability_text = 'Produkt niedostępny';
		?>
			<span class="availability-indicator out-of-stock" title="<?php echo $availability_text; ?>">
				<svg width="8" height="9" viewBox="0 0 8 9" fill="none" xmlns="http://www.w3.org/2000/svg">
					<circle cx="4.00024" cy="4.5" r="4" fill="#FF0000" />
				</svg>
			</span>
		<?php } ?>
	</div>

	<a href="<?php echo $link; ?>" class="thumbnail-link">
		<?php // echo $image;
		?>
		<img src="<?php echo $image; ?>" alt="">
	</a>

	<div class="product-content-wrapper">
		<h2 class="name"><?php echo $name; ?></h2>
		<span class="sku">Nr. <?php echo $sku; ?></span>



		<div class="product-listing-price">
			<?php if ($product->is_on_sale()) : ?>
				<div class="price-with-tax">
					<span class="price-label">Cena z VAT:</span>
					<del><?php echo wc_price(wc_get_price_including_tax($product, array('qty' => 1, 'price' => $product->get_regular_price()))); ?></del>
					<ins><?php echo wc_price($price_including_tax); ?></ins>
				</div>
				<div class="price-without-tax">
					<span class="price-label">Cena netto:</span>
					<del><?php echo wc_price(wc_get_price_excluding_tax($product, array('qty' => 1, 'price' => $product->get_regular_price()))); ?></del>
					<ins><?php echo wc_price($price_excluding_tax); ?></ins>
				</div>
			<?php else : ?>
				<div class="price-with-tax">
					<span class="price-label">Cena z VAT:</span>
					<?php echo wc_price($price_including_tax); ?>
				</div>
				<div class="price-without-tax">
					<span class="price-label">Cena netto:</span>
					<?php echo wc_price($price_excluding_tax); ?>
				</div>
			<?php endif; ?>
		</div>

		<a href="<?php echo $link; ?>" class="galbud-button galbud-button--primary">Zobacz produkt</a>
	</div>
</div>