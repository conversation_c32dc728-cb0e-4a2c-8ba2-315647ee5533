<?php
defined('ABSPATH') || exit;

get_header('shop');
?>

<section class="main-slider">
	<div class="container">
		<div class="main-slider__row">
			<div class="main-slider__col">
				<div>
					<?php if (apply_filters('woocommerce_show_page_title', true)) : ?>
						<h1 class="main-slider__title"> <?php woocommerce_page_title(); ?></h1>
					<?php endif; ?>
					<p class="main-slider__description"> <?php do_action('woocommerce_archive_description'); ?>
					</p>
				</div>
			</div>
			<div class="main-slider__col">
				<?php
				// Get the current category object
				$category = get_queried_object();
				$thumbnail_id = 0;
				$image_url = '';
				$category_name = '';

				if (is_product_category() && $category instanceof WP_Term) {
					// Get the category thumbnail ID
					$thumbnail_id = get_term_meta($category->term_id, 'thumbnail_id', true);
					$category_name = $category->name;
				}

				if ($thumbnail_id) {
					// Get the image URL
					$image_url = wp_get_attachment_url($thumbnail_id);
				}

				// If no image URL, get the placeholder
				if (! $image_url) {
					$image_url = wc_placeholder_img_src();
				}

				// Display the image
				if ($image_url) {
					echo '<img src="' . esc_url($image_url) . '" class="main-slider__main-image" alt="' . esc_attr($category_name) . '">';
				}
				?>
			</div>
		</div>
</section>


<div class="container">
	<div class="row listing">
		<div class="col-md-3">
			<?php get_sidebar('woocommerce'); ?>
		</div>
		<div class="col-md-9">
			<?php
			do_action('woocommerce_before_main_content');
			?>
			<header class="woocommerce-products-header">

			</header>

			<?php
			if (woocommerce_product_loop()) {

				do_action('woocommerce_before_shop_loop');

				woocommerce_product_loop_start();

				if (wc_get_loop_prop('total')) {
					while (have_posts()) {
						the_post();
						do_action('woocommerce_shop_loop');

						wc_get_template_part('content', 'product-listing');
					}
				}

				woocommerce_product_loop_end();

				do_action('woocommerce_after_shop_loop');
			} else {

				do_action('woocommerce_no_products_found');
			}
			?>
		</div>
	</div>

</div>
<?php
do_action('woocommerce_after_main_content');

get_footer('shop');
