<?php

/**
 * Thankyou page
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/checkout/thankyou.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see https://woocommerce.com/document/template-structure/
 * @package WooCommerce\Templates
 * @version 8.1.0
 *
 * @var WC_Order $order
 */

defined('ABSPATH') || exit;
?>

<div class="woocommerce-order">

	<div class="container">
		<img src="<?php echo get_template_directory_uri(); ?>/img/tp.jpg" class="woocommerce-order-image" alt="">

		<?php if ($order) : ?>
			<div class="order-box">
				<?php do_action('woocommerce_before_thankyou', $order->get_id()); ?>
				<h2>
					<?php esc_html_e('<PERSON>am<PERSON><PERSON>ie nr: ', 'woocommerce'); ?>
					<strong><?php echo $order->get_order_number(); // phpcs:ignore WordPress.Security.EscapeOutput.OutputNotEscaped 
							?></strong>
				</h2>

				<div class="current-status">
					<svg width="84" height="84" viewBox="0 0 84 84" fill="none" xmlns="http://www.w3.org/2000/svg">
						<path d="M31.5 38.5L42 49L77 14M56 10.5H27.3C21.4194 10.5 18.4792 10.5 16.2331 11.6444C14.2574 12.6511 12.6511 14.2574 11.6444 16.2331C10.5 18.4792 10.5 21.4194 10.5 27.3V56.7C10.5 62.5807 10.5 65.5207 11.6444 67.767C12.6511 69.7427 14.2574 71.3489 16.2331 72.3555C18.4792 73.5 21.4194 73.5 27.3 73.5H56.7C62.5807 73.5 65.5207 73.5 67.767 72.3555C69.7428 71.3489 71.3489 69.7427 72.3555 67.767C73.5 65.5207 73.5 62.5807 73.5 56.7V42" stroke="#01A0E2" stroke-width="6" stroke-linecap="round" stroke-linejoin="round" />
					</svg>

					<div>
						<h3>W trakcie realizacji</h3>
						<p>Zaplanowano - Środa 24.08.2025, 8:00 - 18:00</p>
					</div>
				</div>

				<ul class="order-statuses">
					<li class="current">
						<p>Zamówienie w przygotowaniu</p>
						<span>Otrzymaliśmy Twoje zamówienie i wkrótce przystąpimy do jego realizacji</span>
					</li>
					<li>Pakowanie zamówienia</li>
					<li>W drodze do punktu odbioru</li>
					<li>Gotowe do odbioru</li>
					<li>Odebrane</li>
				</ul>
			</div>

		<?php endif; ?>

	</div>
</div>