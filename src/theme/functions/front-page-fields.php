<?php

/**
 * Custom ACF fields for the front page
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Get all Contact Form 7 forms as an array for ACF select field
 *
 * @return array Array of form IDs and titles
 */
function galbud_get_contact_form_7_forms()
{
    $forms = array('' => 'W<PERSON>bierz formularz');

    // Check if Contact Form 7 is active
    if (function_exists('wpcf7_contact_form')) {
        // Get all forms
        $args = array(
            'post_type' => 'wpcf7_contact_form',
            'posts_per_page' => -1,
        );

        $cf7_forms = get_posts($args);

        if ($cf7_forms) {
            foreach ($cf7_forms as $form) {
                $forms[$form->ID] = $form->post_title;
            }
        }
    }

    return $forms;
}

/**
 * Validate flexible content to allow only one instance of certain layouts
 *
 * @param bool $valid Whether the value is valid
 * @param mixed $value The field value
 * @return bool|string
 */
function galbud_validate_front_page_flexible_content($valid, $value)
{
    // If not valid already, return
    if (!$valid) {
        return $valid;
    }

    // If no value, return valid
    if (empty($value)) {
        return $valid;
    }

    // Count how many of each layout type are used
    $categories_slider_count = 0;
    $home_contact_section_count = 0;
    $product_slider_count = 0;

    foreach ($value as $layout) {
        if ($layout['acf_fc_layout'] === 'categories_slider') {
            $categories_slider_count++;
        }
        if ($layout['acf_fc_layout'] === 'home_contact_section') {
            $home_contact_section_count++;
        }
        if ($layout['acf_fc_layout'] === 'product_slider') {
            $product_slider_count++;
        }
    }

    // If more than one categories_slider layout is used, return error message
    if ($categories_slider_count > 1) {
        return 'Można dodać tylko jeden slider kategorii produktów.';
    }

    // If more than one home_contact_section layout is used, return error message
    if ($home_contact_section_count > 1) {
        return 'Można dodać tylko jedną sekcję kontaktową.';
    }

    // If more than one product_slider layout is used, return error message
    if ($product_slider_count > 1) {
        return 'Można dodać tylko jeden slider produktów.';
    }

    return $valid;
}

/**
 * Register ACF fields for the front page if ACF is active
 */
function galbud_register_front_page_acf_fields()
{
    // Check if ACF is active
    if (!function_exists('acf_add_local_field_group')) {
        return;
    }

    // Add validation filter for flexible content field
    add_filter('acf/validate_value/key=field_front_page_sections', 'galbud_validate_front_page_flexible_content', 10, 2);

    // Front page flexible content field group
    acf_add_local_field_group(array(
        'key' => 'group_front_page_sections',
        'title' => 'Sekcje strony głównej',
        'fields' => array(
            array(
                'key' => 'field_front_page_sections',
                'label' => 'Sekcje',
                'name' => 'front_page_sections',
                'type' => 'flexible_content',
                'instructions' => 'Dodaj sekcje do strony głównej',
                'required' => 0,
                'layouts' => array(
                    'layout_main_header' => array(
                        'key' => 'layout_main_header',
                        'name' => 'main_header',
                        'label' => 'Główny nagłówek',
                        'display' => 'block',
                        'sub_fields' => array(
                            array(
                                'key' => 'field_main_header_title',
                                'label' => 'Tytuł',
                                'name' => 'title',
                                'type' => 'text',
                                'instructions' => 'Wprowadź główny tytuł',
                                'required' => 1,
                                'default_value' => 'Oferta sezonowa!',
                            ),
                            array(
                                'key' => 'field_main_header_description',
                                'label' => 'Opis',
                                'name' => 'description',
                                'type' => 'textarea',
                                'instructions' => 'Wprowadź krótki opis',
                                'required' => 0,
                                'default_value' => 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Vestibulum bibendum lacus a rhoncus sagittis.',
                                'rows' => 3,
                                'new_lines' => 'br',
                            ),
                            array(
                                'key' => 'field_main_header_subtitle',
                                'label' => 'Podtytuł',
                                'name' => 'subtitle',
                                'type' => 'text',
                                'instructions' => 'Wprowadź podtytuł',
                                'required' => 0,
                                'default_value' => 'Choinka 120cm - 150cm',
                            ),
                            array(
                                'key' => 'field_main_header_button_text',
                                'label' => 'Tekst przycisku',
                                'name' => 'button_text',
                                'type' => 'text',
                                'instructions' => 'Wprowadź tekst przycisku',
                                'required' => 0,
                                'default_value' => 'Zobacz więcej',
                            ),
                            array(
                                'key' => 'field_main_header_button_link',
                                'label' => 'Link przycisku',
                                'name' => 'button_link',
                                'type' => 'url',
                                'instructions' => 'Wprowadź link przycisku',
                                'required' => 0,
                                'default_value' => '#',
                            ),
                            array(
                                'key' => 'field_main_header_image',
                                'label' => 'Obraz',
                                'name' => 'image',
                                'type' => 'image',
                                'instructions' => 'Wybierz obraz głównego nagłówka',
                                'required' => 1,
                                'return_format' => 'url',
                                'preview_size' => 'medium',
                                'library' => 'all',
                            ),
                        ),
                    ),
                    'layout_showcase_products' => array(
                        'key' => 'layout_showcase_products',
                        'name' => 'showcase_products',
                        'label' => 'Wyróżnione produkty',
                        'display' => 'block',
                        'sub_fields' => array(
                            array(
                                'key' => 'field_showcase_section_title',
                                'label' => 'Tytuł sekcji',
                                'name' => 'section_title',
                                'type' => 'text',
                                'instructions' => 'Wprowadź tytuł sekcji (opcjonalnie)',
                                'required' => 0,
                                'default_value' => 'Specjalnie dla Ciebie',
                            ),
                            array(
                                'key' => 'field_showcase_products',
                                'label' => 'Wybierz produkty',
                                'name' => 'products',
                                'type' => 'relationship',
                                'instructions' => 'Wybierz dokładnie dwa produkty do wyświetlenia (wymagane)',
                                'required' => 1,
                                'post_type' => array(
                                    0 => 'product',
                                ),
                                'taxonomy' => '',
                                'filters' => array(
                                    0 => 'search',
                                ),
                                'elements' => array(
                                    0 => 'featured_image',
                                ),
                                'min' => 2,
                                'max' => 2,
                                'return_format' => 'id',
                            ),
                        ),
                    ),
                    'layout_promo_section' => array(
                        'key' => 'layout_promo_section',
                        'name' => 'promo_section',
                        'label' => 'Sekcja promocyjna',
                        'display' => 'block',
                        'sub_fields' => array(
                            array(
                                'key' => 'field_promo_background_image',
                                'label' => 'Obraz tła',
                                'name' => 'background_image',
                                'type' => 'image',
                                'instructions' => 'Wybierz obraz tła dla sekcji promocyjnej',
                                'required' => 1,
                                'return_format' => 'url',
                                'preview_size' => 'medium',
                                'library' => 'all',
                            ),
                            array(
                                'key' => 'field_promo_discount',
                                'label' => 'Zniżka',
                                'name' => 'discount',
                                'type' => 'text',
                                'instructions' => 'Wprowadź wartość zniżki (np. -20%)',
                                'required' => 0,
                                'default_value' => '-20%',
                            ),
                            array(
                                'key' => 'field_promo_title',
                                'label' => 'Tytuł',
                                'name' => 'title',
                                'type' => 'text',
                                'instructions' => 'Wprowadź tytuł sekcji promocyjnej',
                                'required' => 1,
                                'default_value' => 'Lorem ipsum dolor sit amet',
                            ),
                            array(
                                'key' => 'field_promo_text',
                                'label' => 'Tekst',
                                'name' => 'text',
                                'type' => 'textarea',
                                'instructions' => 'Wprowadź tekst sekcji promocyjnej',
                                'required' => 0,
                                'default_value' => 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
                                'rows' => 3,
                                'new_lines' => 'br',
                            ),
                            array(
                                'key' => 'field_promo_button_text',
                                'label' => 'Tekst przycisku',
                                'name' => 'button_text',
                                'type' => 'text',
                                'instructions' => 'Wprowadź tekst przycisku',
                                'required' => 0,
                                'default_value' => 'Zobacz produkt',
                            ),
                            array(
                                'key' => 'field_promo_button_link',
                                'label' => 'Link przycisku',
                                'name' => 'button_link',
                                'type' => 'url',
                                'instructions' => 'Wprowadź link przycisku',
                                'required' => 0,
                                'default_value' => '#',
                            ),
                        ),
                    ),
                    'layout_section_title' => array(
                        'key' => 'layout_section_title',
                        'name' => 'section_title',
                        'label' => 'Nagłówek sekcji',
                        'display' => 'block',
                        'sub_fields' => array(
                            array(
                                'key' => 'field_section_title_text',
                                'label' => 'Tytuł sekcji',
                                'name' => 'title',
                                'type' => 'text',
                                'instructions' => 'Wprowadź tytuł sekcji',
                                'required' => 1,
                                'default_value' => 'Tytuł sekcji',
                            ),
                            array(
                                'key' => 'field_section_title_subtitle',
                                'label' => 'Podtytuł sekcji',
                                'name' => 'subtitle',
                                'type' => 'textarea',
                                'instructions' => 'Wprowadź podtytuł sekcji (opcjonalnie)',
                                'required' => 0,
                                'default_value' => '',
                                'rows' => 2,
                                'new_lines' => 'br',
                            ),
                        ),
                    ),
                    'layout_categories_slider' => array(
                        'key' => 'layout_categories_slider',
                        'name' => 'categories_slider',
                        'label' => 'Slider kategorii produktów',
                        'display' => 'block',
                        'sub_fields' => array(
                            array(
                                'key' => 'field_categories_slider_categories',
                                'label' => 'Wybierz kategorie produktów',
                                'name' => 'categories',
                                'type' => 'taxonomy',
                                'instructions' => 'Wybierz kategorie produktów do wyświetlenia w sliderze',
                                'required' => 1,
                                'taxonomy' => 'product_cat',
                                'field_type' => 'multi_select',
                                'allow_null' => 0,
                                'add_term' => 0,
                                'save_terms' => 0,
                                'load_terms' => 0,
                                'return_format' => 'id',
                                'multiple' => 1,
                            ),
                        ),
                    ),
                    'layout_tabs_section' => array(
                        'key' => 'layout_tabs_section',
                        'name' => 'tabs_section',
                        'label' => 'Sekcja z zakładkami',
                        'display' => 'block',
                        'sub_fields' => array(
                            array(
                                'key' => 'field_tabs_section_tabs',
                                'label' => 'Zakładki',
                                'name' => 'tabs',
                                'type' => 'repeater',
                                'instructions' => 'Dodaj zakładki do sekcji',
                                'required' => 1,
                                'min' => 1,
                                'max' => 0,
                                'layout' => 'block',
                                'button_label' => 'Dodaj zakładkę',
                                'sub_fields' => array(
                                    array(
                                        'key' => 'field_tabs_section_tab_title',
                                        'label' => 'Tytuł zakładki',
                                        'name' => 'tab_title',
                                        'type' => 'text',
                                        'instructions' => 'Wprowadź tytuł zakładki',
                                        'required' => 1,
                                        'default_value' => 'Tytuł zakładki',
                                    ),
                                    array(
                                        'key' => 'field_tabs_section_tab_content',
                                        'label' => 'Treść zakładki',
                                        'name' => 'tab_content',
                                        'type' => 'flexible_content',
                                        'instructions' => 'Dodaj elementy treści do zakładki',
                                        'required' => 1,
                                        'layouts' => array(
                                            'layout_text_content' => array(
                                                'key' => 'layout_text_content',
                                                'name' => 'text_content',
                                                'label' => 'Treść tekstowa',
                                                'display' => 'block',
                                                'sub_fields' => array(
                                                    array(
                                                        'key' => 'field_tab_text_content',
                                                        'label' => 'Treść',
                                                        'name' => 'content',
                                                        'type' => 'wysiwyg',
                                                        'instructions' => 'Wprowadź treść tekstową',
                                                        'required' => 1,
                                                        'default_value' => '<h2>Treść zakładki</h2>',
                                                        'tabs' => 'all',
                                                        'toolbar' => 'full',
                                                        'media_upload' => 1,
                                                    ),
                                                ),
                                            ),
                                            'layout_image_content' => array(
                                                'key' => 'layout_image_content',
                                                'name' => 'image_content',
                                                'label' => 'Obraz z tekstem',
                                                'display' => 'block',
                                                'sub_fields' => array(
                                                    array(
                                                        'key' => 'field_tab_image',
                                                        'label' => 'Obraz',
                                                        'name' => 'image',
                                                        'type' => 'image',
                                                        'instructions' => 'Wybierz obraz',
                                                        'required' => 1,
                                                        'return_format' => 'url',
                                                        'preview_size' => 'medium',
                                                        'library' => 'all',
                                                    ),
                                                    array(
                                                        'key' => 'field_tab_image_caption',
                                                        'label' => 'Podpis obrazu',
                                                        'name' => 'caption',
                                                        'type' => 'text',
                                                        'instructions' => 'Wprowadź podpis obrazu (opcjonalnie)',
                                                        'required' => 0,
                                                    ),
                                                    array(
                                                        'key' => 'field_tab_image_text',
                                                        'label' => 'Tekst',
                                                        'name' => 'text',
                                                        'type' => 'wysiwyg',
                                                        'instructions' => 'Wprowadź tekst obok obrazu',
                                                        'required' => 0,
                                                        'tabs' => 'all',
                                                        'toolbar' => 'full',
                                                        'media_upload' => 0,
                                                    ),
                                                ),
                                            ),
                                            'layout_products_content' => array(
                                                'key' => 'layout_products_content',
                                                'name' => 'products_content',
                                                'label' => 'Produkty',
                                                'display' => 'block',
                                                'sub_fields' => array(
                                                    array(
                                                        'key' => 'field_tab_products_title',
                                                        'label' => 'Tytuł sekcji',
                                                        'name' => 'title',
                                                        'type' => 'text',
                                                        'instructions' => 'Wprowadź tytuł sekcji produktów (opcjonalnie)',
                                                        'required' => 0,
                                                    ),
                                                    array(
                                                        'key' => 'field_tab_products',
                                                        'label' => 'Wybierz produkty',
                                                        'name' => 'products',
                                                        'type' => 'relationship',
                                                        'instructions' => 'Wybierz produkty do wyświetlenia',
                                                        'required' => 1,
                                                        'post_type' => array(
                                                            0 => 'product',
                                                        ),
                                                        'taxonomy' => '',
                                                        'filters' => array(
                                                            0 => 'search',
                                                        ),
                                                        'elements' => array(
                                                            0 => 'featured_image',
                                                        ),
                                                        'min' => 1,
                                                        'max' => 8,
                                                        'return_format' => 'id',
                                                    ),
                                                ),
                                            ),
                                            'layout_product_slider' => array(
                                                'key' => 'layout_tab_product_slider',
                                                'name' => 'product_slider',
                                                'label' => 'Slider produktów',
                                                'display' => 'block',
                                                'sub_fields' => array(
                                                    array(
                                                        'key' => 'field_tab_product_slider_products',
                                                        'label' => 'Wybierz produkty',
                                                        'name' => 'products',
                                                        'type' => 'relationship',
                                                        'instructions' => 'Wybierz produkty do wyświetlenia w sliderze',
                                                        'required' => 1,
                                                        'post_type' => array(
                                                            0 => 'product',
                                                        ),
                                                        'taxonomy' => '',
                                                        'filters' => array(
                                                            0 => 'search',
                                                        ),
                                                        'elements' => array(
                                                            0 => 'featured_image',
                                                        ),
                                                        'min' => 1,
                                                        'max' => 10,
                                                        'return_format' => 'id',
                                                    ),
                                                ),
                                            ),
                                        ),
                                        'button_label' => 'Dodaj element',
                                        'min' => 1,
                                        'max' => '',
                                    ),
                                ),
                            ),
                        ),
                    ),
                    'layout_product_slider' => array(
                        'key' => 'layout_product_slider',
                        'name' => 'product_slider',
                        'label' => 'Slider produktów',
                        'display' => 'block',
                        'sub_fields' => array(
                            array(
                                'key' => 'field_product_slider_title',
                                'label' => 'Tytuł sekcji',
                                'name' => 'title',
                                'type' => 'text',
                                'instructions' => 'Wprowadź tytuł sekcji (opcjonalnie)',
                                'required' => 0,
                                'default_value' => 'Polecane produkty',
                            ),
                            array(
                                'key' => 'field_product_slider_products',
                                'label' => 'Wybierz produkty',
                                'name' => 'products',
                                'type' => 'relationship',
                                'instructions' => 'Wybierz produkty do wyświetlenia w sliderze',
                                'required' => 1,
                                'post_type' => array(
                                    0 => 'product',
                                ),
                                'taxonomy' => '',
                                'filters' => array(
                                    0 => 'search',
                                ),
                                'elements' => array(
                                    0 => 'featured_image',
                                ),
                                'min' => 1,
                                'max' => 10,
                                'return_format' => 'id',
                            ),
                        ),
                    ),
                    'layout_home_contact_section' => array(
                        'key' => 'layout_home_contact_section',
                        'name' => 'home_contact_section',
                        'label' => 'Sekcja kontaktowa',
                        'display' => 'block',
                        'sub_fields' => array(
                            array(
                                'key' => 'field_home_contact_background_image',
                                'label' => 'Obraz tła',
                                'name' => 'background_image',
                                'type' => 'image',
                                'instructions' => 'Wybierz obraz tła dla sekcji kontaktowej',
                                'required' => 1,
                                'return_format' => 'url',
                                'preview_size' => 'medium',
                                'library' => 'all',
                            ),
                            array(
                                'key' => 'field_home_contact_title',
                                'label' => 'Tytuł',
                                'name' => 'title',
                                'type' => 'text',
                                'instructions' => 'Wprowadź tytuł sekcji kontaktowej',
                                'required' => 1,
                                'default_value' => 'Kontakt',
                            ),
                            array(
                                'key' => 'field_home_contact_text',
                                'label' => 'Tekst',
                                'name' => 'text',
                                'type' => 'textarea',
                                'instructions' => 'Wprowadź tekst sekcji kontaktowej',
                                'required' => 0,
                                'default_value' => 'Zapisz się do naszego newsletterai otrzymuj informacje na temat nowych produktów i specjalnych promocji.',
                                'rows' => 3,
                                'new_lines' => 'br',
                            ),
                            array(
                                'key' => 'field_home_contact_form_id',
                                'label' => 'Wybierz formularz kontaktowy',
                                'name' => 'form_id',
                                'type' => 'select',
                                'instructions' => 'Wybierz formularz kontaktowy z listy',
                                'required' => 1,
                                'choices' => galbud_get_contact_form_7_forms(),
                                'default_value' => '',
                                'allow_null' => 0,
                                'multiple' => 0,
                                'ui' => 1,
                                'ajax' => 0,
                                'return_format' => 'value',
                                'placeholder' => '',
                            ),
                        ),
                    ),
                ),
                'button_label' => 'Dodaj sekcję',
                'min' => '',
                'max' => '',
            ),
        ),
        'location' => array(
            array(
                array(
                    'param' => 'page_type',
                    'operator' => '==',
                    'value' => 'front_page',
                ),
            ),
        ),
        'menu_order' => 0,
        'position' => 'normal',
        'style' => 'default',
        'label_placement' => 'top',
        'instruction_placement' => 'label',
        'hide_on_screen' => '',
        'active' => true,
        'description' => '',
        'show_in_rest' => 1,
    ));
}
add_action('acf/init', 'galbud_register_front_page_acf_fields');
