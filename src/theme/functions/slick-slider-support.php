<?php
function enqueue_slick_slider() {
// Enqueue Slick CSS
	wp_enqueue_style(
		'slick-slider-css',
		'https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.8.1/slick.min.css',
		array(),
		'1.8.1'
	);

// Enqueue Slick theme CSS (optional but recommended)
	wp_enqueue_style(
		'slick-theme-css',
		'https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.8.1/slick-theme.min.css',
		array( 'slick-slider-css' ),
		'1.8.1'
	);

// Enqueue jQuery (WordPress already includes it, but this ensures it loads before Slick)
	wp_enqueue_script( 'jquery' );

// Enqueue Slick JS
	wp_enqueue_script(
		'slick-slider-js',
		'https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.8.1/slick.min.js',
		array( 'jquery' ),
		'1.8.1',
		true
	);

}

// Hook into WordPress
add_action( 'wp_enqueue_scripts', 'enqueue_slick_slider' );
