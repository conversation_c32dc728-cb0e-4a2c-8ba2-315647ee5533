<?php
// Register the navigation menus
function register_my_menus()
{
	register_nav_menus(array(
		'header-menu' => 'Header Menu',
		'mobile-menu' => 'Mobile Menu'
	));
}

add_action('init', 'register_my_menus');

/**
 * Get all product categories in a hierarchical structure
 *
 * @param int $parent Parent category ID (0 for top-level categories)
 * @param int $depth Current depth level
 * @return array Array of categories with their children
 */
function get_product_categories_hierarchical($parent = 0, $depth = 0)
{
	$args = array(
		'taxonomy'     => 'product_cat',
		'orderby'      => 'name',
		'order'        => 'ASC',
		'hide_empty'   => true, // Ukryj puste kategorie
		'parent'       => $parent
	);

	$categories = get_terms($args);
	$result = array();

	if (!empty($categories) && !is_wp_error($categories)) {
		foreach ($categories as $category) {
			// Get category image
			$thumbnail_id = get_term_meta($category->term_id, 'thumbnail_id', true);
			$image_url = '';

			if ($thumbnail_id) {
				$image_url = wp_get_attachment_url($thumbnail_id);
			}

			// Use placeholder if no image is available
			if (!$image_url && function_exists('wc_placeholder_img_src')) {
				$image_url = wc_placeholder_img_src('thumbnail');
			}

			$cat_data = array(
				'id'        => $category->term_id,
				'name'      => $category->name,
				'slug'      => $category->slug,
				'count'     => $category->count,
				'link'      => get_term_link($category),
				'image'     => $image_url,
				'depth'     => $depth,
				'children'  => array()
			);

			// Get children if we're not too deep
			if ($depth < 2) { // Limit to 2 levels deep
				$cat_data['children'] = get_product_categories_hierarchical($category->term_id, $depth + 1);
			}

			$result[] = $cat_data;
		}
	}

	return $result;
} // Add the missing closing brace here

/**
 * Filter menu items based on user login status
 *
 * Removes login and registration links when user is logged in
 *
 * @param array $items Menu items
 * @return array Filtered menu items
 */
function galbud_filter_menu_items($items)
{
	// If user is logged in, remove login and registration links
	if (is_user_logged_in()) {
		foreach ($items as $key => $item) {
			// Check if the menu item links to login or registration pages
			if (
				strpos($item->url, '/logowanie') !== false ||
				strpos($item->url, '/rejestracja') !== false ||
				strpos($item->url, '/rejestracja/rejestracja-b2b') !== false ||
				strpos($item->url, '/rejestracja/rejestracja-b2c') !== false
			) {
				unset($items[$key]);
			}
		}
	}

	return $items;
}
add_filter('wp_nav_menu_objects', 'galbud_filter_menu_items', 10, 1);

// Display the main navigation menu (using WordPress header menu)
function display_my_menu()
{
	wp_nav_menu(array(
		'theme_location'  => 'header-menu',
		'container'       => 'nav',
		'container_class' => 'site-navigation product-mega-menu',
		'menu_class'      => 'header-menu',
		'fallback_cb'     => false
	));
}

// Display the mobile navigation menu
function display_mobile_menu()
{
	wp_nav_menu(array(
		'theme_location'  => 'mobile-menu',
		'container'       => 'nav',
		'container_class' => 'mobile-navigation',
		'menu_class'      => 'mobile-menu',
		'fallback_cb'     => false
	));
}
