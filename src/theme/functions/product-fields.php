<?php

/**
 * Custom product fields and taxonomies for WooCommerce
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
	exit;
}

/**
 * Register ACF fields for products if ACF is active
 */
function galbud_register_product_acf_fields()
{
	// Check if ACF is active
	if (!function_exists('acf_add_local_field_group')) {
		return;
	}

	// Product dimensions field group has been removed in favor of WooCommerce's built-in dimensions

	// Product purpose field group
	acf_add_local_field_group(array(
		'key' => 'group_product_purpose',
		'title' => 'Przeznaczenie produktu',
		'fields' => array(
			array(
				'key' => 'field_product_purpose',
				'label' => 'Przeznaczenie',
				'name' => 'product_purpose',
				'type' => 'textarea',
				'instructions' => 'Opisz przeznaczenie produktu',
				'required' => 0,
				'default_value' => '',
				'placeholder' => '',
				'maxlength' => '',
				'rows' => 4,
				'new_lines' => 'br',
			),
		),
		'location' => array(
			array(
				array(
					'param' => 'post_type',
					'operator' => '==',
					'value' => 'product',
				),
			),
		),
		'menu_order' => 0,
		'position' => 'normal',
		'style' => 'default',
		'label_placement' => 'top',
		'instruction_placement' => 'label',
		'hide_on_screen' => '',
		'active' => true,
		'description' => '',
		'show_in_rest' => 1,
	));

	// Product wide image field group
	acf_add_local_field_group(array(
		'key' => 'group_product_wide_image',
		'title' => 'Szeroki obraz produktu',
		'fields' => array(
			array(
				'key' => 'field_product_wide_image',
				'label' => 'Szeroki obraz',
				'name' => 'product_wide_image',
				'type' => 'image',
				'instructions' => 'Dodaj szeroki obraz produktu',
				'required' => 0,
				'return_format' => 'array',
				'preview_size' => 'medium',
				'library' => 'all',
				'min_width' => '',
				'min_height' => '',
				'min_size' => '',
				'max_width' => '',
				'max_height' => '',
				'max_size' => '',
				'mime_types' => '',
			),
		),
		'location' => array(
			array(
				array(
					'param' => 'post_type',
					'operator' => '==',
					'value' => 'product',
				),
			),
		),
		'menu_order' => 1,
		'position' => 'normal',
		'style' => 'default',
		'label_placement' => 'top',
		'instruction_placement' => 'label',
		'hide_on_screen' => '',
		'active' => true,
		'description' => '',
		'show_in_rest' => 1,
	));

	// Product specification field group
	acf_add_local_field_group(array(
		'key' => 'group_product_specification',
		'title' => 'Specyfikacja produktu',
		'fields' => array(
			array(
				'key' => 'field_product_specification',
				'label' => 'Specyfikacja',
				'name' => 'product_specification',
				'type' => 'repeater',
				'instructions' => 'Dodaj specyfikację produktu',
				'required' => 0,
				'collapsed' => '',
				'min' => 0,
				'max' => 0,
				'layout' => 'table',
				'button_label' => 'Dodaj wiersz',
				'sub_fields' => array(
					array(
						'key' => 'field_specification_name',
						'label' => 'Nazwa',
						'name' => 'name',
						'type' => 'text',
						'instructions' => '',
						'required' => 1,
						'conditional_logic' => 0,
						'wrapper' => array(
							'width' => '',
							'class' => '',
							'id' => '',
						),
						'default_value' => '',
						'placeholder' => '',
						'prepend' => '',
						'append' => '',
						'maxlength' => '',
					),
					array(
						'key' => 'field_specification_value',
						'label' => 'Wartość',
						'name' => 'value',
						'type' => 'text',
						'instructions' => '',
						'required' => 1,
						'conditional_logic' => 0,
						'wrapper' => array(
							'width' => '',
							'class' => '',
							'id' => '',
						),
						'default_value' => '',
						'placeholder' => '',
						'prepend' => '',
						'append' => '',
						'maxlength' => '',
					),
				),
			),
		),
		'location' => array(
			array(
				array(
					'param' => 'post_type',
					'operator' => '==',
					'value' => 'product',
				),
			),
		),
		'menu_order' => 2,
		'position' => 'normal',
		'style' => 'default',
		'label_placement' => 'top',
		'instruction_placement' => 'label',
		'hide_on_screen' => '',
		'active' => true,
		'description' => '',
		'show_in_rest' => 1,
	));
}
add_action('acf/init', 'galbud_register_product_acf_fields');
