<?php

namespace Automattic\WooCommerce\Admin;

/**
 * WooCommerce Function Overrides
 * 
 * This file contains namespace-level overrides for deprecated WooCommerce functions.
 * It uses PHP's namespace mechanism to replace the deprecated functions with
 * versions that use the new recommended functions.
 */

// Don't allow direct access
if (!defined('ABSPATH')) {
    exit;
}

// Only define these if they don't already exist
if (!function_exists('Automattic\WooCommerce\Admin\is_current_page_store_page')) {
    /**
     * Replacement for the deprecated is_store_page function
     * 
     * @param string|null $url The URL to check
     * @return bool Whether the URL is a store page
     */
    function is_current_page_store_page($url = null)
    {
        // Use the global function from WooCommerce core if it exists
        if (function_exists('\is_current_page_store_page')) {
            $func = '\is_current_page_store_page';
            return $func($url);
        }

        // Fallback to basic WooCommerce page checks - using global namespace functions
        // Check each function exists before calling it to avoid errors
        $is_store = false;

        if (function_exists('\is_woocommerce')) {
            $is_store = $is_store || \is_woocommerce();
        }

        if (function_exists('\is_shop')) {
            $is_store = $is_store || \is_shop();
        }

        if (function_exists('\is_product_category')) {
            $is_store = $is_store || \is_product_category();
        }

        if (function_exists('\is_product_tag')) {
            $is_store = $is_store || \is_product_tag();
        }

        if (function_exists('\is_product')) {
            $is_store = $is_store || \is_product();
        }

        return $is_store;
    }
}

// Override the WCAdminHelper class if it exists
if (class_exists('Automattic\WooCommerce\Admin\WCAdminHelper')) {
    /**
     * Create a clean override of the WCAdminHelper class instead of extending it
     * This prevents PHP from calling the parent class method which triggers the deprecation
     */
    class WCAdminHelper
    {
        /**
         * Override the deprecated is_store_page method
         * 
         * @param string|null $url The URL to check
         * @return bool Whether the URL is a store page
         */
        public static function is_store_page($url = null)
        {
            return is_current_page_store_page($url);
        }

        /**
         * Pass through any other static calls to the original class
         */
        public static function __callStatic($name, $arguments)
        {
            return call_user_func_array(['\Automattic\WooCommerce\Admin\WCAdminHelper', $name], $arguments);
        }
    }
}
