<?php

/**
 * WooCommerce compatibility functions
 *
 * This file contains functions to handle WooCommerce compatibility,
 * including handling deprecated functions.
 */

/**
 * Compatibility function for is_store_page
 *
 * Handles the deprecation of Automattic\WooCommerce\Admin\WCAdminHelper::is_store_page
 * by using is_current_page_store_page when available.
 *
 * @param string $url Optional URL to check
 * @return bool Whether the current page is a store page
 */
function galbud_is_store_page($url = null)
{
    // First try our own namespace override
    if (class_exists('Automattic\WooCommerce\Admin\WCAdminHelper')) {
        return \Automattic\WooCommerce\Admin\WCAdminHelper::is_store_page($url);
    }

    // As a fallback, check if the new function exists (WooCommerce 9.8.0+)
    if (function_exists('is_current_page_store_page')) {
        return is_current_page_store_page($url);
    }

    // If neither function is available, use a basic check
    // This is a simplified version and may not cover all cases
    $is_store = false;

    if (function_exists('is_woocommerce')) {
        $is_store = $is_store || is_woocommerce();
    }

    if (function_exists('is_shop')) {
        $is_store = $is_store || is_shop();
    }

    if (function_exists('is_product_category')) {
        $is_store = $is_store || is_product_category();
    }

    if (function_exists('is_product_tag')) {
        $is_store = $is_store || is_product_tag();
    }

    if (function_exists('is_product')) {
        $is_store = $is_store || is_product();
    }

    return $is_store;
}

/**
 * Add WooCommerce theme support
 */
function galbud_woocommerce_setup()
{
    // Add WooCommerce support if not already added in functions.php
    if (!current_theme_supports('woocommerce')) {
        add_theme_support('woocommerce');
        add_theme_support('woocommerce', [
            'thumbnail_image_width' => 512,
            'gallery_thumbnail_image_width' => 512,
            'single_image_width' => 512,
        ]);
    }
}
add_action('after_setup_theme', 'galbud_woocommerce_setup', 5); // Priority 5 to run before the main theme setup

/**
 * Override the deprecated WooCommerce function directly
 *
 * This approach uses PHP's namespace mechanism to override the deprecated function
 * before it's called by WooCommerce core code.
 */
function galbud_override_wc_deprecated_functions()
{
    // Only proceed if WooCommerce is active
    if (!class_exists('WooCommerce')) {
        return;
    }

    // Check if we need to override the function - only if the WCAdminHelper class exists
    // but we need to override the deprecated function
    if (class_exists('Automattic\WooCommerce\Admin\WCAdminHelper')) {
        // Include our override functions
        require_once __DIR__ . '/woocommerce-overrides.php';
    }
}

// Run our fix early, before WooCommerce loads its admin features
add_action('plugins_loaded', 'galbud_override_wc_deprecated_functions', 5);

/**
 * Restrict checkout page to logged-in users only
 *
 * This function checks if the current page is the checkout page and if the user is logged in.
 * If the user is not logged in, they will be redirected to the login page with a return URL
 * parameter so they can be redirected back to checkout after logging in.
 */
function galbud_restrict_checkout_to_logged_in_users()
{
    // Only run this on the frontend
    if (is_admin()) {
        return;
    }

    // Check if WooCommerce is active
    if (!class_exists('WooCommerce')) {
        return;
    }

    // Check if this is the checkout page
    if (function_exists('is_checkout') && is_checkout() && !is_wc_endpoint_url('order-received')) {
        // If user is not logged in, redirect to the login page
        if (!is_user_logged_in()) {
            // Check if we're already on the login page to prevent redirect loops
            global $post;
            if ($post && $post->post_name === 'logowanie') {
                return;
            }

            // Use the custom login page
            $login_url = '/logowanie';

            // Add a notice to explain why they were redirected
            wc_add_notice(__('You must be logged in to access the checkout page.', 'woocommerce'), 'notice');

            // Perform the redirect
            wp_safe_redirect($login_url);
            exit;
        }
    }
}

// Add the function to the template_redirect hook
add_action('template_redirect', 'galbud_restrict_checkout_to_logged_in_users', 10);

/**
 * Add a notice on the cart page for non-logged-in users
 *
 * This function adds a notice on the cart page to inform non-logged-in users
 * that they need to log in to proceed to checkout.
 */
function galbud_add_login_notice_on_cart()
{
    // Only run this on the frontend
    if (is_admin()) {
        return;
    }

    // Check if WooCommerce is active
    if (!class_exists('WooCommerce')) {
        return;
    }

    // Check if this is the cart page and user is not logged in
    if (function_exists('is_cart') && is_cart() && !is_user_logged_in()) {
        wc_print_notice(
            sprintf(
                __('You need to be logged in to proceed to checkout. <a href="%s">Log in</a> or <a href="%s">create an account</a>.', 'woocommerce'),
                '/logowanie',
                '/rejestracja'
            ),
            'notice'
        );
    }
}

// Add the notice to the cart page
add_action('woocommerce_before_cart', 'galbud_add_login_notice_on_cart', 10);

/**
 * Modify the checkout URL for non-logged-in users
 *
 * This function changes the checkout URL to the custom login page (/logowanie)
 * for non-logged-in users.
 *
 * @param string $url The original checkout URL
 * @return string The modified URL
 */
function galbud_modify_checkout_url_for_non_logged_in($url)
{
    if (!is_user_logged_in()) {
        return '/logowanie';
    }
    return $url;
}

// Filter the checkout URL
add_filter('woocommerce_get_checkout_url', 'galbud_modify_checkout_url_for_non_logged_in', 10, 1);

/**
 * Modify the "Proceed to Checkout" button text for non-logged-in users
 *
 * @param string $button_text The original button text
 * @return string The modified button text
 */
function galbud_modify_checkout_button_text($button_text)
{
    if (!is_user_logged_in()) {
        return __('Zaloguj się', 'woocommerce');
    }
    return $button_text;
}

// Filter the checkout button text
add_filter('woocommerce_order_button_text', 'galbud_modify_checkout_button_text', 10, 1);
add_filter('woocommerce_proceed_to_checkout_button_text', 'galbud_modify_checkout_button_text', 10, 1);

/**
 * Restrict my-account page to logged-in users only
 *
 * This function checks if the current page is the my-account page and if the user is logged in.
 * If the user is not logged in, they will be redirected to the login page.
 */
function galbud_restrict_myaccount_to_logged_in_users()
{
    // Only run this on the frontend
    if (is_admin()) {
        return;
    }

    // Check if WooCommerce is active
    if (!class_exists('WooCommerce')) {
        return;
    }

    // Check if this is the my-account page
    if (function_exists('is_account_page') && is_account_page()) {
        // If user is not logged in, redirect to the login page
        if (!is_user_logged_in()) {
            // Check if we're already on the login page to prevent redirect loops
            global $post;
            if ($post && $post->post_name === 'logowanie') {
                return;
            }

            // Use the custom login page
            $login_url = '/logowanie';

            // Add a notice to explain why they were redirected
            wc_add_notice(__('Musisz być zalogowany, aby uzyskać dostęp do swojego konta.', 'woocommerce'), 'notice');

            // Perform the redirect
            wp_safe_redirect($login_url);
            exit;
        }
    }
}

// Add the function to the template_redirect hook
add_action('template_redirect', 'galbud_restrict_myaccount_to_logged_in_users', 10);
