<?php
/**
	* Enable SVG upload support
	*/
function add_svg_support() {
// Allow SVG uploads
	add_filter( 'upload_mimes', 'add_svg_to_upload_mimes' );

// Fix SVG display in Media Library
	add_action( 'admin_head', 'fix_svg_display' );
}

add_action( 'init', 'add_svg_support' );

/**
	* Add SVG MIME type to allowed uploads
	*/
function add_svg_to_upload_mimes( $mimes ) {
	$mimes['svg']  = 'image/svg+xml';
	$mimes['svgz'] = 'image/svg+xml';

	return $mimes;
}

/**
	* Fix SVG display in Media Library
	*/
function fix_svg_display() {
	echo '
<style type="text/css">
	.attachment-266x266, .thumbnail img {
		width: 100% !important;
		height: auto !important;
	}
</style>';
}

/**
	* Add additional security for SVG uploads (recommended)
	* Sanitizes SVG uploads to remove potentially malicious code
	*/
function sanitize_svg_uploads( $file ) {
	if ( $file['type'] === 'image/svg+xml' ) {
// Read the file content
		$file_content = file_get_contents( $file['tmp_name'] );

// Basic sanitization - remove PHP, scripts, etc.
// This is a simple implementation - consider using a proper SVG sanitizer library for production
		$file_content = preg_replace( '/<\?php.+?\?>/s', '', $file_content );
		$file_content = preg_replace( '/<script\b[^>]*>(.*?)<\/script>/is', '', $file_content );

// Write sanitized content back to the file
		file_put_contents( $file['tmp_name'], $file_content );
	}

	return $file;
}

add_filter( 'wp_handle_upload_prefilter', 'sanitize_svg_uploads' );

/**
	* Fix SVG handling for WordPress 5.8+
	*/
function fix_svg_thumbnail_size() {
	add_filter( 'wp_generate_attachment_metadata', function ( $metadata, $attachment_id ) {
		$mime = get_post_mime_type( $attachment_id );
		if ( $mime == 'image/svg+xml' ) {
			if ( empty( $metadata['width'] ) && empty( $metadata['height'] ) ) {
				$svg_path   = get_attached_file( $attachment_id );
				$dimensions = svgsize( $svg_path );

				$metadata['width']  = $dimensions[0];
				$metadata['height'] = $dimensions[1];
			}
		}

		return $metadata;
	}, 10, 2 );
}

add_action( 'init', 'fix_svg_thumbnail_size' );

/**
	* Get SVG dimensions
	*/
function svgsize( $file ) {
	$svg = simplexml_load_file( $file );

	if ( $svg === false ) {
		return false;
	}

	$attributes = $svg->attributes();
	$width      = (string) $attributes->width;
	$height     = (string) $attributes->height;

	if ( ! $width || ! $height ) {
		$viewbox = (string) $attributes->viewBox;
		if ( $viewbox ) {
			$parts = explode( ' ', $viewbox );
			if ( count( $parts ) === 4 ) {
				$width  = $parts[2];
				$height = $parts[3];
			}
		}
	}

	return [ $width, $height ];
}
