<?php

/**
 * WordPressify
 */

if (! function_exists('wordpressify_theme_support')) :
	function wordpressify_theme_support()
	{
		add_editor_style('style.css');
		add_theme_support('align-wide');
		add_theme_support('woocommerce');
		add_theme_support('widgets');
		add_theme_support('woocommerce', [
			'thumbnail_image_width' => 512,
			'gallery_thumbnail_image_width' => 512,
			'single_image_width' => 512,
		]);
	}
endif;
add_action('after_setup_theme', 'wordpressify_theme_support');



if (! function_exists('wordpressify_theme_styles')) :
	function wordpressify_theme_styles()
	{
		$theme_version = wp_get_theme()->get('Version');
		$version_string = is_string($theme_version) ? $theme_version : false;
		wp_register_style(
			'wordpressify_theme-style',
			get_template_directory_uri() . '/style.css',
			[],
			$version_string
		);
		wp_enqueue_style('wordpressify_theme-style');
	}
endif;
add_action('wp_enqueue_scripts', 'wordpressify_theme_styles');



function wordpressify_enqueue_scripts()
{
	wp_enqueue_script('header-script', get_template_directory_uri() . '/js/header-bundle.js', [], true);
	wp_enqueue_script('footer-script', get_template_directory_uri() . '/js/footer-bundle.js', [], true);
}

add_action('wp_enqueue_scripts', 'wordpressify_enqueue_scripts');

require get_template_directory() . '/functions/svg-support.php';
require get_template_directory() . '/functions/logo-support.php';
require get_template_directory() . '/functions/nav-support.php';
require get_template_directory() . '/functions/slick-slider-support.php';
require get_template_directory() . '/functions/woocommerce-support.php';
require get_template_directory() . '/functions/product-fields.php';
require get_template_directory() . '/functions/front-page-fields.php';


function custom_woocommerce_sidebar()
{
	register_sidebar(array(
		'name'          => __('WooCommerce Sidebar', 'your-theme-textdomain'),
		'id'            => 'woocommerce-sidebar',
		'description'   => __('Sidebar for WooCommerce product listing page', 'your-theme-textdomain'),
		'before_widget' => '<div id="%1$s" class="widget %2$s">',
		'after_widget'  => '</div>',
		'before_title'  => '<h3 class="widget-title">',
		'after_title'   => '</h3>',
	));
}
add_action('widgets_init', 'custom_woocommerce_sidebar');

// Register Footer Widget Areas
function galbud_footer_widgets_init()
{
	for ($i = 1; $i <= 6; $i++) {
		register_sidebar(array(
			'name'          => sprintf(__('Footer Kolumna %d', 'galbud'), $i),
			'id'            => 'footer-column-' . $i,
			'description'   => sprintf(__('Widgety w kolumnie %d stopki.', 'galbud'), $i),
			'before_widget' => '<div id="%1$s" class="widget %2$s footer-widget">',
			'after_widget'  => '</div>',
			'before_title'  => '<h4 class="widget-title footer-widget-title">',
			'after_title'   => '</h4>',
		));
	}
}
add_action('widgets_init', 'galbud_footer_widgets_init');


/**
 * Custom implementation of WooCommerce cart totals
 * This demonstrates how to customize the cart totals section
 */

// Remove default cart totals
remove_action('woocommerce_cart_collaterals', 'woocommerce_cart_totals', 10);

// Add our custom cart totals function
add_action('woocommerce_cart_collaterals', 'custom_cart_totals', 10);

function custom_cart_totals()
{
?>
	<div class="cart_totals <?php echo (WC()->customer->has_calculated_shipping()) ? 'calculated_shipping' : ''; ?>">
		<?php do_action('woocommerce_before_cart_totals'); ?>

		<h2><?php _e('Podsumowanie ', 'woocommerce'); ?></h2>

		<table cellspacing="0" class="shop_table shop_table_responsive">
			<tr class="cart-subtotal">
				<th><?php _e('Subtotal', 'woocommerce'); ?></th>
				<td data-title="<?php esc_attr_e('Subtotal', 'woocommerce'); ?>"><?php wc_cart_totals_subtotal_html(); ?></td>
			</tr>

			<?php foreach (WC()->cart->get_coupons() as $code => $coupon) : ?>
				<tr class="cart-discount coupon-<?php echo esc_attr(sanitize_title($code)); ?>">
					<th><?php wc_cart_totals_coupon_label($coupon); ?></th>
					<td data-title="<?php echo esc_attr(wc_cart_totals_coupon_label($coupon, false)); ?>"><?php wc_cart_totals_coupon_html($coupon); ?></td>
				</tr>
			<?php endforeach; ?>

			<?php if (WC()->cart->needs_shipping() && WC()->cart->show_shipping()) : ?>
				<?php do_action('woocommerce_cart_totals_before_shipping'); ?>

				<?php wc_cart_totals_shipping_html(); ?>

				<?php do_action('woocommerce_cart_totals_after_shipping'); ?>
			<?php elseif (WC()->cart->needs_shipping() && 'yes' === get_option('woocommerce_enable_shipping_calc')) : ?>
				<tr class="shipping">
					<th><?php _e('Shipping', 'woocommerce'); ?></th>
					<td data-title="<?php esc_attr_e('Shipping', 'woocommerce'); ?>"><?php woocommerce_shipping_calculator(); ?></td>
				</tr>
			<?php endif; ?>

			<?php foreach (WC()->cart->get_fees() as $fee) : ?>
				<tr class="fee">
					<th><?php echo esc_html($fee->name); ?></th>
					<td data-title="<?php echo esc_attr($fee->name); ?>"><?php wc_cart_totals_fee_html($fee); ?></td>
				</tr>
			<?php endforeach; ?>

			<?php if (wc_tax_enabled() && !WC()->cart->display_prices_including_tax()) : ?>
				<?php if ('itemized' === get_option('woocommerce_tax_total_display')) : ?>
					<?php foreach (WC()->cart->get_tax_totals() as $code => $tax) : ?>
						<tr class="tax-rate tax-rate-<?php echo sanitize_title($code); ?>">
							<th><?php echo esc_html($tax->label); ?></th>
							<td data-title="<?php echo esc_attr($tax->label); ?>"><?php echo wp_kses_post($tax->formatted_amount); ?></td>
						</tr>
					<?php endforeach; ?>
				<?php else : ?>
					<tr class="tax-total">
						<th><?php echo esc_html(WC()->countries->tax_or_vat()); ?></th>
						<td data-title="<?php echo esc_attr(WC()->countries->tax_or_vat()); ?>"><?php wc_cart_totals_taxes_total_html(); ?></td>
					</tr>
				<?php endif; ?>
			<?php endif; ?>

			<?php do_action('woocommerce_cart_totals_before_order_total'); ?>

			<tr class="order-total">
				<th><?php _e('Total', 'woocommerce'); ?></th>
				<td data-title="<?php esc_attr_e('Total', 'woocommerce'); ?>"><?php wc_cart_totals_order_total_html(); ?></td>
			</tr>

			<?php do_action('woocommerce_cart_totals_after_order_total'); ?>
		</table>

		<!-- Coupon form section -->
		<?php if (wc_coupons_enabled()) : ?>
			<div class="cart-coupon-section">


				<?php wc_get_template('cart/coupon.php'); ?>
				<button type="button" class="show-coupon-form galbud-button galbud-button--secondary">
					<?php esc_html_e('Dodaj kupon rabatowy', 'woocommerce'); ?>
				</button>
			</div>
		<?php endif; ?>

		<div class="wc-proceed-to-checkout">
			<?php if (is_user_logged_in()) : ?>
				<a href="/checkout/" class="galbud-button galbud-button--primary">Dalej</a>
			<?php else : ?>
				<div class="login-register-buttons">
					<a href="<?php echo esc_url('/logowanie'); ?>" class="galbud-button galbud-button--primary">Zaloguj się</a>
					<a href="<?php echo esc_url('/rejestracja/'); ?>" class="galbud-button galbud-button--secondary">Zarejestruj się</a>
				</div>
			<?php endif; ?>
		</div>

		<?php do_action('woocommerce_after_cart_totals'); ?>
	</div>

	<style>
		.login-register-buttons {
			display: flex;
			flex-direction: column;
			gap: 10px;
			width: 100%;
		}

		.login-register-buttons a {
			text-align: center;
		}

		@media (min-width: 768px) {
			.login-register-buttons {
				flex-direction: row;
			}

			.login-register-buttons a {
				flex: 1;
			}
		}
	</style>
<?php
}

/**
 * Alternative approach - using woocommerce_get_cart_totals_html filter
 * This allows modifying the cart totals HTML without replacing the entire function
 */
function filter_cart_totals_html($html)
{
	// You can modify the HTML here using string replacement
	// For example, change the heading text
	$html = str_replace('<h2>Cart totals</h2>', '<h2>Your Order Summary</h2>', $html);

	// Add a message before the checkout button
	$checkout_button_position = strpos($html, 'wc-proceed-to-checkout');
	if ($checkout_button_position !== false) {
		$insert_message = '<div class="secure-checkout-message"><p>🔒 Secure checkout. 100% satisfaction guaranteed.</p></div>';
		$html = substr_replace($html, $insert_message, $checkout_button_position, 0);
	}

	return $html;
}

function display_woocommerce_cart_item_count()
{
	// Check if WooCommerce is active
	if (class_exists('WooCommerce')) {
		$count = WC()->cart->get_cart_contents_count();

		if ($count > 0) {
			return '<span class="cart-count">' . $count . '</span>';
		} else {
			return '<span class="cart-count">0</span>';
		}
	}

	return '';
}
// Register as shortcode so you can use [woo_cart_count] anywhere
add_shortcode('woo_cart_count', 'display_woocommerce_cart_item_count');

function my_custom_woocommerce_notification($message, $type = 'success')
{
	// Valid notice types: success, error, notice
	if (!in_array($type, ['success', 'error', 'notice'])) {
		$type = 'notice';
	}

	// Add the notice
	wc_add_notice($message, $type);

	// If we want to display it immediately
	if (function_exists('wc_print_notices')) {
		wc_print_notices();
	}
}

function my_custom_mime_types($mimes)
{
	$mimes["gpx"] = "text/gpsxml";
	return $mimes;
}
add_filter("upload_mimes", "my_custom_mime_types");

/**
 * Redirect to login page after logout with status parameter
 */
function galbud_logout_redirect()
{
	wp_redirect(home_url('/logowanie?logout=success'));
	exit();
}
add_action('wp_logout', 'galbud_logout_redirect');


add_filter('woocommerce_placeholder_img_src', 'custom_woocommerce_placeholder_img_src');

function custom_woocommerce_placeholder_img_src($src)
{

	return 'http://dev.gal-bud.pl/wp-content/uploads/woocommerce-placeholder.png'; // Replace this URL with the path to your custom placeholder image.

}

/**
 * Display Latest WordPress Posts
 *
 * This code can be used in a WordPress theme file or as a shortcode
 */

// Basic function to display latest posts
function display_latest_posts($atts)
{
	// Default parameters
	$args = shortcode_atts(array(
		'posts_per_page' => 5,
		'category' => '',
		'order' => 'DESC',
		'orderby' => 'date',
		'thumbnail' => true,
		'excerpt' => true,
		'readmore' => true,
		'readmore_text' => 'Read More',
	), $atts);

	// Convert category slug to ID if provided
	$category_id = '';
	if (!empty($args['category'])) {
		$category = get_category_by_slug($args['category']);
		if ($category) {
			$category_id = $category->term_id;
		}
	}

	// WP_Query arguments
	$query_args = array(
		'post_type' => 'post',
		'post_status' => 'publish',
		'posts_per_page' => $args['posts_per_page'],
		'order' => $args['order'],
		'orderby' => $args['orderby'],
	);

	// Add category parameter if provided
	if ($category_id) {
		$query_args['cat'] = $category_id;
	}

	// The Query
	$query = new WP_Query($query_args);

	// Start output buffering
	ob_start();

	// The Loop
	if ($query->have_posts()) {
		echo '<div class="latest-posts-wrapper">';
		while ($query->have_posts()) {
			$query->the_post();
			echo '<article class="latest-post">';

			// Display featured image if enabled
			if ($args['thumbnail'] && has_post_thumbnail()) {
				echo '<div class="post-thumbnail">';
				echo '<a href="' . get_permalink() . '">';
				the_post_thumbnail('medium');
				echo '</a>';
				echo '</div>';
			}

			echo '<div class="post-content">';
			echo '<h3 class="post-title"><a href="' . get_permalink() . '">' . get_the_title() . '</a></h3>';

			// Display post date
			echo '<div class="post-meta">';
			echo '<span class="post-date">' . get_the_date() . '</span>';
			echo '</div>';

			// Display excerpt if enabled
			if ($args['excerpt']) {
				echo '<div class="post-excerpt">';
				the_excerpt();
				echo '</div>';
			}

			// Display read more link if enabled
			if ($args['readmore']) {
				echo '<a class="read-more" href="' . get_permalink() . '">' . $args['readmore_text'] . '</a>';
			}

			echo '</div>'; // .post-content
			echo '</article>';
		}
		echo '</div>'; // .latest-posts-wrapper
	} else {
		echo '<p>No posts found.</p>';
	}

	// Reset post data
	wp_reset_postdata();

	// Get the buffered content and return it
	return ob_get_clean();
}

// Register shortcode [latest_posts]
add_shortcode('latest_posts', 'display_latest_posts');

/**
 * Custom WooCommerce review template callback
 * This function displays reviews in our custom testimonials format
 */
function galbud_woocommerce_review_template($comment, $args, $depth)
{
	$GLOBALS['comment'] = $comment;
	$comment_id = get_comment_ID();
	$rating = intval(get_comment_meta($comment_id, 'rating', true));
?>
	<div class="testimonials-item">
		<div>
			<?php
			// Get avatar or use default image
			$avatar = get_avatar($comment, 82);
			if ($avatar) {
				echo $avatar;
			} else {
				echo '<img src="https://picsum.photos/id/1/82/82" class="testimonials-image" alt="">';
			}
			?>
		</div>
		<div>
			<h4><?php comment_author(); ?></h4>
			<span><?php echo get_comment_date('d.m.y'); ?></span>

			<?php if ($rating && wc_review_ratings_enabled()) : ?>
				<ul class="stars-list">
					<?php for ($i = 1; $i <= 5; $i++) : ?>
						<li class="<?php echo ($i <= $rating) ? 'active' : 'inactive'; ?>">
							<svg width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
								<path d="M9.5 1.5L12.0819 6.75532L17.8372 7.59549L13.6686 11.6447L14.6638 17.4045L9.5 14.7053L4.33624 17.4045L5.33141 11.6447L1.16282 7.59549L6.91809 6.75532L9.5 1.5Z" fill="#01A0E2" />
							</svg>
						</li>
					<?php endfor; ?>
				</ul>
			<?php endif; ?>

			<p><?php comment_text(); ?></p>
		</div>
	</div>
	<?php
}

/**
 * Add "verified purchase" text to reviews from customers who bought the product
 */
function galbud_add_verified_purchase_badge($comment_text, $comment)
{
	if (wc_customer_bought_product($comment->comment_author_email, $comment->user_id, get_the_ID())) {
		$verified_badge = '<span class="verified-purchase">' . esc_html__('Zweryfikowany zakup', 'woocommerce') . '</span>';
		return $comment_text . $verified_badge;
	}
	return $comment_text;
}
add_filter('comment_text', 'galbud_add_verified_purchase_badge', 10, 2);

/**
 * Display related products on the cart page based on categories/tags.
 */
function galbud_display_cart_related_products()
{
	// Get cart contents
	$cart = WC()->cart->get_cart();

	if (!$cart) {
		return; // Exit if cart is empty
	}

	$cart_product_ids = [];
	$category_ids = [];
	$tag_ids = [];

	// Collect IDs, categories, and tags from cart items
	foreach ($cart as $cart_item_key => $cart_item) {
		$product_id = $cart_item['product_id'];
		$cart_product_ids[] = $product_id;

		$terms = get_the_terms($product_id, 'product_cat');
		if ($terms && !is_wp_error($terms)) {
			foreach ($terms as $term) {
				$category_ids[] = $term->term_id;
			}
		}

		$terms = get_the_terms($product_id, 'product_tag');
		if ($terms && !is_wp_error($terms)) {
			foreach ($terms as $term) {
				$tag_ids[] = $term->term_id;
			}
		}
	}

	$category_ids = array_unique($category_ids);
	$tag_ids = array_unique($tag_ids);

	// Only proceed if we have categories or tags to relate by
	if (empty($category_ids) && empty($tag_ids)) {
		return;
	}

	// Prepare query arguments
	$args = [
		'post_type' => 'product',
		'post_status' => 'publish',
		'posts_per_page' => 4, // Number of related products to show
		'post__not_in' => $cart_product_ids, // Exclude items already in cart
		'orderby' => 'rand', // Show random related products
		'tax_query' => [
			'relation' => 'OR', // Match products in same category OR tag
		],
	];

	// Add category query if categories found
	if (!empty($category_ids)) {
		$args['tax_query'][] = [
			'taxonomy' => 'product_cat',
			'field' => 'term_id',
			'terms' => $category_ids,
		];
	}

	// Add tag query if tags found
	if (!empty($tag_ids)) {
		$args['tax_query'][] = [
			'taxonomy' => 'product_tag',
			'field' => 'term_id',
			'terms' => $tag_ids,
		];
	}

	$related_products = new WP_Query($args);

	if ($related_products->have_posts()) : ?>
		<div class="related-products">
			<h2><?php esc_html_e('Podobne produkty', 'woocommerce'); ?></h2>
			<div class="products-grid">
				<?php while ($related_products->have_posts()) : $related_products->the_post();
					// Use the same template as product listing
					wc_get_template_part('content', 'product-listing');
				endwhile; ?>
			</div>
		</div>
<?php
	endif;
	wp_reset_postdata(); // Reset post data after custom query
}


/**
 * Enable reviews tab on product pages
 */
function galbud_enable_reviews_tab($tabs)
{
	// Ensure the reviews tab is enabled
	if (isset($tabs['reviews'])) {
		$tabs['reviews']['priority'] = 30;
	}
	return $tabs;
}
add_filter('woocommerce_product_tabs', 'galbud_enable_reviews_tab', 98);
