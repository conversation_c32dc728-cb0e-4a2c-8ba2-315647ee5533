<?php

/**
 * Template Name: Login and Lost Password Page
 *
 * This template handles both login and lost password functionality
 * based on the 'action' parameter in the URL.
 */

get_header();

// Je<PERSON><PERSON> użytkownik jest już zalogowany, przekieruj go na stronę konta
if (is_user_logged_in() && !isset($_GET['action'])) {
    wp_redirect(wc_get_page_permalink('myaccount'));
    exit;
}

// Get the current action (default is login)
$action = isset($_GET['action']) ? sanitize_text_field($_GET['action']) : 'login';

// Check if user has just logged out
if (isset($_GET['logout']) && $_GET['logout'] === 'success') {
    wc_add_notice(__('Zostałeś pomyślnie wylogowany.', 'woocommerce'), 'success');
}

// Process the lost password form submission
if (isset($_POST['wc_reset_password']) && isset($_POST['user_login'])) {
    $user_login = sanitize_text_field($_POST['user_login']);

    // Verify nonce
    if (isset($_POST['woocommerce-lost-password-nonce']) && wp_verify_nonce($_POST['woocommerce-lost-password-nonce'], 'lost_password')) {
        $user_data = get_user_by('login', $user_login);

        if (!$user_data && is_email($user_login)) {
            $user_data = get_user_by('email', $user_login);
        }

        if ($user_data) {
            $user_login = $user_data->user_login;
            $user_email = $user_data->user_email;
            $key = get_password_reset_key($user_data);

            if (is_wp_error($key)) {
                wc_add_notice($key->get_error_message(), 'error');
            } else {
                // Send email with reset link
                $reset_url = add_query_arg(array(
                    'key' => $key,
                    'id' => $user_data->ID,
                ), wc_get_endpoint_url('lost-password', '', wc_get_page_permalink('myaccount')));

                // Get email content
                $mailer = WC()->mailer();
                $email = $mailer->emails['WC_Email_Customer_Reset_Password'];
                $email->trigger($user_login, $key);

                // Show success message
                wc_add_notice(__('A password reset link has been sent to your email address.', 'woocommerce'), 'success');

                // Set flag for template to show success message
                $action = 'reset-link-sent';
            }
        } else {
            wc_add_notice(__('Invalid username or email.', 'woocommerce'), 'error');
        }
    }
}

// Process password reset form if key and login are provided
if (isset($_GET['key']) && isset($_GET['id'])) {
    $user_id = absint($_GET['id']);
    $reset_key = $_GET['key'];
    $user_data = get_userdata($user_id);

    if ($user_data && get_password_reset_key($user_data) === $reset_key) {
        // Show reset password form
        $action = 'reset';
    } else {
        wc_add_notice(__('Invalid password reset link. Please request a new link.', 'woocommerce'), 'error');
    }
}

// Process the password reset form submission
if (isset($_POST['wc_reset_password_submit']) && isset($_POST['password_1']) && isset($_POST['password_2']) && isset($_POST['key']) && isset($_POST['id'])) {
    $user_id = absint($_POST['id']);
    $reset_key = wc_clean($_POST['key']);
    $user_data = get_userdata($user_id);
    $password_1 = $_POST['password_1'];
    $password_2 = $_POST['password_2'];

    // Verify passwords match
    if ($password_1 !== $password_2) {
        wc_add_notice(__('Passwords do not match.', 'woocommerce'), 'error');
    } elseif (empty($password_1)) {
        wc_add_notice(__('Please enter a password.', 'woocommerce'), 'error');
    } else {
        // Validate password reset key
        $user = check_password_reset_key($reset_key, $user_data->user_login);

        if (is_wp_error($user)) {
            wc_add_notice(__('This password reset key is invalid or has already been used.', 'woocommerce'), 'error');
        } else {
            // Reset password
            reset_password($user, $password_1);

            // Set success message and redirect to login
            wc_add_notice(__('Your password has been reset successfully. Please log in with your new password.', 'woocommerce'), 'success');
            wp_redirect(home_url('/logowanie'));
            exit;
        }
    }
}
?>

<section class="main-slider">
    <div class="container">
        <div class="main-slider__row">
            <div class="main-slider__col">
                <div>
                    <?php if ($action === 'lostpassword') : ?>
                        <!-- Lost Password Form -->
                        <div class="woocommerce-lost-password">
                            <h2><?php esc_html_e('Odzyskiwanie dostępu do konta', 'woocommerce'); ?></h2>

                            <?php
                            // Show messages if there are any
                            wc_print_notices();
                            ?>

                            <form method="post" class="woocommerce-form woocommerce-form-lost-password lost_reset_password">
                                <?php wp_nonce_field('lost_password', 'woocommerce-lost-password-nonce'); ?>

                                <p><?php echo esc_html__('Lost your password? Please enter your username or email address. You will receive a link to create a new password via email.', 'woocommerce'); ?></p>

                                <p class="woocommerce-form-row woocommerce-form-row--wide form-row form-row-wide">
                                    <label for="user_login"><?php esc_html_e('Username or email', 'woocommerce'); ?></label>
                                    <input class="woocommerce-Input woocommerce-Input--text input-text" type="text" name="user_login" id="user_login" autocomplete="username" />
                                </p>

                                <div class="clear"></div>

                                <?php do_action('woocommerce_lostpassword_form'); ?>

                                <p class="woocommerce-form-row form-row">
                                    <input type="hidden" name="wc_reset_password" value="true" />
                                    <button type="submit" class="woocommerce-Button button<?php echo esc_attr(wc_wp_theme_get_element_class_name('button') ? ' ' . wc_wp_theme_get_element_class_name('button') : ''); ?>" value="<?php esc_attr_e('Reset password', 'woocommerce'); ?>"><?php esc_html_e('Reset password', 'woocommerce'); ?></button>
                                </p>

                                <p class="return-to-login">
                                    <a href="<?php echo esc_url('/logowanie'); ?>"><?php esc_html_e('Wróć do logowania', 'woocommerce'); ?></a>
                                </p>

                                <?php do_action('woocommerce_lostpassword_form_end'); ?>
                            </form>
                        </div>
                    <?php elseif ($action === 'reset') : ?>
                        <!-- Reset Password Form -->
                        <div class="woocommerce-lost-password">
                            <h2><?php esc_html_e('Reset Password', 'woocommerce'); ?></h2>

                            <?php wc_print_notices(); ?>

                            <form method="post" class="woocommerce-form woocommerce-form-lost-password reset-password">
                                <p><?php echo esc_html__('Enter a new password below.', 'woocommerce'); ?></p>

                                <p class="woocommerce-form-row woocommerce-form-row--wide form-row form-row-wide">
                                    <label for="password_1"><?php esc_html_e('New password', 'woocommerce'); ?>&nbsp;<span class="required">*</span></label>
                                    <input type="password" class="woocommerce-Input woocommerce-Input--text input-text" name="password_1" id="password_1" autocomplete="new-password" />
                                </p>

                                <p class="woocommerce-form-row woocommerce-form-row--wide form-row form-row-wide">
                                    <label for="password_2"><?php esc_html_e('Re-enter new password', 'woocommerce'); ?>&nbsp;<span class="required">*</span></label>
                                    <input type="password" class="woocommerce-Input woocommerce-Input--text input-text" name="password_2" id="password_2" autocomplete="new-password" />
                                </p>

                                <input type="hidden" name="wc_reset_password_submit" value="true" />
                                <input type="hidden" name="key" value="<?php echo esc_attr($_GET['key']); ?>" />
                                <input type="hidden" name="id" value="<?php echo esc_attr($_GET['id']); ?>" />

                                <div class="clear"></div>

                                <?php do_action('woocommerce_resetpassword_form'); ?>

                                <p class="woocommerce-form-row form-row">
                                    <button type="submit" class="woocommerce-Button button" value="<?php esc_attr_e('Save', 'woocommerce'); ?>"><?php esc_html_e('Save', 'woocommerce'); ?></button>
                                </p>

                                <?php do_action('woocommerce_resetpassword_form_end'); ?>
                            </form>
                        </div>
                    <?php elseif ($action === 'reset-link-sent') : ?>
                        <!-- Reset Link Sent Message -->
                        <div class="woocommerce-lost-password">
                            <h2><?php esc_html_e('Check Your Email', 'woocommerce'); ?></h2>

                            <?php wc_print_notices(); ?>

                            <p class="return-to-login">
                                <a href="<?php echo esc_url('/logowanie'); ?>"><?php esc_html_e('Back to login', 'woocommerce'); ?></a>
                            </p>
                        </div>
                    <?php else : ?>
                        <!-- Standard Login Form -->
                        <div class="woocommerce-login-wrapper">
                            <?php
                            // Use the standard WooCommerce login form
                            if (function_exists('woocommerce_login_form')) {
                                echo '<h2>' . esc_html__('Login', 'woocommerce') . '</h2>';

                                // Show messages if there are any
                                wc_print_notices();

                                // Display the login form
                                woocommerce_login_form(array(
                                    'redirect' => isset($_GET['redirect_to']) ? $_GET['redirect_to'] : wc_get_page_permalink('myaccount'),
                                ));

                                // Add lost password link
                                echo '<p class="woocommerce-LostPassword lost_password">';
                                echo '<a href="' . esc_url('/logowanie?action=lostpassword') . '">' . esc_html__('Lost your password?', 'woocommerce') . '</a>';
                                echo '</p>';

                                // Add registration link if registration is enabled
                                echo '<p class="woocommerce-register-link">';
                                echo esc_html__('Nie masz konta?', 'woocommerce') . ' ';
                                echo '<a href="' . esc_url('/rejestracja/') . '">' . esc_html__('Register', 'woocommerce') . '</a>';
                                echo '</p>';
                            }
                            ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            <div class="main-slider__col">
                <img src="<?php echo get_template_directory_uri(); ?>/img/b2b.jpg" class="main-slider__main-image" alt="">
            </div>
        </div>
    </div>
</section>

<?php
get_footer();
?>