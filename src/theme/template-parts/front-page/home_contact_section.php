<?php

/**
 * Template part for displaying the Home Contact Section.
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package Galbud
 */

$allowed_html = get_query_var('allowed_html', array());
$section_data = get_query_var('section_data', array());

$background_image = ! empty($section_data['background_image']) ? $section_data['background_image'] : '';
$title            = ! empty($section_data['title']) ? $section_data['title'] : '';
$text             = ! empty($section_data['text']) ? $section_data['text'] : '';
$form_id          = ! empty($section_data['form_id']) ? $section_data['form_id'] : '';

// Use default image if none provided
if (! $background_image) {
    $background_image = get_template_directory_uri() . '/assets/img/contact.jpg'; // Poprawiona ścieżka
}

// Generate shortcode from form ID
$form_shortcode = '';
if ($form_id) {
    // Get the form title
    $form = get_post($form_id);
    if ($form && ! is_wp_error($form)) {
        $form_title     = $form->post_title;
        $form_shortcode = '[contact-form-7 id="' . esc_attr($form_id) . '" title="' . esc_attr($form_title) . '"]';
    }
} else {
    // Default shortcode if no form is selected
    $form_shortcode = '[contact-form-7 id="61ab135" title="Home Page Formularz kontaktowy"]';
}
?>
<div class="container">
    <section class="home-contact-section" style="background-image: url('<?php echo esc_url($background_image); ?>');">
        <h2 class="home-contact-section__title"><?php echo wp_kses($title, $allowed_html); ?></h2>
        <p class="home-contact-section__text"><?php echo wp_kses(nl2br($text), $allowed_html); ?></p>
        <?php echo do_shortcode($form_shortcode); ?>
    </section>
</div>