<?php

/**
 * Template part for displaying the Showcase Products section.
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package Galbud
 */

$allowed_html = get_query_var('allowed_html', array());
$section_data = get_query_var('section_data', array());

$section_title = ! empty($section_data['section_title']) ? $section_data['section_title'] : '';
$product_ids   = ! empty($section_data['products']) ? $section_data['products'] : array();

// Make sure we have exactly 2 products
if ($product_ids && count($product_ids) === 2) :
?>
    <div class="container">
        <section class="home-showcase-products">
            <?php
            // Loop through selected products (max 2)
            foreach ($product_ids as $product_id) {
                // Get the WooCommerce product
                $product = wc_get_product($product_id);

                if ($product) {
                    // Get product data
                    $title       = $product->get_name();
                    $description = wp_trim_words($product->get_short_description(), 20, '...');
                    $price       = $product->get_price();
                    $price_html  = $product->get_price_html();
                    $link        = get_permalink($product_id);

                    // Get product image
                    $image_url = get_the_post_thumbnail_url($product_id, 'medium');
                    if (! $image_url) {
                        $image_url = wc_placeholder_img_src('medium');
                    }
            ?>
                    <div class="home-showcase-products__item">
                        <span class="category-name"><?php echo wp_kses($section_title, $allowed_html); ?></span>
                        <div>
                            <img src="<?php echo esc_url($image_url); ?>" class="home-showcase-products__image" alt="<?php echo esc_attr($title); ?>">
                            <h2 class="home-showcase-products__title"><?php echo wp_kses($title, $allowed_html); ?></h2>
                            <p class="home-showcase-products__description"><?php echo wp_kses($description, $allowed_html); ?></p>
                            <span class="home-showcase-products__price">
                                <?php
                                if ($price) {
                                    echo '<span>' . esc_html($price) . '</span>zł /szt.';
                                } else {
                                    echo wp_kses_post($price_html);
                                }
                                ?>
                            </span>
                            <div class="home-showcase-products__footer">
                                <a href="<?php echo esc_url($link); ?>" class="button button--primary">Zobacz produkt</a>
                            </div>
                        </div>
                    </div>
            <?php
                }
            }
            ?>
        </section>
    </div>
<?php
endif;
?>