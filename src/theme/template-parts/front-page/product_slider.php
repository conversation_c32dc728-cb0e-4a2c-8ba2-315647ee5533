<?php

/**
 * Template part for displaying the Product Slider section.
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package Galbud
 */

$allowed_html = get_query_var('allowed_html', array());
$section_data = get_query_var('section_data', array());

$title       = ! empty($section_data['title']) ? $section_data['title'] : '';
$product_ids = ! empty($section_data['products']) ? $section_data['products'] : array();

if ($product_ids && ! empty($product_ids)) :
?>
    <div class="container">
        <?php if ($title) : ?>
            <section class="section-title">
                <div class="section-title__container container"> <!-- Nested container might be redundant, check styling -->
                    <h2 class="section-title__text"><?php echo wp_kses($title, $allowed_html); ?></h2>
                </div>
            </section>
        <?php endif; ?>

        <section class="galbud-product-slider galbud-promo-slider">
            <?php
            // Loop through selected products
            foreach ($product_ids as $product_id) {
                // Get the WooCommerce product
                $product = wc_get_product($product_id);

                if ($product) {
                    // Get product data
                    $product_title       = $product->get_name();
                    $sku                 = $product->get_sku();
                    $link                = get_permalink($product_id);
                    $is_featured         = $product->is_featured();
                    $is_on_sale          = $product->is_on_sale();
                    $sale_percentage     = '';
                    $price_excluding_tax = wc_get_price_excluding_tax($product);
                    $price_including_tax = wc_get_price_including_tax($product);

                    // Calculate sale percentage if product is on sale
                    if ($is_on_sale && $product->get_regular_price() > 0) {
                        $regular_price   = floatval($product->get_regular_price());
                        $sale_price      = floatval($product->get_sale_price());
                        $percentage      = round(100 - $sale_price / $regular_price * 100);
                        $sale_percentage = "-{$percentage}%";
                    }

                    // Get product image
                    $image_url = get_the_post_thumbnail_url($product_id, 'medium');
                    if (! $image_url) {
                        $image_url = wc_placeholder_img_src('medium');
                    }
            ?>
                    <div class="galbud-promo-slider__item">
                        <?php if ($is_featured || $is_on_sale) : ?>
                            <ul class="galbud-promo-slider__badges galbud-promo-slider-badges">
                                <?php if ($is_featured) : ?>
                                    <li class="galbud-promo-slider-badges__item galbud-promo-slider-badges__item--featured">
                                        <span>Polecany</span>
                                    </li>
                                <?php endif; ?>
                                <?php if ($is_on_sale && $sale_percentage) : ?>
                                    <li class="galbud-promo-slider-badges__item galbud-promo-slider-badges__item--promo">
                                        <span><?php echo wp_kses($sale_percentage, $allowed_html); ?></span>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        <?php endif; ?>

                        <a class="galbud-promo-slider__item-link" href="<?php echo esc_url($link); ?>">
                            <img src="<?php echo esc_url($image_url); ?>" alt="<?php echo esc_attr($product_title); ?>">
                        </a>

                        <div class="galbud-promo-slider__item-content">
                            <h2 class="galbud-promo-slider__item-title"><?php echo wp_kses($product_title, $allowed_html); ?></h2>
                            <?php if ($sku) : ?>
                                <span class="galbud-promo-slider__item-sku"><?php echo wp_kses($sku, $allowed_html); ?></span>
                            <?php endif; ?>
                            <div class="galbud-promo-slider__item-price">
                                <?php if ($product->is_on_sale()) : ?>
                                    <div class="price-with-tax">
                                        <span class="price-label">Cena z VAT:</span>
                                        <del><?php echo wp_kses_post(wc_price(wc_get_price_including_tax($product, array('qty' => 1, 'price' => $product->get_regular_price())))); ?></del>
                                        <ins><?php echo wp_kses_post(wc_price($price_including_tax)); ?></ins>
                                    </div>
                                    <div class="price-without-tax">
                                        <span class="price-label">Cena netto:</span>
                                        <del><?php echo wp_kses_post(wc_price(wc_get_price_excluding_tax($product, array('qty' => 1, 'price' => $product->get_regular_price())))); ?></del>
                                        <ins><?php echo wp_kses_post(wc_price($price_excluding_tax)); ?></ins>
                                    </div>
                                <?php else : ?>
                                    <div class="price-with-tax">
                                        <span class="price-label">Cena z VAT:</span>
                                        <?php echo wp_kses_post(wc_price($price_including_tax)); ?>
                                    </div>
                                    <div class="price-without-tax">
                                        <span class="price-label">Cena netto:</span>
                                        <?php echo wp_kses_post(wc_price($price_excluding_tax)); ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <a href="<?php echo esc_url($link); ?>" class="galbud-button galbud-button--primary">Zobacz produkt</a>
                        </div>
                    </div>
            <?php
                }
            }
            ?>
        </section>
    </div>
<?php
endif;
?>