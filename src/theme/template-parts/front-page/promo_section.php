<?php

/**
 * Template part for displaying the Promo Section.
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package Galbud
 */

$allowed_html = get_query_var('allowed_html', array());
$section_data = get_query_var('section_data', array());

$background_image = ! empty($section_data['background_image']) ? $section_data['background_image'] : '';
$discount         = ! empty($section_data['discount']) ? $section_data['discount'] : '';
$title            = ! empty($section_data['title']) ? $section_data['title'] : '';
$text             = ! empty($section_data['text']) ? $section_data['text'] : '';
$button_text      = ! empty($section_data['button_text']) ? $section_data['button_text'] : '';
$button_link      = ! empty($section_data['button_link']) ? $section_data['button_link'] : '';

// Use default image if none provided
if (! $background_image) {
    $background_image = get_template_directory_uri() . '/assets/img/orionthemes-placeholder-image.jpg'; // Poprawiona ścieżka
}
?>
<div class="container">
    <section class="home-promo-section" style="background-image: url('<?php echo esc_url($background_image); ?>');">
        <?php if ($discount) : ?>
            <span class="home-promo-section-price"><?php echo wp_kses($discount, $allowed_html); ?></span>
        <?php endif; ?>

        <h2 class="home-promo-section-title"><?php echo wp_kses(nl2br($title), $allowed_html); ?></h2>

        <?php if ($text) : ?>
            <p class="home-promo-section-text"><?php echo wp_kses(nl2br($text), $allowed_html); ?></p>
        <?php endif; ?>

        <?php if ($button_text && $button_link) : ?>
            <div class="home-promo-section-text-buttons">
                <a href="<?php echo esc_url($button_link); ?>" class="button button-primary"><span><?php echo wp_kses($button_text, $allowed_html); ?></span></a>
            </div>
        <?php endif; ?>
    </section>
</div>