<?php

/**
 * Template part for displaying the Section Title.
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package Galbud
 */

$allowed_html = get_query_var('allowed_html', array());
$section_data = get_query_var('section_data', array());

$title    = ! empty($section_data['title']) ? $section_data['title'] : '';
$subtitle = ! empty($section_data['subtitle']) ? $section_data['subtitle'] : '';

if ($title) :
?>
    <section class="section-title">
        <div class="section-title__container container">
            <h2 class="section-title__text"><?php echo wp_kses($title, $allowed_html); ?></h2>
            <?php if ($subtitle) : ?>
                <div class="section-title__subtitle">
                    <?php echo wp_kses(nl2br($subtitle), $allowed_html); ?>
                </div>
            <?php endif; ?>
        </div>
    </section>
<?php
endif;
?>