<?php

/**
 * Template part for displaying the Tabs Section.
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package Galbud
 */

$allowed_html = get_query_var('allowed_html', array());
$section_data = get_query_var('section_data', array());
$tabs         = ! empty($section_data['tabs']) ? $section_data['tabs'] : array();

if (! empty($tabs)) :
?>
    <!-- tabs section -->
    <section id="tabs" class="tabs-container">
        <div class="container">
            <div class="tabs-header">
                <?php
                $tab_count = 0;
                foreach ($tabs as $tab_item) {
                    $tab_count++;
                    $tab_title    = ! empty($tab_item['tab_title']) ? $tab_item['tab_title'] : '';
                    $active_class = (1 === $tab_count) ? 'active' : '';
                ?>
                    <div class="tab-title <?php echo esc_attr($active_class); ?>" data-tab="tab<?php echo esc_attr($tab_count); ?>"><?php echo wp_kses($tab_title, $allowed_html); ?></div>
                <?php
                }
                ?>
            </div>
        </div>

        <div class="tabs-content">
            <?php
            $tab_count = 0;
            foreach ($tabs as $tab_item) {
                $tab_count++;
                $active_class = (1 === $tab_count) ? 'active' : '';
                $tab_content_layouts = ! empty($tab_item['tab_content']) ? $tab_item['tab_content'] : array();
            ?>
                <div id="tab<?php echo esc_attr($tab_count); ?>" class="tab-content <?php echo esc_attr($active_class); ?>">
                    <div class="container">
                        <?php
                        if (! empty($tab_content_layouts)) {
                            foreach ($tab_content_layouts as $content_layout) {
                                $layout_type = ! empty($content_layout['acf_fc_layout']) ? $content_layout['acf_fc_layout'] : '';

                                if ('text_content' === $layout_type) {
                                    $content = ! empty($content_layout['content']) ? $content_layout['content'] : '';
                                    echo wp_kses_post($content); // Using wp_kses_post for WYSIWYG content
                                } elseif ('image_content' === $layout_type) {
                                    $image   = ! empty($content_layout['image']) ? $content_layout['image'] : '';
                                    $caption = ! empty($content_layout['caption']) ? $content_layout['caption'] : '';
                                    $text    = ! empty($content_layout['text']) ? $content_layout['text'] : '';
                        ?>
                                    <div class="tab-image-content">
                                        <div class="tab-image-content__row">
                                            <div class="tab-image-content__image-col">
                                                <img src="<?php echo esc_url($image); ?>" alt="<?php echo esc_attr($caption); ?>">
                                                <?php if ($caption) : ?>
                                                    <div class="tab-image-content__caption"><?php echo wp_kses($caption, $allowed_html); ?></div>
                                                <?php endif; ?>
                                            </div>
                                            <?php if ($text) : ?>
                                                <div class="tab-image-content__text-col">
                                                    <?php echo wp_kses_post($text); ?> <!-- Using wp_kses_post for WYSIWYG content -->
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <?php
                                } elseif ('products_content' === $layout_type) {
                                    $title       = ! empty($content_layout['title']) ? $content_layout['title'] : '';
                                    $product_ids = ! empty($content_layout['products']) ? $content_layout['products'] : array();

                                    if ($product_ids && ! empty($product_ids)) {
                                    ?>
                                        <?php if ($title) : ?>
                                            <h3 class="tab-products-title"><?php echo wp_kses($title, $allowed_html); ?></h3>
                                        <?php endif; ?>

                                        <div class="tab-products-grid">
                                            <?php
                                            foreach ($product_ids as $product_id) {
                                                $product = wc_get_product($product_id);
                                                if ($product) {
                                                    $product_title = $product->get_name();
                                                    $price_html    = $product->get_price_html();
                                                    $link          = get_permalink($product_id);
                                                    $image_url     = get_the_post_thumbnail_url($product_id, 'medium');
                                                    if (! $image_url) {
                                                        $image_url = wc_placeholder_img_src('medium');
                                                    }
                                            ?>
                                                    <div class="tab-product-item">
                                                        <a href="<?php echo esc_url($link); ?>" class="tab-product-item__link">
                                                            <img src="<?php echo esc_url($image_url); ?>" alt="<?php echo esc_attr($product_title); ?>" class="tab-product-item__image">
                                                            <h4 class="tab-product-item__title"><?php echo wp_kses($product_title, $allowed_html); ?></h4>
                                                            <div class="tab-product-item__price"><?php echo wp_kses_post($price_html); ?></div>
                                                        </a>
                                                        <a href="<?php echo esc_url($link); ?>" class="button button--primary">Zobacz produkt</a>
                                                    </div>
                                            <?php
                                                }
                                            }
                                            ?>
                                        </div>
                                    <?php
                                    }
                                } elseif ('product_slider' === $layout_type) {
                                    $title       = ! empty($content_layout['title']) ? $content_layout['title'] : '';
                                    $product_ids = ! empty($content_layout['products']) ? $content_layout['products'] : array();

                                    if ($product_ids && ! empty($product_ids)) {
                                    ?>
                                        <?php if ($title) : ?>
                                            <h3 class="tab-products-title"><?php echo wp_kses($title, $allowed_html); ?></h3>
                                        <?php endif; ?>

                                        <div class="galbud-product-slider galbud-promo-slider">
                                            <?php
                                            foreach ($product_ids as $product_id) {
                                                $product = wc_get_product($product_id);
                                                if ($product) {
                                                    $product_title       = $product->get_name();
                                                    $sku                 = $product->get_sku();
                                                    $link                = get_permalink($product_id);
                                                    $is_featured         = $product->is_featured();
                                                    $is_on_sale          = $product->is_on_sale();
                                                    $sale_percentage     = '';
                                                    $price_excluding_tax = wc_get_price_excluding_tax($product);
                                                    $price_including_tax = wc_get_price_including_tax($product);

                                                    if ($is_on_sale && $product->get_regular_price() > 0) {
                                                        $regular_price = floatval($product->get_regular_price());
                                                        $sale_price    = floatval($product->get_sale_price());
                                                        $percentage      = round(100 - $sale_price / $regular_price * 100);
                                                        $sale_percentage = "-{$percentage}%";
                                                    }

                                                    $image_url = get_the_post_thumbnail_url($product_id, 'medium');
                                                    if (! $image_url) {
                                                        $image_url = wc_placeholder_img_src('medium');
                                                    }
                                            ?>
                                                    <div class="galbud-promo-slider__item">
                                                        <?php if ($is_featured || $is_on_sale) : ?>
                                                            <ul class="galbud-promo-slider__badges galbud-promo-slider-badges">
                                                                <?php if ($is_featured) : ?>
                                                                    <li class="galbud-promo-slider-badges__item galbud-promo-slider-badges__item--featured">
                                                                        <span>Polecany</span>
                                                                    </li>
                                                                <?php endif; ?>
                                                                <?php if ($is_on_sale && $sale_percentage) : ?>
                                                                    <li class="galbud-promo-slider-badges__item galbud-promo-slider-badges__item--promo">
                                                                        <span><?php echo wp_kses($sale_percentage, $allowed_html); ?></span>
                                                                    </li>
                                                                <?php endif; ?>
                                                            </ul>
                                                        <?php endif; ?>

                                                        <a class="galbud-promo-slider__item-link" href="<?php echo esc_url($link); ?>">
                                                            <img src="<?php echo esc_url($image_url); ?>" alt="<?php echo esc_attr($product_title); ?>">
                                                        </a>

                                                        <div class="galbud-promo-slider__item-content">
                                                            <h2 class="galbud-promo-slider__item-title"><?php echo wp_kses($product_title, $allowed_html); ?></h2>
                                                            <?php if ($sku) : ?>
                                                                <span class="galbud-promo-slider__item-sku"><?php echo wp_kses($sku, $allowed_html); ?></span>
                                                            <?php endif; ?>
                                                            <div class="galbud-promo-slider__item-price">
                                                                <?php if ($product->is_on_sale()) : ?>
                                                                    <div class="price-with-tax">
                                                                        <span class="price-label">Cena z VAT:</span>
                                                                        <del><?php echo wp_kses_post(wc_price(wc_get_price_including_tax($product, array('qty' => 1, 'price' => $product->get_regular_price())))); ?></del>
                                                                        <ins><?php echo wp_kses_post(wc_price($price_including_tax)); ?></ins>
                                                                    </div>
                                                                    <div class="price-without-tax">
                                                                        <span class="price-label">Cena netto:</span>
                                                                        <del><?php echo wp_kses_post(wc_price(wc_get_price_excluding_tax($product, array('qty' => 1, 'price' => $product->get_regular_price())))); ?></del>
                                                                        <ins><?php echo wp_kses_post(wc_price($price_excluding_tax)); ?></ins>
                                                                    </div>
                                                                <?php else : ?>
                                                                    <div class="price-with-tax">
                                                                        <span class="price-label">Cena z VAT:</span>
                                                                        <?php echo wp_kses_post(wc_price($price_including_tax)); ?>
                                                                    </div>
                                                                    <div class="price-without-tax">
                                                                        <span class="price-label">Cena netto:</span>
                                                                        <?php echo wp_kses_post(wc_price($price_excluding_tax)); ?>
                                                                    </div>
                                                                <?php endif; ?>
                                                            </div>
                                                            <a href="<?php echo esc_url($link); ?>" class="galbud-button galbud-button--primary">Zobacz produkt</a>
                                                        </div>
                                                    </div>
                                            <?php
                                                }
                                            }
                                            ?>
                                        </div>
                        <?php
                                    }
                                }
                            }
                        }
                        ?>
                    </div>
                </div>
            <?php
            }
            ?>
        </div>
    </section>
    <!-- end of tabs section -->
<?php
endif;
?>