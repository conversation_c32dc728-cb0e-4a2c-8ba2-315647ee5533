<?php

/**
 * Template part for displaying the Main Header section.
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package Galbud
 */

$allowed_html = get_query_var('allowed_html', array());
$section_data = get_query_var('section_data', array());

$title       = ! empty($section_data['title']) ? $section_data['title'] : '';
$description = ! empty($section_data['description']) ? $section_data['description'] : '';
$subtitle    = ! empty($section_data['subtitle']) ? $section_data['subtitle'] : '';
$button_text = ! empty($section_data['button_text']) ? $section_data['button_text'] : '';
$button_link = ! empty($section_data['button_link']) ? $section_data['button_link'] : '';
$image_url   = ! empty($section_data['image']) ? $section_data['image'] : '';

// Use default image if none provided
if (! $image_url) {
    $image_url = get_template_directory_uri() . '/assets/img/main.png'; // Poprawiona ścieżka do obrazka
}
?>
<!-- main header-->
<section class="main-slider">
    <div class="container">
        <div class="main-slider__row">
            <div class="main-slider__col">
                <div>
                    <h1 class="main-slider__title"><?php echo wp_kses(nl2br($title), $allowed_html); ?></h1>
                    <?php if ($description) : ?>
                        <p class="main-slider__description"><?php echo wp_kses($description, $allowed_html); ?></p>
                    <?php endif; ?>
                    <?php if ($subtitle) : ?>
                        <h2 class="main-slider__subtitle"><?php echo wp_kses($subtitle, $allowed_html); ?></h2>
                    <?php endif; ?>
                    <?php if ($button_text && $button_link) : ?>
                        <a href="<?php echo esc_url($button_link); ?>" class="button button--primary"><?php echo wp_kses($button_text, $allowed_html); ?></a>
                    <?php elseif ($button_text) : ?>
                        <button class="button button--primary"><?php echo wp_kses($button_text, $allowed_html); ?></button>
                    <?php endif; ?>
                </div>
            </div>
            <div class="main-slider__col parallax">
                <img src="<?php echo esc_url($image_url); ?>" class="main-slider__main-image" alt="<?php echo esc_attr($title); ?>">
            </div>
        </div>
    </div>
</section>
<!--main header-->