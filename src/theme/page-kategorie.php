<?php
get_header(); // Poprawiona nazwa funkcji

// Parametry pobierania kategorii
$paged = (get_query_var('paged')) ? get_query_var('paged') : 1;
$per_page = 18; // Kategorie na stronę
$offset = ($paged - 1) * $per_page;

// Sortowanie
$orderby = isset($_GET['orderby']) ? sanitize_text_field($_GET['orderby']) : 'name'; // Domyślnie sortuj po nazwie
$order = isset($_GET['order']) ? strtoupper(sanitize_text_field($_GET['order'])) : 'ASC'; // Domyślnie rosnąco

if (! in_array($orderby, array('name', 'count'))) {
    $orderby = 'name';
}
if (! in_array($order, array('ASC', 'DESC'))) {
    $order = 'ASC';
}

$args = array(
    'taxonomy'   => 'product_cat',
    'orderby'    => $orderby,
    'order'      => $order,
    'hide_empty' => true, // Ukryj puste kategorie
    'number'     => $per_page,
    'offset'     => $offset,
);

$product_categories = get_terms($args);
$total_categories = wp_count_terms('product_cat', array('hide_empty' => true)); // Całkowita liczba kategorii (tylko niepuste)
$total_pages = ceil($total_categories / $per_page);

// Użyj domyślnego obrazka zastępczego WooCommerce
$placeholder_image_url = function_exists('wc_placeholder_img_src') ? wc_placeholder_img_src('woocommerce_thumbnail') : '';

?>

<div class="container page-kategorie-container">

    <div class="category-controls">
        <h1><?php the_title(); ?></h1>

        <div class="category-sorting">
            <span>Sortuj według:</span>
            <a href="<?php echo esc_url(add_query_arg(array('orderby' => 'name', 'order' => 'asc'))); ?>" class="<?php echo ($orderby === 'name' && $order === 'ASC') ? 'active' : ''; ?>">Nazwa (A-Z)</a> |
            <a href="<?php echo esc_url(add_query_arg(array('orderby' => 'name', 'order' => 'desc'))); ?>" class="<?php echo ($orderby === 'name' && $order === 'DESC') ? 'active' : ''; ?>">Nazwa (Z-A)</a> |
            <a href="<?php echo esc_url(add_query_arg(array('orderby' => 'count', 'order' => 'desc'))); ?>" class="<?php echo ($orderby === 'count' && $order === 'DESC') ? 'active' : ''; ?>">Popularność (Najwięcej)</a> |
            <a href="<?php echo esc_url(add_query_arg(array('orderby' => 'count', 'order' => 'asc'))); ?>" class="<?php echo ($orderby === 'count' && $order === 'ASC') ? 'active' : ''; ?>">Popularność (Najmniej)</a>
        </div>
    </div>

    <div class="product-category-list">
        <?php
        if (! empty($product_categories) && ! is_wp_error($product_categories)) {
            foreach ($product_categories as $category) {
                $category_link = get_term_link($category);
                $thumbnail_id = get_term_meta($category->term_id, 'thumbnail_id', true);
                $image_url = $thumbnail_id ? wp_get_attachment_url($thumbnail_id) : $placeholder_image_url;
        ?>
                <div class="category-item">
                    <a href="<?php echo esc_url($category_link); ?>" class="category-item-link">
                        <div class="category-image-wrapper">
                            <img src="<?php echo esc_url($image_url); ?>" alt="<?php echo esc_attr($category->name); ?>" class="category-image">
                        </div>
                        <h3 class="category-name"><?php echo esc_html($category->name); ?></h3>
                        <span class="category-count">(<?php echo esc_html($category->count); ?> produktów)</span>
                    </a>
                </div>
        <?php
            }
        } else {
            echo '<p>Nie znaleziono kategorii produktów.</p>';
        }
        ?>
    </div>

    <?php
    // Paginacja
    if ($total_pages > 1) {
        echo '<div class="pagination">';
        echo paginate_links(array(
            'base'      => str_replace(PHP_INT_MAX, '%#%', esc_url(get_pagenum_link(PHP_INT_MAX))),
            'format'    => '?paged=%#%',
            'current'   => max(1, $paged),
            'total'     => $total_pages,
            'prev_text' => __('&laquo; Poprzednia'),
            'next_text' => __('Następna &raquo;'),
        ));
        echo '</div>';
    }
    ?>

</div>

<?php
get_footer();
