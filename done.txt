Zmodyfikowano plik src/plugins/optima-woocommerce/includes/class-wc-optima-product-fields.php, aby dodać obsługę pola optima_warehouse_id w kodzie JavaScript.
Zmodyfikowano wyświetlanie kategorii produktów, aby ukrywać puste kategorie w product-category-list i header-menu
Fixed WC_Optima_Logs::log_error() method by creating the missing class-wc-optima-logs.php file
Modified get_order_number() to remove hash from order numbers
Added new LOGS tab to plugin settings to display error logs
Usunięto wyświetlanie ID dokumentów Optima w sekcji edycji zamówienia
Dodano zapisywanie ID dokumentu RO jako _ro_document_id w metadanych zamówienia WooCommerce
Zaktualizowano komunikaty w notatkach zamówienia, aby informowały ookumentu RO
Dostosowano kod do obsługi HPOS (High-Performance Order Storage) w WooCommerce
Dodano wyświetlanie ID klienta Optima i ID dokumentu RO w sekcji integracji Optima w edycji zamówienia
Poprawiono tworzenie dokumentów RO zgodnie z dokumentacją - numer zamówienia w polu foreignNumber jest identyczny z numerem zamówienia WooCommerce
Naprawiono błąd agregacji dokumentu RO do typu 302 w integracji Optima - "Document type: 0 is not supported by this API"
Usunięto nieużywane pole optima_ro_document_id, pozostawiając tylko _ro_document_id dla identyfikacji dokumentów RO
Dodano zapisywanie wszystkich zapytań do systemu Optima w pliku logs.txt
Utworzono zakładkę LOGS w ustawieniach pluginu do przeglądania logów zapytań API
Dodano szczegółowe logowanie zapytań tworzenia dokumentów RO i agregacji dokumentów w logs.txt
Naprawiono funkcję agregacji dokumentów zgodnie z dokumentacją readme-optima - zmieniono endpoint na /DocumentsAggregation
Zaktualizowano funkcję agregacji dokumentów zgodnie z nową specyfikacją API DocumentsAggregation
Dodano przycisk do czyszczenia przeterminowanych akcji w Action Scheduler w ustawieniach pluginu
Usunięto zakładkę "Test Faktury" z panelu opcji wtyczki
Usunięto funkcjonalność testowania faktur
Usunięto handlery AJAX związane z fakturami
Usunięto kod JavaScript obsługujący faktury
Usunięto zakładkę "Klienci" z panelu administracyjnego
Usunięto funkcjonalność pobierania klientów z API Optima
Usunięto funkcjonalność dodawania przykładowego klienta
Usunięto kod JavaScript obsługujący operacje na klientach
Usunięto zakładkę "Invoices" z panelu administracyjnego
Usunięto zakładkę "Invoice History" z panelu administracyjnego
Usunięto funkcjonalność wyszukiwania i wyświetlania faktur
Dodano przekierowanie z usuniętych zakładek na zakładkę Synchronizacja
Dodano możliwość wyszukiwania dokumentów RO po różnych kryteriach (ID, numer obcy, pełny numer, data) z możliwością łączenia wielu kryteriów jednocześnie
Zmieniono zakładkę "Dokumenty RO" na "Dokumenty" z możliwością wyboru typu dokumentu do wyszukiwania
Poprawiono obsługę błędów API w funkcji pobierania dokumentów
Usunięto funkcjonalność "Pobierz ostatnie dokumenty" z panelu administracyjnego
Zaimplementowano standardowy select dla wyboru typu dokumentu w wyszukiwaniu dokumentów
Dodano możliwość wyszukiwania dokumentów tylko po typie dokumentu
Naprawiono błąd bazy danych przy wyszukiwaniu dokumentów
Usunięto funkcjonalność logowania w bazie danych, pozostawiając tylko logi w pliku tekstowym
Ustawiono domyślną wartość pola wyboru typu dokumentu na pustą wartość
Zmodyfikowano pliki src/theme/woocommerce/content-product-listing.php i src/theme/single-product.php, aby odznaka "badge-featured" była wyświetlana tylko wtedy, gdy produkt jest oznaczony jako "featured" w WooCommerce.
Zmodyfikowano pliki src/theme/woocommerce/content-product-listing.php i src/theme/single-product.php, aby wyświetlać regularną cenę przekreśloną oraz cenę promocyjną, jeśli produkt ma cenę promocyjną.
Dodano wyświetlanie wagi produktu w sekcji wymiarów na stronie szczegółów produktu
Zmodyfikowano wyświetlanie szerokiego obrazu produktu, aby nie pokazywał się gdy nie jest dodany przez ACF
Zmodyfikowano widok produktu, aby pokazywał "Produkt niedostępny" i czerwone kółko zamiast "Produkt dostępny" gdy produkt jest niedostępny
Ukryto formularz dodania do koszyka gdy produkt jest niedostępny
Dodano stronę wyszukiwania produktów wykorzystującą content-product-listing.php
Zmodyfikowano sekcję podobnych produktów na stronie produktu, aby używała szablonu content-product-listing.php
Zmodyfikowano sekcję podobnych produktów na stronie koszyka, aby używała szablonu content-product-listing.php
Usunięto nieużywane pliki class-wc-optima-invoice.php i class-wc-optima-invoice-history.php
Usunięto komentarze HTML z formularzy wyszukiwania dokumentów
Usunięto zbędne puste linie w kodzie
Usunięto komentarze wskazujące na usunięte funkcjonalności
Usunięto klasę WC_Optima_Filters zachowując funkcjonalność filtra wc_optima_should_sync_product
Dodano wykresy w zakładce synchronizacja w ustawieniach pluginu do wizualizacji danych ostatniej aktualizacji przy użyciu Chart.js
Zmieniono warunek usuwania produktów, aby usuwać tylko gdy cena jest nieprawidłowa I jednocześnie stan magazynowy wynosi zero
Przeniesiono funkcjonalność "Wymuś ponowne zaplanowanie zadania Cron" do zakładki ustawień i dodano możliwość wyboru godziny codziennej aktualizacji produktów
Dodano funkcjonalność zapobiegającą nadpisywaniu kategorii, które zostały już pobrane i zmodyfikowane
Ulepszono system logowania, dodając przechwytywanie komunikatów error_log() do pliku logs.txt
Poprawiono wyświetlanie logów w panelu administracyjnym - sortowanie od najnowszych, zwijane wpisy, kolorowe oznaczenia typów logów
Utworzono brakujący plik class-wc-optima-order-completed.php, aby naprawić błąd krytyczny w integracji Optima WooCommerce
Dodano funkcjonalność zapisywania statystyk importu z datami i możliwość wyświetlania szczegółów poszczególnych importów
Dodano nową zakładkę "Zaawansowane funkcje pomocnicze" w ustawieniach pluginu Optima WooCommerce z przyciskiem do usuwania wszystkich produktów i powiązanych danych z WooCommerce
Ulepszono funkcję process_stock_data, aby obsługiwała różne formaty danych magazynowych z API Optima, dodano obsługę różnych struktur danych i lepsze logowanie
Zmieniono logikę synchronizacji produktów, aby nie usuwać produktów z nieprawidłową ceną lub zerowym stanem magazynowym, a zamiast tego oznaczać je jako niedostępne
