{"name": "wordpressify", "version": "0.5.0-14", "description": "Automate your WordPress development workflow.", "keywords": ["babel", "boilerplate", "build", "css", "cherry", "es6", "front-end", "gulp", "javascript", "node", "nodejs", "postcss", "sass", "web", "website", "wordpress", "wordpressify", "workflow", "docker", "docker compose"], "homepage": "https://www.wordpressify.co", "repository": "https://github.com/luangjokaj/wordpressify", "type": "module", "node": "^12.20.0 || ^14.13.1 || >=16.0.0", "scripts": {"dev": "gulp dev --stack-size=81920", "prod": "gulp prod --stack-size=81920", "backup": "gulp backup --stack-size=81920", "lint:css": "stylelint src/**/*.css", "start": "docker-compose up", "build": "docker-compose build", "delete": "docker-compose down -v", "rebuild": "docker-compose down -v && docker compose build", "export": "docker-compose run --rm nodejs npm run prod", "export:backup": "docker-compose run --rm nodejs npm run backup", "lintcss": "docker-compose run --rm nodejs npm run lint:css"}, "author": "<PERSON><PERSON> <<EMAIL>>", "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://www.riangle.com/"}, {"name": "<PERSON>", "url": "https://github.com/ribaricplusplus"}, {"name": "Mountain/\\Ash", "url": "https://github.com/mountainash"}], "license": "MIT", "dependencies": {"@babel/core": "^7.26.9", "@babel/preset-env": "^7.26.9", "autoprefixer": "^10.4.20", "browser-sync": "^3.0.3", "chokidar": "^4.0.3", "cssnano": "^7.0.6", "del": "^8.0.0", "fancy-log": "^2.0.0", "gulp": "^5.0.0", "gulp-babel": "^8.0.0", "gulp-clean-css": "^4.3.0", "gulp-concat": "^2.6.1", "gulp-plumber": "^1.2.1", "gulp-postcss": "^10.0.0", "gulp-sourcemaps": "^3.0.0", "gulp-uglify": "^3.0.2", "gulp-vinyl-zip": "^2.5.0", "jquery": "^3.7.1", "postcss": "^8.5.3", "postcss-easy-import": "^4.0.0", "postcss-mixins": "^11.0.3", "postcss-preset-env": "^10.1.5", "stylelint": "^16.14.1"}}