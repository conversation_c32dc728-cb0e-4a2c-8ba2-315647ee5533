# API Comarch ERP Optima v. Professional / Multi-Company (v2)

This document describes the API functionalities for Comarch ERP Optima v. Professional / Multi-Company.

## Authentication

* **Method:** OAuth 2.0
* **Token Endpoint:** `{BASE_URL}/api/Token`
    * **Method:** POST
    * **Body (x-www-form-urlencoded):**
        * `username`: (string, required) - Provided by ELTE-S
        * `password`: (string, required) - Provided by ELTE-S
        * `grant_type`: (string, required) - Must be "password"
* **Usage:** Obtain a Bearer Token via the Token endpoint and include it in the `Authorization` header for subsequent requests (e.g., `Authorization: Bearer <token>`). Some endpoints might use OAuth 2.0 directly as noted.

## Base URL Placeholder

* `{BASE_URL}`: Represents the base URL of the API instance (e.g., `http://YOUR_IP/api/`).

## Possible Server Responses

* **200 OK:** Request successful.
* **201 Created:** Resource created successfully.
* **300 Multiple Choices:**
* **400 Bad Request:** Error due to incorrect data sent by the user.
* **401 Unauthorized:** Authentication failed or missing.
* **404 Not Found:** Resource not found.
* **500 Internal Server Error:** Unexpected problems while executing the query.

## Supported Document Types

| Type  | Description                       |
| :---- | :-------------------------------- |
| 301   | Purchase Invoice                  |
| 302   | Invoice                           |
| 303   | Internal Receipts                 |
| 304   | Internal Releases                 |
| 305   | Receipt                           |
| 306   | Sales Order Releases              |
| 307   | Purchase Order Receipts           |
| 308   | Sales Orders                      |
| 309   | Purchase Order                    |
| 312   | Warehouse Movements               |
| 317   | Internal Product Acceptance       |
| 320   | Pro Forma Invoice                 |
| 350   | Advance Invoice                   |
*(Source:)*

## Supported Correction Kinds

| Kind     | Description                                     | Document Types Applicable |
| :------- | :---------------------------------------------- | :------------------------ |
| 301001   | Quantity correction for Purchase Invoices       | 301                       |
| 301002   | Value correction for Purchase Invoices          | 301                       |
| 302001   | Quantity correction for Invoices                | 302                       |
| 302002   | Value correction for Invoices                   | 302                       |
| 302011   | Data correction for Invoices                    | 302                       |
| 306001   | Quantity correction for Sales Order Releases    | 306                       |
| 306002   | Value correction for Sales Order Releases       | 306                       |
| 307001   | Quantity correction for Purchase Order Receipts | 307                       |
| 307002   | Value correction for Purchase Order Receipts    | 307                       |
*(Source:)*

## List of Available Functionalities

### 1. Configurations

* **POST `{BASE_URL}/Token`**
    * Description: Creates an Access Key (Bearer Token). (See Authentication section).
* **GET `{BASE_URL}/ApiInformations`**
    * Description: Downloads information about the API (e.g., versions, modules).
* **GET `{BASE_URL}/CheckConnections`**
    * Description: Checks the connection status.
* **POST `{BASE_URL}/CustomizeDatabase`**
    * Description: Updates procedures in the database.

### 2. Accounts

* **GET `{BASE_URL}/Accounts`**
    * Description: Downloads information about Accounts.
    * Query Parameters: `accountID`, `parentId`, `accountNumber`, `segment`, `currency`, `accountingPeriodSymbol`, `databaseName`, `limit`, `offset`.
    * Company Data Parameters: `companyName`, `databaseName`.
    * Response Fields: `accountID`, `costCorrect`, `budgetControl`, `parentId`, `accountNumber`, `accountNumberIndex`, `segment`, `name1`, `name2`, `currency`, `accountType`, `withSettlement`, `withoutSettlement`, `ballanceControl`.
* **POST `{BASE_URL}/Accounts`**
    * Description: Creates an Account.
    * Body (JSON): Includes fields like `costCorrect`, `budgetControl`, `accountNumberIndex`, `segment`, `name1`, `name2`, `currency`, `accountType`, etc..
    * Company Data Fields: `companyName`, `databaseName`.

### 3. AccountsForCustomer

* **GET `{BASE_URL}/AccountsForCustomers`**
    * Description: Downloads customer account information.
    * Query Parameters: `id`, `username`, `vatNumber`.
* **POST `{BASE_URL}/AccountsForCustomers`**
    * Description: Creates customer accounts.
    * Body (JSON): Array of objects with `username`, `password`, `vatNumber`.
* **PUT `{BASE_URL}/AccountsForCustomers`**
    * Description: Modifies customer accounts.
    * Body (JSON): Array of objects with `username`, `password`, `vatNumber`.
* **DELETE `{BASE_URL}/AccountsForCustomers`**
    * Description: Deletes a customer account.
    * Query Parameters: `username`.

### 4. Additionals

* **GET `{BASE_URL}/Additions`**
    * Description: Downloads information about Additions (e.g., for employees).
    * Query Parameters: `employeeId`, `inactive`, `databaseName`.
    * Response Fields: `id`, `employeeId`, `typeId`, `type`, `name`, `inactive`, `contract`, `sicknessInclude`.
* **GET `{BASE_URL}/AdditionsValues`**
    * Description: Downloads information about Addition Values.
    * Query Parameters: `additionId`, date ranges (`periodStartFrom`, `periodStartTo`, etc.), `databaseName`.
    * Response Fields: `id`, `additionId`, `periodFrom`, `periodTo`, `validFrom`, `validTo`, `value1`, `value2`, `name`, `description`.
* **POST `{BASE_URL}/AdditionalRecords`**
    * Description: Creates Additional Records (e.g., revenue/cost).
    * Body (JSON): Includes fields like `type` (101-Revenue, 102-Cost), `contractor`, `register`, `recordDate`, `elements` (with `category1`, `amount`, `accountWn`, `accountMa`), etc..
    * Company Data Fields: `companyName`, `databaseName`.

### 5. Analyticals

* **GET `{BASE_URL}/AnalyticalDescriptions`**
    * Description: Downloads data with analytical values.
    * Query Parameters: `documentId`, `offset`, `limit`, `databaseName`.
    * Response Fields: `documentId`, `elements` (list of `lp`, `positionValue`, `percentage`, `value`, `setPercentage`, `options`), `companyData`.
* **GET `{BASE_URL}/AnalyticalDescriptionDictionaries`**
    * Description: Downloads Analytical Description Dictionaries.
    * Query Parameters: `documentId` (required), `companyName` (required).
* **POST `{BASE_URL}/AnalyticalDescriptions`**
    * Description: Creates additional analytical values.
    * Body (JSON): Includes `documentId`, `elements` (list with `positionValue`, `percentage`/`value`, `options` like `dimensionName`, `dimensionValue`).
    * Company Data Fields: `companyName`, `databaseName`.

### 6. Attributes

* **GET `{BASE_URL}/AttributeDefinitions`**
    * Description: Downloads Attribute Definitions data.
    * Query Parameters: `id`, `type` (1-product, 2-customer, 3-asset, 4-document, 5-JPK).
    * Response Fields: `id`, `code`, `name`, `type`, `format` (1-text, 2-number, 3-list, 4-date, 5-binary), `values`.
* **POST `{BASE_URL}/AttributeDefinitions`**
    * Description: Creates Attribute Definitions.
    * Body (JSON): Includes `code`, `name`, `format`, `type`, `values` (list of `value`).

### 7. AllowIssuedReservedProducts

* **POST `{BASE_URL}/AllowIssuedReservedProducts`**
    * Description: Changes the "Do not allow goods to be issued when there are reservations" parameter.
    * Body (JSON): `{ "value": (int32, 0 or 1) }`.

### 8. Banks

* **GET `{BASE_URL}/BankStatements`**
    * Description: Downloads data from bank statements.
    * Query Parameters: `id`, `fullNumber`, `foreignNumber`, date ranges (`issueDateStart`, `issueDateEnd`, etc.), `full_number` (comma-separated list), `foreign_number` (comma-separated list), `booked`, `optima_id` (comma-separated list), `databaseName`.
    * Response Fields: `id`, `fullNumber`, `foreignNumber`, `documentTypeId`, `issueDate`, `closeDate`, `balance`, `revenue`, etc..
* **GET `{BASE_URL}/BankAccountNumbers`**
    * Description: Downloads Bank Account Numbers information.
    * Query Parameters: `id`, `customerId`, `offset`, `limit`, date ranges (`c_dateFrom`, `c_dateTo`, etc.), `databaseName`.
    * Response Fields: `id`, `customerId`, `accountNumber`, `iban`, `description`, `defaultAccountNumber`, `created`, `updated`, `companyData`.
* **POST `{BASE_URL}/BankAccountNumbers`**
    * Description: Creates Bank Account Numbers.
* **PUT `{BASE_URL}/BankAccountNumbers`**
    * Description: Modifies Bank Account Numbers.

### 9. Categories

* **GET `{BASE_URL}/Categories`**
    * Description: Downloads information about Categories.

### 10. CivilLaws

* **POST `{BASE_URL}/AddCivilLawContracts`**
    * Description: Creates Civil Law Contracts.
    * Body (JSON): Includes `employeeAbbreviation`, `singingDate`, `conclusionDate`, `nettoValue`, etc..
    * Company Data Fields: `companyName`, `databaseName`.

### 11. Contracts

* **GET `{BASE_URL}/Contracts`**
    * Description: Downloads information about Contracts.
* **GET `{BASE_URL}/ContractsInsurances`**
    * Description: Downloads information about Contract Insurances.
* **PUT `{BASE_URL}/ContractsInsurances`**
    * Description: Modifies Contract Insurances.

### 12. CostCenters

* **GET `{BASE_URL}/CostCenters`**
    * Description: Downloads information about Cost Centers. (Note: File shows GET `/Contracts` for Cost Centers description, likely a typo, using `/CostCenters` as endpoint).

### 13. Customers

* **GET `{BASE_URL}/Customers`**
    * Description: Downloads information about Customers.
    * Query Parameters: `id`, `code`, `vatNumber`, `paymentMethod`, `offset`, `limit`, date ranges, `databaseName`.
* **POST `{BASE_URL}/Customers`**
    * Description: Creates a Customer.
    * Body (JSON): Includes `code`, `name1`, `vatNumber`, `address` details, `paymentMethod`, etc..
* **PUT `{BASE_URL}/Customers`**
    * Description: Modifies a Customer.
    * Body (JSON): Includes `id` or `code` (required), and fields to modify.

### 14. Departments

* **GET `{BASE_URL}/Departments`**
    * Description: Downloads information about Departments.

### 15. Deliveries

* **GET `{BASE_URL}/Deliveries`**
    * Description: Downloads delivery history.
    * Query Parameters: `transactionId`, `warehouseId`, `deliveryId`, `itemId`, `documentId`, `elementId`, `deliveryIndication`, date ranges, `offset`, `limit`, `databaseName`, `type` (1-delivery, 2-expenditure, 3-reservation, 4-order).
* **PUT `{BASE_URL}/Deliveries`**
    * Description: Updates element delivery in an existing Document.
    * Body (JSON): Includes `documentId`, `elements` (list with `elementId`, `deliveries` list).

### 16. DocumentsLibrary / Documents

* **GET `{BASE_URL}/Documents`**
    * Description: Downloads information about Documents.
    * Query Parameters: Include various filters like `id`, `type`, `fullNumber`, `foreignNumber`, date ranges, `customerId`, `warehouseId`, `status`, `attributeCode`, `attributeValue`, etc..
* **GET `{BASE_URL}/RelatedDocument`**
    * Description: Downloads Related Document information.
* **GET `{BASE_URL}/DocumentsExport`**
    * Description: Downloads information about Document Exports.
* **GET `{BASE_URL}/DocumentsFiscalize`**
    * Description: Downloads information about Document Fiscalization.
* **GET `{BASE_URL}/Settlements` / `{BASE_URL}/DocumentsSettlement`**
    * Description: Downloads settlement information for documents.
    * Query Parameters: `id`, `documentId`, `customerId`, date ranges, `limit`, `offset`, `databaseName`.
* **GET `{BASE_URL}/DocumentStatus`**
    * Description: Downloads Document Status information.
* **GET `{BASE_URL}/VatDocuments`**
    * Description: Downloads VAT Document information.
* **POST `{BASE_URL}/DocumentsLibrary`**
    * Description: Creates a Document Library entry (attaches a file to a document).
    * Body (JSON): Includes `docId`, `fileName`, `fileExtension`, `fileBinary`.
* **POST `{BASE_URL}/DocumentsAggregation`**
    * Description: Creates Document Aggregation (relates documents).
    * Body (JSON): Includes `sourceDocumentId`, `sourceType`, `targetType`, optional `elements`.
* **POST `{BASE_URL}/DocumentsCorrection`**
    * Description: Creates a Document Correction. (See Supported Correction Kinds).
    * Body (JSON): Includes `sourceDocumentId`, `type`, `kind`, correction dates, `elements` (with `id`, `code`, `quantity`, potentially prices), `payer`/`recipient` details if changed.
* **POST `{BASE_URL}/Documents`**
    * Description: Creates various types of Documents. (See Supported Document Types).
    * Body (JSON): Includes `type`, `warehouseId`, `calculatedOn`, `paymentMethod`, `currency`, `payer`/`recipient`/`defaultPayer` info, document dates, `elements` (with `code`/`itemId`, `quantity`, `price`, `vatRate`), `attributes`.

### 17. Employees

* **GET `{BASE_URL}/EmployeeAttribiutes`**
    * Description: Downloads information about Employee Attributes.
* **GET `{BASE_URL}/Employees`**
    * Description: Downloads information about Employees.

### 18. Employments

* **GET `{BASE_URL}/Employments`**
    * Description: Downloads information about Employments.

### 19. Entities

* **GET `{BASE_URL}/Entities`**
    * Description: Downloads information about Entities.
* **GET `{BASE_URL}/EntitiesAttribute`**
    * Description: Downloads information about Entity Attributes.
    * Query Parameters: `attrDefinitionId`, `entityType`, `attrValue` (comma-separated), `entityId` (comma-separated), `databaseName`.

### 20. Exchange

* **GET `{BASE_URL}/ExchangeRateTypes`**
    * Description: Downloads exchange rate types.

*(Note: The list continues with Items, Invoices, Inventory, Journal, etc. based on the provided file structure, following a similar pattern of GET/POST/PUT/DELETE operations for each resource)*.