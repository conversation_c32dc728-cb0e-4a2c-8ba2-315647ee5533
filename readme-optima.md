# API Comarch ERP Optima v. Professional / Multi-Company

# 2.

## Possible server responses:

## Supported document types :

## Supported correction kinds :

```
Code Description
```
```
200 OK
```
```
201 Created
```
```
300 Multiple choices
```
```
400 Error due to incorrect data sent by the user.
```
```
401 Unauthorized
```
```
404 Not found
```
```
500 Unexpected problems while executing the query
```
```
Type Description
```
```
301 Purchase Invoice
```
```
302 Invoice
```
```
303 Internal Receitps
```
```
304 Internal Releases
```
```
305 Receipt
```
```
306 Sales Order Releases
```
```
307 Purchase Order Receipts (Only Add document and
Add correction)
```
```
308 Sales Orders
```
```
309 Purchase Order
```
```
312 Warehouse Movements
```
```
317 Internal Product Acceptance
```
```
320 Pro Forma Invoice
```
```
350 Advance Invoice
```

### List of Available Functionalities :

1. Configurations :
2. Accounts :
3. Additionals :
4. Analyticals :

```
Kind Description
```
```
301001 Quantity correction for Purchase Invoices
```
```
301002 Value correction for Purchase Invoices
```
```
302001 Quantity correction for Invoices
```
```
302002 Value correction for Invoices
```
```
306001 Quantity correction for Sales Order Releases
```
```
306002 Value correction for Sales Order Releases
```
```
307001 Quantity correction for Purchase Order Receipts
```
```
307002 Value correction for Purchase Order Receipts
```
```
Definition Http Commands
```
```
Function Allows you to Create an Access Key [POST] - Token
```
```
Function Allows you to Download Information about
api
```
```
[GET] - ApiInformation
```
```
Function Allows you to Download Information about
Connection
```
```
[GET] - CheckConnection
```
```
Definition Http Commands
```
```
Function Allows you to Download Information about
Accounts
```
```
[GET] - Accounts
```
```
Fuction Allows you to Create a Account [POST] - Accounts
```
```
Definition Http Commands
```
```
Fuction Allows you to Download Information about
Additions
```
```
[GET] - Additions
```
```
Fuction Allows you to Download Information about
AdditionsValues
```
```
[GET] - AdditionsValues
```
```
Fuction Allows you to Create a AdditionalRecords [POST] - AdditionalRecords
```

5. Banks :
6. Categories :
7. CivilLaws :
8. Contracts :
9. CostCenters :

```
Definition Http Commands
```
```
Fuction Allows you to Download Information about
AnalyticalDescription
```
```
[GET] - AnalyticalDescription
```
```
Fuction Allows you to Download Information about
AnalyticalDescriptionDictionaries
```
```
[GET] - AnalyticalDescriptionDictionaries
```
```
Fuction Allows you to Create a AnalyticalDescription [POST] - AnalyticalDescription
```
```
Definition Http Commands
```
```
Fuction Allows you to Download Information about
BankStatements
```
```
[GET] - BankStatements
```
```
Fuction Allows you to Download Information about
BankAccountNumbers
```
```
[GET] - BankAccountNumbers
```
```
Fuction Allows you to Create a
BankAccountNumbers
```
```
[POST] - BankAccountNumbers
```
```
Fuction Allows you to Modification of
BankAccountNumbers
```
```
[PUT] - BankAccountNumbers
```
```
Definition Http Commands
```
```
Fuction Allows you to Download Information about
Categories
```
```
[GET] - Categories
```
```
Definition Http Commands
```
```
Fuction Allows you to Create a
AddCivilLawContracts
```
```
[POST] - AddCivilLawContracts
```
```
Definition Http Commands
```
```
Fuction Allows you to Download Information about
Contracts
```
```
[GET] - Contracts
```
```
Fuction Allows you to Download Information about
ContractsInsurances
```
```
[GET] - ContractsInsurances
```
```
Fuction Allows you to Modification of
ContractsInsurances
```
```
[PUT] - ContractsInsurances
```

10. Customers :
11. Departments :
12. Deliveries :
13. DocumentsLibrary :

```
Definition Http Commands
```
```
Fuction Allows you to Download Information about
Contracts
```
```
[GET] - CostCenters
```
```
Definition Http Commands
```
```
Fuction Allows you to Download Information about
Customers
```
```
[GET] - Customers
```
```
Fuction Allows you to Create a Customers [POST] - Customers
```
```
Fuction Allows you to Modification of Customers [PUT] -Customers
```
```
Definition Http Commands
```
```
Fuction Allows you to Download Information about
Departments
```
```
[GET] - Departments
```
```
Definition Http Commands
```
```
Fuction Allows you to Download Information about
Deliveries
```
```
[GET] - Deliveries
```
```
Definition Http Commands
```
```
Fuction Allows you to Download Information about
Documents
```
```
[GET] - Documents
```
```
Function Allows you to Download Related Document [GET] - RelatedDocument
```
```
Fuction Allows you to Download Information about
DocumentsExport
```
```
[GET] - DocumentsExport
```
```
Fuction Allows you to Download Information about
DocumentsFiscalize
```
```
[GET] - DocumentsFiscalize
```
```
Fuction Allows you to Download Information about
Settlements
```
```
[GET] - Settlements
```
```
Fuction Allows you to Download Information about
DocumentStatus
```
```
[GET] - DocumentStatus
```
```
Fuction Allows you to Download Information about
VatDocuments
```
```
[GET] - VatDocuments
```
```
Fuction Allows you to Create a DocumentsLibrary [POST] - DocumentsLibrary
```
```
Fuction Allows you to Create a [POST] - DocumentsAggregation
```

14. Employees :
15. Employments :
16. Entities :
17. Items :
18. Invoices :
19. Inventory :

```
DocumentsAggregation
```
```
Fuction Allows you to Create a DocumentsCorrection [POST] - DocumentsCorrection
```
```
Definition Http Commands
```
```
Fuction Allows you to Download Information about
EmployeeAttribiutes
```
```
[GET] - EmployeeAttribiutes
```
```
Fuction Allows you to Download Information about
Employees
```
```
[GET] - Employees
```
```
Definition Http Commands
```
```
Fuction Allows you to Download Information about
Employments
```
```
[GET] - Employments
```
```
Definition Http Commands
```
```
Fuction Allows you to Download Information about
Entities
```
```
[GET] - Entities
```
```
Fuction Allows you to Download Information about
EntitiesAttribute
```
```
[GET] - EntitiesAttribute
```
```
Definition Http Commands
```
```
Fuction Allows you to Download Information about
Items
```
```
[GET] - Items
```
```
Fuction Allows you to Download Information about
ItemsGroups
```
```
[GET] - ItemsGroups
```
```
Fuction Allows you to Create a Items [POST] - Items
```
```
Fuction Allows you to Modification of Items Chosen
by Id
```
```
[PUT] - Items{Id}
```
```
Fuction Allows you to Delete of Items Chosen by Id [DELETE] - Items{Id}
```
```
Definition Http Commands
```
```
Fuction Allows you to Download Information about
InvoiceItems
```
```
[GET] - InvoiceItems
```
```
Fuction Allows you to Download Information about
Invoices
```
```
[GET] - Invoices
```

20. Journal :
21. OcrFile :
22. Prices :
23. PayCheck :
24. Payments :

```
Definition
```
```
Fuction Allows you to Modification of
RealizeInventories
```
```
[PUT] - RealizeInventories
```
```
Definition Http Commands
```
```
Fuction Allows you to Create a Journal [POST] - Journal
```
```
Definition Http Commands
```
```
Fuction Allows you to Create a OcrFile [POST] - OcrFile
```
```
Definition Http Commands
```
```
Fuction Allows you to Download Information about
Prices
```
```
[GET] - Prices
```
```
Fuction Allows you to Modification of Prices [PUT] - Prices
```
```
Definition Http Commands
```
```
Fuction Allows you to Download Information about
PaycheckComponents
```
```
[GET] - PaycheckComponents
```
```
Fuction Allows you to Download Information about
Paychecks
```
```
[GET] - Paychecks
```
```
Fuction Allows you to Download Information about
PaycheckItems
```
```
[GET] - PaycheckItems
```
```
Definition Http Commands
```
```
Fuction Allows you to Download Information about
PaymentMethods
```
```
[GET] - PaymentMethods
```
```
Fuction Allows you to Download Information about
Payments
```
```
[GET] - Payments
```
```
Fuction Allows you to Create a Payments [POST] - Payments
```
```
Fuction Allows you to Create a PaymentsSettlement [POST] - PaymentsSettlement
```
```
Fuction Allows you to Modification of Payments
Chosen by Id
```
```
[PUT] - Payments{Id}
```

25. Resources :
26. Stocks :
27. Transfers :
28. UnitsOfMeasures :
29. WareHouses :
30. Vat :
31. Query :

```
Definition Http Commands
```
```
Fuction Allows you to Download Information about
Resources
```
```
[GET] - Resources
```
```
Definition Http Commands
```
```
Fuction Allows you to Download Information about
Stocks
```
```
[GET] - Stocks
```
```
Definition Http Commands
```
```
Fuction Allows you to Download Information about
Transfers
```
```
[GET] - Transfers
```
```
Fuction Allows you to Modification of Transfers [PUT] - Transfers
```
```
Definition Http Commands
```
```
Fuction Allows you to Download Information about
UnitOfMeasures
```
```
[GET] - UnitOfMeasures
```
```
Definition Http Commands
```
```
Fuction Allows you to Download Information about
Warehouses
```
```
[GET] - Warehouses
```
```
Fuction Allows you to Create a Warehosue [POST] - Warehouses
```
```
Fuction Allows you to Modification of Warehouses
Chosen by Id
```
```
[PUT] - Warehouses{Id}
```
```
Fuction Allows you to Delete a Warehouses Chosen
by Id
```
```
[DELETE] - Warehouses{Id}
```
```
Definition HttpCommands
```
```
Fuction Allows you to Create a VatRegister [POST] - VatRegister
```
```
Definition HttpCommands
```
```
Function Allows you to Perform any Select Query [POST] - AutomaticQuery
```

#### AUTHORIZATION OAuth 2.

#### AUTHORIZATION OAuth 2.

This folder is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.

```
http://{{API}}/api/Token
```
#### AUTHORIZATION OAuth 2.

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.

#### Body urlencoded

**username** String

**password** String

**grant_type** password

## Configurations

### POST http://YOUR_IP/api/Token

#### Function possible to create a token for use with endpoints

To enter the authorization data the appropriate place, you must enter:

```
Body and X-WWW-FORM-URLENCODED section, data marked with ellipsis, grant_type always must be
password
```
```
Key Value
```
```
username ...
```
```
password ...
```
```
grant_type password
```
```
Username sent by ELTE-S
```
```
Password sent by ELTE-S
```
```
Value Password
```
### GET http://YOUR_IP/api/ApiInformations


```
http://{{API}}/api/ApiInformations
```
#### AUTHORIZATION OAuth 2.

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.

```
http://{{API}}/api/CheckConnections
```
#### AUTHORIZATION OAuth 2.

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.

```
http://{{API}}/api/CustomizeDatabase
```
#### AUTHORIZATION Bearer Token

**Token** <token>

#### AUTHORIZATION OAuth 2.

This folder is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.

```
http://{{API}}/api/Accounts
```
#### The function allows you to download information for configuration needs, such as:

```
optima version,
api version,
modules.
```
### GET http://YOUR_IP/api/CheckConnections

#### This function allows you to check the connection status

### POST http://YOUR_IP/api/CustomizeDatabase

This method allows us to update procedure in database.

## Accounts

### GET http://YOUR_IP/api/Accounts


#### AUTHORIZATION Bearer Token

**Token** <token>

#### PARAMS

#### This function allows you to download Account Data from the database

**Body Response :**

**Company Data Fields :**

**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Definition
```
```
Int32 accountID Account Id
```
```
Int32 costCorrect Active Cost Adjustment
```
```
Int32 budgetControl Account bid Control
```
```
Int32 parentId Parent Id
```
```
String accountNumber Account Number
```
```
String accountNumberIndex Acount Number Index
```
```
String segment Account Segment Number
```
```
String name1 First row in the Name
```
```
String name2 Secound row in the Name
```
```
String currency Currency
```
```
Int32 accountType Type of Account
```
```
Int32 withSettlement Settlement Account
```
```
Int32 withoutSettlement A non-Settlement Account
```
```
Int32 ballanceControl Control your Account Balance
```
```
Type Name Definition Length Required
```
```
String companyName Chosen Company
Name for
Accounts
```
```
None No
```
```
String databaseName Chosen
Database Name
for Accounts
```
```
None No
```

**accountID** Int

**parentId** Int

**accountNumber** String

**segment** String

**currency** String

**accountingPeriodSymbol** String

**databaseName** String

**limit** Int

**offset** Int

```
http://{{API}}/api/Accounts
```
```
Account ID
```
```
Parent account ID
```
```
Account Number
```
```
Account segment number
```
```
Account currency
```
```
Accounting period symbol
```
```
Database name (Default in Web.Config)
```
```
Limit numbers of results
```
```
Skip the numbers of results
```
### POST http://YOUR_IP/api/Accounts

#### The function allows you to create an account

**Body Params Request :**


#### AUTHORIZATION OAuth 2.

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.

#### Body raw (json)

**Company Data Fields :**

**Http Correct Answer :**

**201 Created**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Definition Length Required
```
```
Int32 costCorrect Active Cost
Adjustment
```
```
Value(0,1) No
```
```
Int32 budgetControl Account bid
Control
```
```
Value(0,1) No
```
```
Int32 parentId Parent Id None No
```
```
Int32 nextAccountId Next Account Id None No
```
```
Int32 level Level None No
```
```
String accountNumber Number of
Account
```
```
0-50 No
```
```
String accountNumberI
ndex
```
```
Account Number
for the Index (if
you Insert a
Value, you will
add a sub
Account)
```
```
0-50 No
```
```
String segment Account
Segment Number
```
```
0-20 No
```
```
String name1 First row in the 0-50 Yes
```
```
Type Name Definition Length Required
```
```
String companyName Chosen Company
Name for
Accounts
```
```
None No
```
```
String databaseName Chosen
Database Name
for Accounts
```
```
None No
```

#### AUTHORIZATION OAuth 2.

This folder is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.

```
http://{{API}}/api/AccountsForCustomers
```
#### AUTHORIZATION OAuth 2.

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.

#### PARAMS

**id** int

**username** string

**vatNumber** string

```
json
```
##### {

```
"costCorrect" : 0 ,
"budgetControl" : 0 ,
"accountNumberIndex" : "",
"segment" : "514",
"name1" : "Test",
"name2" : "Test",
"currency" : "PLN",
"accountType" : 1 ,
"withSettlement" : 0 ,
"withoutSettlement" : 0 ,
"ballanceControl" : 0 ,
"dictionary" : 0 ,
"dictionaryGroup" : 0 ,
"dictionaryType" : 0 ,
"dictionaryId" : 0 ,
"unactive" : 0
}
```
## AccountsForCustomer

### GET http://YOUR_IP/api/AccountsForCustomers

```
id for customer account
```
```
username for customer account
```
```
vat number for customer account
```

```
http://{{API}}/api/AccountsForCustomers
```
#### AUTHORIZATION OAuth 2.

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.

#### Body raw (json)

```
http://{{API}}/api/AccountsForCustomers
```
#### AUTHORIZATION OAuth 2.

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.

#### Body raw (json)

```
http://{{API}}/api/AccountsForCustomers?username=string
```
### POST http://YOUR_IP/api/AccountsForCustomers

```
json
```
```
[
{
"username": "test4",
"password": "abc123",
"vatNumber": "***********"
}
]
```
### PUT http://YOUR_IP/api/AccountsForCustomers

```
json
```
```
[
{
"username": "test4",
"password": "abc123",
"vatNumber": "***********"
}
]
```
### DELETE http://YOUR_IP/api/AccountsForCustomers


#### AUTHORIZATION OAuth 2.

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.

#### PARAMS

**username** string

#### AUTHORIZATION OAuth 2.

This folder is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.

```
http://{{API}}/api/Additions
```
```
username for customer account
```
## Additionals

### GET http://YOUR_IP/api/Additions

#### The function allows you to download data from the database on an additional topic for employees

**Body Response :**

**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Description
```
```
Int32 id Additions Id
```
```
Int32 employeeId Employee Id for Additions
```
```
Int32 typeId Type Id for Additions
```
```
Int32 type Type for Additions
```
```
String name First row of Name
```
```
Int32 inactive Inactive for Additions
```
```
Int32 contract Contract for Additions
```
```
Int32 sicknessInclude Information or the Occurrence of
the Disease
```

#### AUTHORIZATION OAuth 2.

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.

#### PARAMS

**employeeId** Int

**inactive** Int

**databaseName** String

```
http://{{API}}/api/AdditionsValues
```
```
Employee ID
```
```
0 - active 1- inactive
```
```
Database name (Default in Web.Config)
```
### GET http://YOUR_IP/api/AdditionsValues

#### The function allows you to download data from the database on additional values for employees

**Body Response :**

**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Definition
```
```
Int32 id Id for AdditionsValues
```
```
Int32 additionId AdditionId for AdditionsValues
```
```
(DataTime)String periodFrom Period From
```
```
(DataTime)String periodTo Period To
```
```
(DataTime)String validFrom Valid From
```
```
(DataTime)String validTo Valid To
```
```
Decimal value1 First Row of Value
```
```
Decimal value2 Secound Row of Value
```
```
String name First Row of Name
```
```
String description Description for Addition Value
```

#### AUTHORIZATION OAuth 2.

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.

#### PARAMS

**additionId** Int

**periodStartFrom** String (yyyy-MM-dd)

**periodStartTo** String (yyyy-MM-dd)

**periodEndFrom** String (yyyy-MM-dd)

**periodEndTo** String (yyyy-MM-dd)

**validStartFrom** String (yyyy-MM-dd)

**validStartTo** String (yyyy-MM-dd)

**validEndFrom** String (yyyy-MM-dd)

**validEndTo** String (yyyy-MM-dd)

**databaseName** String

```
http://{{API}}/api/AdditionalRecords
```
```
Addition id
```
```
Initial range of values for the beginning of the period
```
```
Final range of values for the beginning of the period
```
```
Initial range of values for the end of the period
```
```
Final range of values for the end of the period
```
```
Initial range of values at the beginning of validity
```
```
Final range of values at the beginning of validity
```
```
Initial range of values for the end of validity
```
```
Final range of values for the end of validity
```
```
Database name (Default in Web.Config)
```
### POST http://YOUR_IP/api/AdditionalRecords

#### The function allows you to create additional records for employees

**Body Params Request :**


**Company Data Fields :**

**Elements For Body :**

```
Type Name Definition Length Required
```
```
Int32 type Type For
Additional
Records
```
```
101 - Revenue
102 - Cost
```
```
Yes
```
```
String fullNumber FullNumber For
Additional
Records
```
```
0-30 No
```
```
Bool disableContracto
rDataUpdate
```
```
Disable
Contractor Data
Update :
```
- false
- true

```
None No
```
```
String category CategoryFor
Additional
Records
```
```
0-20 No
```
```
String register Register For
Additional
Records
```
```
0-20 Yes
```
```
String foreignNumber Foregin Number
For Additional
Records
```
```
0-30 No
```
```
Type Name Definition Length Required
```
```
String companyName Chosen Company
Name for
Additional
Records
```
```
None No
```
```
String databaseName Chosen
Database Name
for Additional
Records
```
```
None No
```
```
Type Name Description Length Required
```
```
String category1 First row of
Category
```
```
0-20 No
```
```
String description Description 0-50 No
```
```
Decimal amount Amount None No
```
```
String category2 Secound row of
Category
```
```
0-20 No
```
```
String accountWn AccountWn 0-50 No
```

#### AUTHORIZATION OAuth 2.

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.

#### Body raw (json)

**Name using for Customer :**

**All Using Filds For Customer :**

**Http Correct Answer :**

**201 Created**

**Http Possible Answer :**

**401 Unauthorized**

```
String accountMa AccoutnMa 0-50 No
```
```
Name Description Required
```
```
contractor Contractor For Additional
Records
```
```
Yes
```
```
payer Payer For Additional Records No
```
```
advanceCustomer AdvanceCustomer For Additional
Records
```
```
No
```
```
Type Name Description Length Required
```
```
Int32 id Customer Id None No
```
```
String code Customer Code 0-20 Yes
```
```
String name1 First row of
Name Customer
```
```
0-50 Yes
```
```
String name2 Secound row of
Name Customer
```
```
0-50 No
```
```
String name3 Third row of
Name Customer
```
```
0-50 No
```
```
String vatNumber Vat Number 0-20 No
```
```
String country Country 0-40 No
```
```
String city City Name 0-40 No
```
```
String street Street Name 0-40 No
```
```
String additionalAdress Additional Adress 0-44 No
```
```
String postCode Post Code 0-10 No
```
```
String houseNumber House Number 0-10 No
```

```
json
```
```
{
"type": 101 ,
"contractor": {
"Code": "MARSZALIK",
"Name1": "-",
"Type": 1
},
"payer": {
"Code": "ADRIAN_ELTES",
"Name1": "-",
"Type": 3
},
"advanceCustomer": {
"Code": "PRACOWNIK1",
"Name1": "-",
"Type": 3
},
"disableContractorDataUpdate": true,
"category": "KATEGORIA 1_2 REVENU",
"register": "PRZYCHODY",
"checkIfExists": false,
"foreignNumber": "Numer obcy ELTE-S",
"currency": "EUR",
"recordDate": "2022-09-01",
"issueDate": "2022-09-01",
"operationDate": "2022-09-01",
"generatingPayment": 1 ,
"paymentMethod": "przelew",
"total": 2122 ,
"elements": [
{
"category1": "KATEGORIA 1_1 REVENU",
"category2": "KATEGORIA 2 REVENUES",
"description": "opis1",
"amount": 225 ,
"accountWn": "020-3",
"accountMa": "132-1-2"
},
{
"category1": "KATEGORIA 1 REVENUES",
"category2": "KATEGORIA 2 REVENUES",
"description": "opis2",
"amount": 100.22,
"accountWn": "100",
"accountMa": "241-1-1"
}
]
}
```
## Analyticals


#### AUTHORIZATION OAuth 2.0

This folder is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

```
http://{{API}}/api/AnalyticalDescriptions
```
### GET http://YOUR_IP/api/AnalyticalDescriptions

#### The function of using data with analytical values from the database

**Body Response :**

**Company Data Fields :**

**Elements for Body :**

**Options For Elements :**

```
Type Name Definition
```
```
Int32 documentId Docuemnt Id
```
```
List elements Elements
```
```
Object companyData Company Data obejct for
Analytical Descriptions
```
```
Type Name Definition Length Required
```
```
String companyName Chosen Company
Name for
Analytical
Descriptions
```
```
None No
```
```
String databaseName Chosen
Database Name
for Analytical
Descriptions
```
```
None No
```
```
Type Name Definition
```
```
Int32 lp Lp
```
```
String positionValue Position Value
```
```
Decimal percentage Percentage
```
```
Decimal value Value
```
```
Bool setPercentage True or False
```
```
List options Options
```

#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### PARAMS

**documentId** Int32

**offset** Int32

**limit** Int32

**databaseName** String

```
http://{{API}}/api/AnalyticalDescriptionDictionaries
```
**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Definition
```
```
String dimensionName Dimension Name
```
```
String dimensionValue Dimension Value
```
```
Int32 optionIndex Option Index
```
```
Int32 columnIndex Column Index
```
```
Unique vat document identifier in the Comarch ERP Optima
```
```
Skip the numbers of results
```
```
Limit the numbers of results (defualt 100)
```
```
Database name (Default in Web.Config)
```
### GET http://YOUR_IP/api/AnalyticalDescriptionDictionaries

#### The function allows you to download Analytical Description Dictionaries

**Body Response :**

```
Name Description
```
```
Pozycja Object For Pozycje
```

#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### PARAMS

**documentId** Int32

**companyName** String

```
http://{{API}}/api/AnalyticalDescriptions
```
**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

```
Kategoria_II Object For Kategoria_II
```
```
Kategoria_III Object For Kategoria_III
```
```
Required parameter - VAT Document ID
```
```
Required parameter - Company Name
```
### POST http://YOUR_IP/api/AnalyticalDescriptions

#### The function is used to create additional analytical values

**Body Params Request :**

**Company Data Fields :**

```
Type Name Description Length Required
```
```
Int32 documentId VAT document ID None Yes
```
```
List of Dimension
objects
```
```
elements List of
dimensions
```
```
None Yes
```
```
Object companyData Company Data
obejct for
Analytical
Descriptions
```
```
None No
```

#### AUTHORIZATION OAuth 2.0

**Dimension for Body :**

**Option For Dimension :**

**Http Correct Answer :**

**201 Created**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Definition Length Required
```
```
String companyName Chosen Company
Name for
Analytical
Descriptions
```
```
None No
```
```
String databaseName Chosen Data
Base Name for
Analytical
Descriptions
```
```
None No
```
```
Type Name Description Length Required
```
```
Int32 lp Lp of VAT
Document
element
```
```
None No
```
```
String positionValue Value of position
column
```
```
1-255 Yes
```
```
Decimal percentage Position
percentage
```
```
None No
```
```
Decimal value Position amount None No
```
```
Boolean setPercentage Specifies
whether to use
the percentage
or value fields
(default = false)
```
```
None No
```
```
List of Dimension
option objects
```
```
options Values of defined
dimensions
```
```
None Yes
```
```
Type Name Description Length Required
```
```
String dimensionName Name of
dimension
```
```
1-20 Yes
```
```
String dimensionValue Value of
dimension
```
```
1-255 Yes
```

#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### Body raw (json)

```
json
```
```
{
"documentId": 8 ,
"elements": [
{
"percentage": 50 ,
"setPercentage": true,
"value": 0 ,
"positionValue": "Pozycja/1/Netto, 23%/[200.00]",
"options": [
{
"dimensionName": "KATEGORIA_II",
"dimensionValue": "6/KATEGORIA_II/70004SPRZ.ELEKTRONIK"
},
{
"dimensionName": "KATEOGIRA_3",
"dimensionValue": "7/KATEOGIRA_3/KATEGORIA 1 REVENUES/KATEGORIA 1_1 REVE
}
]
},
{
"percentage": 0 ,
"value": 100 ,
"positionValue": "Pozycja/1/Netto, 23%/[200.00]",
"options": [
{
"dimensionName": "KATEOGIRA_3",
"dimensionValue": "7/KATEOGIRA_3/70004SPRZ.ELEKTRONIK"
},
{
"dimensionName": "KATEGORIA_II",
"dimensionValue": "6/KATEGORIA_II/KATEGORIA 1 REVENUES/KATEGORIA 1_1 REV
}
]
},
{
"percentage": 100 ,
"setPercentage": true,
"value": 0 ,
"positionValue": "Pozycja/2/Netto, 23%/[500.00]",
"options": [
{
"dimensionName": "KATEOGIRA_3",
"dimensionValue": "7/KATEOGIRA_3/70004SPRZ.ELEKTRONIK"
},
{
"dimensionName": "KATEGORIA_II",
"dimensionValue": "6/KATEGORIA_II/KATEGORIA 1 REVENUES/KATEGORIA 1_1 REV
}
]
}
```

#### AUTHORIZATION OAuth 2.0

This folder is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

```
http://{{API}}/api/AttributeDefinitions
```
#### AUTHORIZATION Bearer Token

**Token** <token>

##### ]

##### }

## Attributes

### GET http://YOUR_IP/api/AttributeDefinitions

#### This function allows you to download Attributes Data from the database

**Body Response :**

**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Definition
```
```
Int32 id Attribute ID
```
```
string code Attribute code
```
```
string name Attribute name
```
```
Int32 type Type of attribute
1 - product
2- customer
3 -asset
4 - document
5 - JPK
Int32 format Format of attribute
1 - text
2 - number
3 - predefined list
4 - date
5 - binary data
```
```
List values List of attributes value
```

#### PARAMS

**id** 4

**type** int

#### Body urlencoded

**id** Int32

```
http://{{API}}/api/AttributeDefinitions
```
```
Attribute ID
```
```
Type of attribute [1 - product, 2- customer, 3 -asset, 4 - document, 5 - JPK]
```
### POST http://YOUR_IP/api/AttributeDefinitions

#### This function allows you to download Attributes Data from the database

**Body Response :**

**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Definition
```
```
Int32 code Attribute Code
```
```
string name Attribute name
```
```
Int32 type Type of attribute
1 - product
2- customer
3 -asset
4 - document
5 - JPK
Int32 format Format of attribute
1 - text
2 - number
3 - predefined list
4 - date
5 - binary data
```
```
List values List of attributes value
```

#### AUTHORIZATION Bearer Token

**Token** <token>

#### Body raw (json)

#### AUTHORIZATION OAuth 2.0

This folder is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

```
http://{{API}}/api/AllowIssuedReservedProducts
```
#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### Body raw (json)

```
json
```
```
{
"code" : "Test",
"name" : "Test",
"format" : 4 ,
"type" : 1 ,
"values" : [
{"value" : "jeden"} ,
{"value" : "dwa"}
]
}
```
## AllowIssuedReservedProducts

### POST http://YOUR_IP/api/AllowIssuedReservedProducts

The method allows you to change the Do not allow goods to be issued when there are reservations parameter

```
Type Name Definition
```
```
int32 value Value of parameter
```
```
json
```

#### AUTHORIZATION OAuth 2.0

This folder is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

```
http://{{API}}/api/BankStatements
```
##### {

```
"value" : 1
}
```
## Banks

### GET http://YOUR_IP/api/BankStatements

#### This function allows you to download data from the bank statement database

**Body Response :**

**Http Correct Answer :**

**200 Ok**

```
Type Name Definition
```
```
Int32 id Bank Id
```
```
String fullNumber Full Number
```
```
String foreignNumber Foregin Number
```
```
Int32 documentTypeId Document Type Id
```
```
String documentTypeSignature Document Type Signature
```
```
String documentTypeName Document Type Name
```
```
(DataTime)String issueDate IssueDate
```
```
(DataTime)String closeDate CloseDate
```
```
Int32 closed Closed
```
```
Decimal balance Balance
```
```
Decimal revenue Revenue
```
```
Decimal expenditure Expenditure
```
```
Decimal systemBalance System Balance
```
```
Decimal systemRevenue System Revenue
```

#### AUTHORIZATION Bearer Token

**Token** <token>

#### PARAMS

**id** Int32

**fullNumber** String

**foreignNumber** String

**issueDateStart** String (yyyy-MM-dd)

**issueDateEnd** String (yyyy-MM-dd)

**closeDateStart** String (yyyy-MM-dd)

**closeDateEnd** String (yyyy-MM-dd)

**full_number** String, String...

**foreign_number** String, String...

**booked** Int32

**optima_id** Int32,Int32...

**databaseName** String

#### Body urlencoded

**id** 15

**Http Possible Answer :**

**401 Unauthorized**

```
Record id
```
```
Full bank report number
```
```
Foreign report number
```
```
Start of date range of opening the bank report
```
```
End of date range of opening the bank report
```
```
Start of date range of bank report closing
```
```
End of date range of bank report closing
```
```
List of values separated by comma
```
```
List of values separated by comma
```
```
Posted raport has an value, unposted has a null value
```
```
Optima id
```
```
Database name (Default in Web.Config)
```

```
http://{{API}}/api/BankAccountNumbers
```
#### AUTHORIZATION Bearer Token

### GET http://YOUR_IP/api/BankAccountNumbers

#### The function enables downloading data from the database on Bank Account Numbers

**Body Response :**

**Company Data Fields :**

**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Definition
```
```
Int32 id Bank Account Number Id
```
```
Int32 customerId Customer Id
```
```
String accountNumber Account Number
```
```
String accountNumberWithoutSeparato
rs
```
```
Account Number Without
Separators
```
```
Int32 iban Iban
```
```
String description Description
```
```
Int32 defaultAccountNumber Default Account Number
```
```
(DateTime)String created Created Date
```
```
(DateTime)String updated Update Date
```
```
Object companyData Company Data obejct for Bank
Account Numbers
```
```
Type Name Definition Length Required
```
```
String companyName Chosen Company
Name for Bank
Account
Numbers
```
```
None No
```
```
String databaseName Chosen
Database Name
for Bank Account
Numbers
```
```
None No
```

#### AUTHORIZATION Bearer Token

**Token** <token>

#### PARAMS

**id** Int32

**cutomerId** Int32

**offset** Int32

**limit** Int32

**c_dateFrom** String(yyyy-dd-mm)

**c_dateTo** String(yyyy-dd-mm)

**u_dateFrom** String(yyyy-dd-mm)

**u_dateTo** String(yyyy-dd-mm)

**databaseName** String

```
http://{{API}}/api/BankAccountNumbers
```
```
Bank account number id
```
```
Bank account customer id
```
```
Skip the numbers of results
```
```
Limit the numbers of results (defualt 100)
```
```
Bank account number c_from
```
```
Bank account number c_to
```
```
Bank account number u_from
```
```
Bank account number u_to
```
```
Database name (Default in Web.Config)
```
### POST http://YOUR_IP/api/BankAccountNumbers

#### The function allows you to create Bank Account Numbers

**Body Params Request :**


#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

**Company Data Fields :**

**Http Correct Answer :**

**201 Created**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Definition Length Required
```
```
Int32 id Bank Account
Numbers Id
```
```
None No
```
```
Int32 customerId Customer Id None Yes
```
```
String accountNumber Account Number None Yes
```
```
String accountNumber
WithoutSeparator
s
```
```
Account Number
Without
Separators
```
```
None No
```
```
Int32 iban 0 or 1 None No
```
```
String description Description None No
```
```
Int32 defaultAccountN
umber
```
```
Default Account
Number :
0 - No
1 - Yes
```
```
None No
```
```
(DateTime)String created Created Date (yyyy-mm-dd) No
```
```
(DateTime)String updated Update Date (yyyy-mm-dd) No
```
```
Object companyData Company Data
obejct for Bank
```
```
None No
```
```
Type Name Definition Length Required
```
```
String companyName Chosen Company
Name for Bank
Account
Numbers
```
```
None No
```
```
String databaseName Chosen
Database Name
for Bank Account
Numbers
```
```
None No
```
### PUT http://YOUR IP/api/BankAccountNumbers


```
http://{{API}}/api/BankAccountNumbers/94
```
### PUT http://YOUR_IP/api/BankAccountNumbers

#### The function enables modification of Bank Account Numbers

**Body Params Request :**

**Company Data Fields :**

**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Definition Length Required
```
```
Int32 id Bank Account
Numbers Id
```
```
None No
```
```
Int32 customerId Customer Id None Yes
```
```
String accountNumber Account Number None Yes
```
```
String accountNumber
WithoutSeparator
s
```
```
Account Number
Without
Separators
```
```
None No
```
```
Int32 iban 0 or 1 None No
```
```
String description Description None No
```
```
Int32 defaultAccountN
umber
```
```
Default Account
Number :
0 - No
1 - Yes
```
```
None No
```
```
(DateTime)String created Created Date (yyyy-mm-dd) No
```
```
(DateTime)String updated Update Date (yyyy-mm-dd) No
```
```
Object companyData Company Data
obejct for Bank
```
```
None No
```
```
Type Name Definition Length Required
```
```
String companyName Chosen Company
Name for Bank
Account
Numbers
```
```
None No
```
```
String databaseName Chosen
Database Name
for Bank Account
Numbers
```
```
None No
```

#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### Body raw (json)

#### AUTHORIZATION OAuth 2.0

This folder is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

```
http://{{API}}/api/Categories
```
```
json
```
```
{
"customerId" : 4 ,
"accountNumber": "94",
"description" : "Test opisu"
}
```
## Categories

### GET http://YOUR_IP/api/Categories

#### The function allows you to download data about categories

**Body Response :**

```
Type Name Definition
```
```
Int32 id Categories Id
```
```
Int32 type Type of Categories
```
```
Int32 level Level
```
```
Int32 parentId Parent Id
```
```
String code Code
```
```
String detailCode Detail Code
```
```
String description Description
```
```
Int32 KPIRColumn KPIR Column
```
```
Decimal vatRate Vat Rate
```
```
Int32 flag Flag
```

#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### PARAMS

**id** Int32

**code** String

**databaseName** String

#### AUTHORIZATION OAuth 2.0

This folder is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

```
http://{{API}}/api/AddCivilLawContracts
```
**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

```
ID of category in Comarch ERP Optima
```
```
Code of category in Comarch ERP Optima
```
```
Database name (Default in Web.Config)
```
## CivilLaws

### POST http://YOUR_IP/api/AddCivilLawContracts

#### This function allows you to create civil law documents

**Body Params Request :**


#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### Body raw (json)

**Company Data Fields :**

**Http Correct Answer :**

**201 Created**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Definition Length Required
```
```
String employeeAbbrevi
ation
```
```
employee
abbreviation in
Comarch ERP
Optima
```
```
0-20 Yes
```
```
(DataTime)String singingDate date of signing
the contract
```
```
(yyyy-mm-dd) Yes
```
```
(DataTime)String conclusionDate date of
conclusion the
contract
```
```
(yyyy-mm-dd) Yes
```
```
(DataTime)String dissolutionDate date of
dissolution the
contract
```
```
(yyyy-mm-dd) No
```
```
Decimal nettoValue net value of the
contract
```
```
0-50 Yes,
VALUE GRATER
THEN 0
String contractTitle contract title 0-127 No
```
```
Object companyData Company Data
obejct for Civil
Law Contracts
```
```
None No
```
```
Type Name Definition Length Required
```
```
String companyName Chosen Company
Name for Civil
Law Contracts
```
```
None No
```
```
String databaseName Chosen
Database Name
for Civil Law
Contracts
```
```
None No
```

```
http://{{API}}/api/AddCivilLawContracts
```
```
json
```
```
{
"employeeAbbreviation" : "003/E",
"singingDate" : "2023-05-08",
"conclusionDate" : "2023-05-12",
"dissolutionDate" : "2023-05-14",
"nettoValue" : "200",
"contractTitle" : "test"
}
```
### POST http://YOUR_IP/api/AddValueToCivilLaw

#### This function allows you to create value for civil law.

**Body Params Request :**

**Company Data Fields :**

**Http Correct Answer :**

```
Type Name Definition Length Required
```
```
String EmployeeCode employee code in
Comarch ERP
Optima
```
```
0-20 Yes
```
```
string CivilLawCode code of civil law None Yes
```
```
string Month month None Yes
```
```
int Year year None
```
```
double Value Value of the None No
```
```
Object companyData Company Data
obejct for Civil
Law Contracts
```
```
None No
```
```
Type Name Definition Length Required
```
```
String companyName Chosen Company
Name for Civil
Law Contracts
```
```
None No
```
```
String databaseName Chosen
Database Name
for Civil Law
Contracts
```
```
None No
```

#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### Body raw (json)

#### AUTHORIZATION OAuth 2.0

This folder is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

```
http://{{API}}/api/Companies
```
#### AUTHORIZATION OAuth 2.0

**201 Created**

**Http Possible Answer :**

**401 Unauthorized**

```
json
```
```
{
"employeeAbbreviation" : "003/E",
"singingDate" : "2023-05-08",
"conclusionDate" : "2023-05-12",
"dissolutionDate" : "2023-05-14",
"nettoValue" : "200",
"contractTitle" : "test"
}
```
## Companies

### GET http://YOUR_IP/api/Companies

#### The function allows you to download data your companies from configuration.

```
Type Name Definition
```
```
string fullName Fullname your database
company (with prefix)
```
```
string name Name of your company
```
```
string vatNumber Nip of your company from
Baz_nip
```

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### PARAMS

**databaseName** String

#### AUTHORIZATION OAuth 2.0

This folder is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

```
http://{{API}}/api/Contracts
```
```
Chosen Database Name for Civil Law Contracts
```
## Contracts

### GET http://YOUR_IP/api/Contracts

#### The function enables downloading the history of contracts from the database

**Body Response :**

**Http Correct Answer :**

```
Type Name Definition
```
```
Int32 id Contracts Id
```
```
String number Number
```
```
Int32 employeeId Employee Id
```
```
Int32 documentTypeId Document Type Id
```
```
String documentTypeSignature Document Type Signature
```
```
String documentTypeName Document Type Name
```
```
Int32 paycheckTypeId Paycheck Type Id
```
```
String paycheckTypeName Paychack Type Name
```
```
(DataTime)String createDate Create Date
```
```
(DataTime)String dateFrom Date From
```
```
(DataTime)String dateTo Date To
```
```
Decimal value Value
```
```
Decimal paidValue Paid Value
```
```
String currency Currency
```

#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### PARAMS

**id** Int32

**employeeId** Int32

**documentTypeId** Int32

**paycheckTypeId** Int32

**createDateStart** String (yyyy-MM-dd)

**createDateEnd** String (yyyy-MM-dd)

**dateFromStart** String (yyyy-MM-dd)

**dateFromEnd** String (yyyy-MM-dd)

**dateToStart** String (yyyy-MM-dd)

**dateToEnd** String (yyyy-MM-dd)

**employee_id** Int32, Int32...

**databaseName** String

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

```
Unique Contracts id in the Comarch ERP Optima
```
```
Unique Employee id in the Comarch ERP Optima
```
```
Unique Document type id in the Comarch ERP Optima
```
```
Unique Paycheck type id in the Comarch ERP Optima
```
```
Start date range of create contract
```
```
End date range of create contract
```
```
Start date range of start contract
```
```
End date range of start contract
```
```
Start date range of end contract
```
```
End date range of end contract
```
```
List of employee id separated by comma
```
```
Database name (Default in Web.Config)
```
### GET htt //YOUR IP/ i/C t t I


```
http://{{API}}/api/ContractsInsurances
```
### GET http://YOUR_IP/api/ContractsInsurances

#### The function enables downloading from the insurance database

**Body Response :**

**Company Data Fields :**

**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Definition
```
```
Int32 id ContractsInsurances Id
```
```
String contractNumber Contract Number
```
```
Int32 employeeId Employee Id
```
```
String insuranceTitleCode Insurance Title Code
```
```
String insuranceTitleAdditionalCode Insurance Title Additional Code
```
```
Int32 compulsoryPensionInsurance Compulsory Pension Insurance
```
```
Int32 compulsoryDisabilityInsurance Compulsory Pension Insurance
```
```
Int32 compulsorySicknessInsurance Compulsory Sick ness Insurance
```
```
Int32 compulsoryAccidentInsurance Compulsory Accident Insurance
```
```
(DataTime)String dateOfSocialInsurance Date of Social Insurance
```
```
(DataTime)String dateOfHealthInsurance Date of Health Insurance
```
```
Int32 voluntaryPensionInsurance Voluntary Pension Insurance
```
```
(DataTime)String dateOfPensionInsurance Date of Pension Insurance
```
```
Int32 voluntaryDisabilityInsurance Voluntary Disability Insurance
```
```
Type Name Definition Length Required
```
```
String companyName Chosen Company
Name for
Contracts
Insurances
```
```
None No
```
```
String databaseName Chosen
Database Name
for Contracts
Insurances
```
```
None No
```

#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### PARAMS

**id** Int32

**contractNumber** String

**employeeId** Int32

**databaseName** String

```
http://{{API}}/api/ContractsInsurances
```
```
Unique contracts id in the Comarch ERP Optima
```
```
Unique contracts number in the Comarch ERP Optima
```
```
Unique Employee id in the Comarch ERP Optima
```
```
Database name (Default in Web.Config)
```
### PUT http://YOUR_IP/api/ContractsInsurances

#### The function enables modifiaction from the insurance database

**Body Params Reqest :**

```
Type Name Definition Length Required
```
```
Int32 id ContractsInsuran
ces Id
```
```
None No
```
```
String contractNumber Contract Number None No
```
```
Int32 employeeId Employee Id None No
```
```
String insuranceTitleCo
de
```
```
Insurance Title
Code
```
```
None No
```
```
String insuranceTitleAd
ditionalCode
```
```
Insurance Title
Additional Code
```
```
None No
```
```
Int32 compulsoryPensi
onInsurance
```
```
Compulsore
Pension
Insurance
```
```
None No
```
```
Int32 compulsoryDisab
ilityInsurance
```
```
Compulsore
Disabilitu
Insurance
```
```
None No
```
```
Int32 compulsorySickn
essInsurance
```
```
Compulsore
Sickness
I
```
```
None No
```

#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### Body raw (json)

#### AUTHORIZATION OAuth 2.0

This folder is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

```
http://{{API}}/api/CostCenters
```
**Company Data Fields :**

**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

```
Insurance
```
```
Int32 compulsoryAccid Compulsore None No
```
```
Type Name Definition Length Required
```
```
String companyName Chosen Company
Name for
Contracts
Insurances
```
```
None No
```
```
String databaseName Chosen Data
Base Name for
Contracts
Insurances
```
```
None No
```
```
json
```
```
{
"id": 1 ,
"nextPayoutAmount":10.20
}
```
## CostCenters

### GET http://YOUR_IP/api/CostCenters


#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### PARAMS

**id** Int32

**databaseName** String

#### AUTHORIZATION OAuth 2.0

This folder is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

```
http://{{API}}/api/Customers
```
#### The function enables to Download od date for CostCenter

**Body Response :**

**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Definition
```
```
Int32 id Cost Center Id
```
```
String code Cone
```
```
String name Name
```
```
Int32 level Level
```
```
Int32 parentId Parent Id
```
```
Int32 inactive Inactive
```
```
Unique Cost Center id in the Comarch ERP Optima
```
```
Database name (Default in Web.Config)
```
## CustomerGroups

### GET http://YOUR_IP/api/CustomerGroups

#### The Function enalble to download the customer groups from ERP Optima


#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### PARAMS

**databaseName** String

#### AUTHORIZATION OAuth 2.0

This folder is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

```
http://{{API}}/api/Customers
```
**Body Response :**

**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

```
type name description
```
```
String name group's name
```
```
String description description
```
```
Database name (Default in Web.Config)
```
## Customers

### GET http://YOUR_IP/api/Customers

#### The Function enalble to download the hisotry of the date base for Customers

```
Get list of customers
Get single customer by ID (ID is unique)
Get customer/list of customers by vatNumber number (multiple companies with the same number possible)
Get single customer by code (code in Comarch ERP Optima is unique)
```
You can combine parameters

**Body Response :**


#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### PARAMS

**id** Int32

**Company Data Fields :**

**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Definition
```
```
Int32 id Customer Id
```
```
String code Code
```
```
String name1 First row of Name
```
```
String name2 Second row of Name
```
```
String name3 Third row of Name
```
```
String vatNumber Vat Number
```
```
String country Country
```
```
String city City Name
```
```
String street Street Name
```
```
String additionalAdress Additional Adress
```
```
String postCode Post Code
```
```
String houseNumber House Number
```
```
String flatNumber Flat Number
```
```
String phone1 First Phone Number
```
```
Type Name Definition Length Required
```
```
String companyName Chosen Company
Name for
Customers
```
```
None No
```
```
String databaseName Chosen
Database Name
for Customers
```
```
None No
```

**id** Int32

**code** String

**vatNumber** String

**isoVatNumber** String

**offset** Int32

**limit** Int32

**c_dateFrom** String (yyyy-mm-dd)

**c_dateTo** String (yyyy-mm-dd)

**u_dateFrom** String (yyyy-mm-dd)

**u_dateTo** String (yyyy-mm-dd)

**optima_companycode** String, String...

**email** String

**databaseName** String

**optima_id** Int32,Int32...

```
http://{{API}}/api/Customers
```
```
Unique customer identifier in the Comarch ERP Optima
```
```
Unique customer code in the Comarch ERP Optima
```
```
Number used to identify taxpayers
```
```
Unique code used to identify taxpayers with country prefix
```
```
Skip the numbers of results
```
```
Limit the numbers of results
```
```
Start range of created date
```
```
End range of created date
```
```
Start range of updated date
```
```
End range of updated date
```
```
List of value separated by comma
```
```
Customer Email
```
```
Database name (Default in Web.Config)
```
```
List Document Id
```
### POST http://YOUR_IP/api/Customers

#### The Function enable to Create Customers in Erp Optima

**Body Params Request :**


#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### PARAMS

**code** 000001/PRESTA

**Body Params Request :**

**Company Data Fields :**

**Http Correct Answer :**

**201 Created**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Definition Length Required
```
```
Int32 id Unique customer
ID in Optima
```
```
None No
```
```
String code Unique customer
Code in Optima
```
```
1 - 20 Yes
```
```
String name1 First line of name
in Optima
```
```
1 - 50 Yes
```
```
String name2 Second line of
name in Optima
```
```
0 - 50 No
```
```
String name3 Third line of
name in Optima
```
```
0 - 50 No
```
```
String peselNumber Pesel number 11 No
```
```
String vatNumber Taxpayer
identification
number
```
```
0 - 20 No
```
```
String country Country 1 - 40 Yes
```
```
String voivodeship Name of
voivodeship
```
```
0 - 40 No
```
```
St i it Cit N 1 40 Y
```
```
Type Name Definition Length Required
```
```
String companyName Chosen Company
Name for
Customers
```
```
None No
```
```
String databaseName Chosen
Database Name
for Customers
```
```
None No
```

**code** 000001/PRESTA

#### Body raw (json)

```
http://{{API}}/api/Customers
```
```
json
```
```
{
"code": "000001/PRESTA",
"name1": "New customer name1 - Wojtek2",
"name2": "Wojtek3",
"name3": "Wojtek3",
"nip": "",
"country": "Polska",
"city": "Kraków",
"street": "ul. Czeczów \"Rudego\"",
"additionalAdress" : "Dodatkowe pole adresu",
"postCode": "35-256",
"houseNumber": "30",
"flatNumber": "",
"phone1": "*********",
"phone2": "",
"inactive": 0 ,
"defaultPrice": 0 ,
"regon": "*********",
"email": "tak",
"paymentMethod": "gotówka",
"dateOfPayment": 0 ,
"maxPaymentDelay": 0 ,
"created": "0001-01-01T00:00:00",
"description": "",
"countryCode": "PL"
}
```
### PUT http://YOUR_IP/api/Customers

#### The Function enable to modification Customer in Erp Optima


#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### PARAMS

**Company Data Fields :**

**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Definition Length Required
```
```
Int32 id Unique customer
ID in Optima
```
```
None No
```
```
String code Unique customer
Code in Optima
```
```
1 - 20 Yes
```
```
String name1 First line of name
in Optima
```
```
1 - 50 Yes
```
```
String name2 Second line of
name in Optima
```
```
0 - 50 No
```
```
String name3 Third line of
name in Optima
```
```
0 - 50 No
```
```
String peselNumber Pesel number 11 No
```
```
String vatNumber Taxpayer
identification
number
```
```
0 - 20 No
```
```
String country Country 1 - 40 Yes
```
```
String voivodeship Name of
voivodeship
```
```
0 - 40 No
```
```
St i it Cit N 1 40 Y
```
```
Type Name Definition Length Required
```
```
String companyName Chosen Company
Name for
Customers
```
```
None No
```
```
String databaseName Chosen
Database Name
for Customers
```
```
None No
```

#### Body raw (json)

#### AUTHORIZATION OAuth 2.0

This folder is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

```
http://{{API}}/api/RecurringInvoices
```
```
json
```
```
{
"code": "000001/PRESTA",
"name1": "New customer name1",
"name2": "Customer name2",
"name3": " ",
"nip": "",
"country": "Polska",
"city": "Kraków",
"street": "ul. Street",
"additionalAdress" : "additional adress",
"postCode": "35-256",
"houseNumber": "30",
"flatNumber": "",
"phone1": "*********",
"phone2": "",
"inactive": 0 ,
"defaultPrice": 0 ,
"regon": "*********",
"email": "tak",
"paymentMethod": "gotówka",
"dateOfPayment": 0 ,
"maxPaymentDelay": 0 ,
"created": "0001-01-01T00:00:00",
"description": "",
"countryCode": "PL"
}
```
## CRM

### POST http://{{API}}/api/RecurringInvoices

### The Function enable to Create a new recurring invoice in the Comarch ERP Optima

### system.

**Body Params Request :**


**Customers Data Fields :**

**Products Data Fields :**

**Company Data Fields :**

**Http Correct Answer :**

**201 Created**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Definition Length Required
```
```
String code Recurring invoice
template code
```
```
1-40 Yes
```
```
String name Recurring invoice
template name
```
```
1 - 50 Yes
```
```
Int32 cyclePeriod Cycle period:
weekly/monthly
0 - weekly
1- monthly
```
```
Value [ 0 / 1] Yes
```
```
Int32 dayOfTheWeek The day of the
week on which
recurring invoices
should be
created
```
```
Value [ 1-7] Yes
```
```
Int32 dayOfTheMonth The day of the
month on which
invoices should
be generated
```
```
Value [ 1 - 31] No
```
```
Int32 repeat Specification of
cycle recurrence
(every 1 2 3
```
```
Value [ 0-255] No
```
```
Type Name Definition Length Required
```
```
String code Unique product
code
```
```
1-50 Yes
```
```
Type Name Definition Length Required
```
```
String companyName Chosen Company
Name for
Customers
```
```
None No
```
```
String databaseName Chosen
Database Name
for Customers
```
```
None No
```

#### AUTHORIZATION Bearer Token

**Token** <token>

#### PARAMS

**code** Recurring invoice template code

**name**

#### Body raw (json)

```
Recurring invoice template code
```
```
json
```
```
{
"code": "INV-2025-003",
"name": "Recurring Invoice Example",
"cyclePeriod": 1 ,
"dayOfTheWeek": 1 ,
"dayOfTheMonth": 15 ,
"repeat": 12 ,
"documentType": 320 ,
"bufor": 1 ,
"magCode": "MAGAZYN_FOCUS",
"nb": 1 ,
"reminder": 1 ,
"comments": "This is a test recurring invoice.",
"currency": "PLN",
"vatCalculationMethod": 1 ,
"documentDateType": 1 ,
"documentDate": "2025-03-01T00:00:00Z",
"documentDateDayOfMonth": 15 ,
"saleDateType": 1 ,
"saleDate": "2025-03-01T00:00:00Z",
"saleDateDayOfMonth": 15 ,
"paymentType": 1 ,
"paymentMethod": "CZEK",
"fixedPaymentFormId": 123 ,
"paymentTermType": 1 ,
"paymentTermDay": 14 ,
"categoryType": 2 ,
"category": "ODSETKI LOKAT",
"categoryDescription": "Subscription service",
"reminderTime": "2025-03-05T10:00:00Z",
"nextGenerationDate": "2025-04-01T00:00:00Z",
"invoiceDescription": "Monthly subscription invoice for software service.",
"customers": [
{
"code": "ADM"
},
{
```

#### AUTHORIZATION OAuth 2.0

This folder is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

```
http://{{API}}/api/Deliveries
```
```
"code": "AL_KOMP"
}
],
"products": [
{
"code": "MISKA+DLA+PSA"
},
{
"code": "123_TEST"
}
],
"attributes": [{
"code" : "Booking_ELTES",
"Value" : "test_atr"
}]
```
```
}
```
## Deliveries

### GET http://YOUR_IP/api/Deliveries

#### The Function enable to download hisotry of the date base for Deliveries

**Body Response :**


#### AUTHORIZATION Bearer Token

**Token** <token>

#### PARAMS

**transactionId** Int32

**warehouseId** Int32

**deliveryId** Int32

**itemId** Int32

**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Definition
```
```
Int32 transactionId Transaction Id
```
```
Int32 elementId Element Id
```
```
Int32 deliveryId Delivery Id
```
```
Int32 deliveryIndicated Delivery Indicate
```
```
Int32 itemId Item Id
```
```
Int32 warehouseId Warehouse Id
```
```
Decimal quantity Quantity
```
```
Decimal totalValue Total Value
```
```
Decimal price Price
```
```
(DateTime)String operationDate Operation Date
```
```
Int32 documentId Document Id
```
```
String documentFullnumber Document Fullnumber
```
```
String feature1Code Feature First Code
```
```
String feature1Value Feature First Value
```
```
Transaction ID
```
```
Warehouse ID from Comarch ERP Optima
```
```
Delivery ID
```
```
Item ID
```

**documentId** Int32

**elementId** Int32

**deliveryIndication** Int32

**dateFrom** String (yyyy-mm-dd)

**dateTo** String (yyyy-mm-dd)

**offset** Int32

**limit** Int32

**databaseName** String

**type** int32

```
http://{{API}}/api/Deliveries
```
```
Item ID
```
```
Document ID
```
```
Element ID
```
```
Is delivery selection manual? 0 - No deliveries 1 - Selection of deliveries
```
```
Operation date from
```
```
Operation date to
```
```
Offset
```
```
Limit
```
```
Database name (Default in Web.Config)
```
```
Type : 1 - delivery 2 - expenditure 3 - reservation 4 - order
```
### PUT http://YOUR_IP/api/Deliveries

#### The Function enable to update element delivery in exist Document

* _Body Request :__*_

**Elements:**

```
Type Name Definition Required
```
```
int documentId Document ID Yes
```
```
List elements document element No
```
```
Type Name Definition Required
```
```
int32 elementId Element ID No
```
```
List deliveryLots Delivery Loots No
```

#### AUTHORIZATION Bearer Token

**Token** <token>

#### Body raw (json)

#### AUTHORIZATION OAuth 2.0

This folder is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

**DeliveryLots**

**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Definition Required
```
```
int32 deliveryId Delivery ID No
```
```
int32 quantity Quantity No
```
```
List Features Features No
```
```
Type Name Definition Required
```
```
string FeatureCode Code No
```
```
string FeatureValue Value No
```
```
json
```
```
{
"documentId" : 1111 ,
"elements" : [
{
"elementId" : 1136
}
]
}
```
## Departments

### GET http://YOUR_IP/api/Departments


```
http://{{API}}/api/Departments
```
#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### PARAMS

**id** Int32

**databaseName** String

#### AUTHORIZATION OAuth 2.0

This folder is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

```
http:/{{API}}/api/Documents
```
#### The Function enable to download the history of date base for Departments

**Body Response :**

**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Definition
```
```
Int32 id Departments Id
```
```
String code Code
```
```
String name Name
```
```
Int32 level Level
```
```
Int32 parentId Parent Id
```
```
Int32 inactive Inactive
```
```
Unique Department id in the Comarch ERP Optima
```
```
Database name (Default in Web.Config)
```
## DocumentsLibrary

### GET http://YOUR_IP/api/Documents


```
http:/{{API}}/api/Documents
```
#### The Function enable to download history of the date base for Documents

```
Get list of documents
Get list of customer's documents
Get single document by ID
Get document by foreign number from external system
Get list of documents by Optima type
Get list of documents by status (-1 - cancelled, 0 - confirmed, 1 - to edit)
```
You can combine parameters

**Body Response :**

**Company Data Fields :**

**Customer Object For Payer and Recipient :**

```
Type Name Definition
```
```
Int32 id Document Id
```
```
Int32 type Document Type
```
```
String fullNumber Document Full Number
```
```
String foreignNumber Document ForeignNumber
```
```
Int32 calculatedOn calculatedOn
```
```
String paymentMethod Document Payment Method
```
```
String currency currency
```
```
String description Description
```
```
Int32 status Status
```
```
Int32 sourceWarehouseId Source Ware House ld
```
```
Int32 targetWarehouseId Target Ware House Id
```
```
(DateTime)String documentSaleDate Document Sale Date
```
```
(DateTime)String documentIssueDate Document Issuje Date
```
```
(DateTime)String documentPaymentDate Doument Payment Date
```
```
Type Name Definition Length Required
```
```
String companyName Chosen Company
Name for
Document
```
```
None No
```
```
String databaseName Chosen
Database Name
for Document
```
```
None No
```

[http://{{API}}/api/Customers](http://{{API}}/api/Customers)

**Elements For Body :**

**Attributes For Body and Elements :**

**DeliveryLot For Elements :**

**Object Features for DeliveryLot :**

**Http Correct Answer :**

**200 Ok**

```
Type Name Definition
```
```
Int32 id Element Id
```
```
Int32 itemId Item Id
```
```
String code Code
```
```
String name Name
```
```
String manufacturerCode Manufacture Code
```
```
Decimal unitNetPrice Unit Net Price
```
```
Decimal unitGrossPrice Unit Gross Price
```
```
Decimal totalNetValue Total Net Value
```
```
Decimal totalGrossValue Total Gross Value
```
```
Decimal quantity Quantity
```
```
Decimal vatRate Vat Rate
```
```
String category Category name
```
```
Int32 vatRateFlag Vat Rate Flag Settings :
0 -VAT rate on the goods,
1 t f t
```
```
Type Name Definition
```
```
String code Attribute Code
```
```
String value Attribute Value
```
```
Type Name Definition
```
```
Int32 deliveryId Delivery Id
```
```
Decimal quantity Quantity
```
```
Object features Object Features
```
```
Type Name Definition
```
```
String feature1Code Feature First Code
```
```
String feature1Value Feature First Value
```

#### AUTHORIZATION Bearer Token

**Token** <token>

#### PARAMS

**payerId** Int32

**status** Int32

**foreignNumber** String

**fullNumber** String

**databaseName** String

**offset** Int32

**documentId** 1134

**c_dateFrom** String (yyyy-mm-dd)

**c_dateTo** String (yyyy-mm-dd)

**u_dateTo** String (yyyy-mm-dd)

**correction** 0

**u_dateFrom** String(yyyy-mm-dd)

**type** int32

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

## Attention!!

All data are shown as an example, but their appearance may differ depending on the documents being called.

```
Document payer id
```
```
Status of Comarch ERP Document
```
```
Number from the external system
```
```
Document FullNumber
```
```
Database name (Default in Web.Config)
```
```
Skip the numbers of results
```
```
Limit the numbers of results
```
```
Start range of created date
```
```
End range of created date
```
```
End range of updated date
```
```
Start range of updated date
```

**attributeCode** String

**attributeValue** String

#### Body raw (json)

```
Document type
```
```
Attribute code (required include attribute value too)
```
```
Attribute value
```
```
json
```
##### {

```
"id": 2 ,
"type": 301 ,
"fullNumber": "FA/3/2023",
"foreignNumber": "",
"calculatedOn": 2 ,
"paymentMethod": "gotówka",
"currency": "PLN",
"payer": {
"id": 1 ,
"code": "!NIEOKREŚLONY!",
"name1": "tak",
"name2": "",
"name3": "",
"vatNumber": "",
"country": "",
"city": "",
"street": "",
"postCode": "",
"houseNumber": "",
"flatNumber": "",
"phone1": "",
"phone2": "",
"inactive": 0 ,
"defaultPrice": 0 ,
"bankAccountNumber": null,
"regon": "",
"email": "",
"paymentMethod": "gotówka",
"dateOfPayment": 0 ,
"maxPaymentDelay": 0 ,
"created": "2006-10-28T08:40:00",
"updated": "2023-09-26T20:49:34",
"description": "",
"countryCode": "",
"countryIso": "",
"group": "",
"purchaseCategory": ""
},
"defaultPayer": {
"type": 3 ,
```

// "id" : 10,
"code": "KA",
// "bankAccountNumber" : "12-********-**************** ",

"name1": "Wojtek",
"name2": "",
"name3": "",
"vatNumber": "",
"country": "",
"city": "",
"street": "",
"postCode": "",
"houseNumber": "",
"flatNumber": "",
"phone1": "",
"phone2": "",
"inactive": 0 ,
"defaultPrice": 0 ,
"regon": "",
"email": "",
"paymentMethod": "gotówka",
"dateOfPayment": 0 ,
"maxPaymentDelay": 0 ,
"created": "2006-10-28T08:40:00",
"updated": "2023-08-31T11:05:15",
"description": "",
"countryCode": "",
"countryIso": "",
"group": "",
"purchaseCategory": ""
},
"recipient": {
"id": 1 ,
"code": "!NIEOKREŚLONY!",
"name1": "next",
"name2": "",
"name3": "",
"vatNumber": "",
"country": "",
"city": "",
"street": "",
"postCode": "",
"houseNumber": "",
"flatNumber": "",
"phone1": "",
"phone2": "",
"inactive": 0 ,
"defaultPrice": 0 ,
"regon": "",
"email": "",
"paymentMethod": "gotówka",
"dateOfPayment": 0 ,
"maxPaymentDelay": 0 ,
"created": "2006-10-28T08:40:00",
"updated": "2023-08-31T11:05:15",
"description": "",
"countryCode": "",
"countryIso": "",
"group": "",
"purchaseCategory": ""
},
"elements": [


elements: [
{
"id": 1 ,
"itemId": 32 ,
"code": "Stojak",
"name": null,
"manufacturerCode": "",
"unitNetPrice": 0 ,
"unitGrossPrice": 0 ,
"totalNetValue": 0 ,
"totalGrossValue": 0 ,
"quantity": 1 ,
"vatRate": 23 ,
"setCustomValue": true,
"currentQuantity": 0 ,
"unit": "SZT",
"purchaseAmount": 0 ,
"attributes": [],
"category" : "MAT. PODSTAWOWE"
},{
"id": 2 ,
"itemId": 32 ,
"code": "stojak",
"name": null,
"manufacturerCode": "",
"unitNetPrice": 0 ,
"unitGrossPrice": 0 ,
"totalNetValue": 0 ,
"totalGrossValue": 0 ,
"quantity": 1 ,
"vatRate": 23 ,
"setCustomValue": true,
"currentQuantity": 0 ,
"unit": "SZT",
"purchaseAmount": 0 ,
"attributes": [],
"category" : "MAT. PODSTAWOWE"
}
],
"attributes": [],
"description": "",
"status": 1 ,
"sourceWarehouseId": 1 ,
"targetWarehouseId": 0 ,
"documentSaleDate": "2023-07-14T00:00:00",
"documentIssueDate": "2023-07-14T00:00:00",
"documentPaymentDate": "2023-07-14T00:00:00",
"documentReservationDate": "0001-01-01T00:00:00",
"documentCorrectionDate": "0001-01-01T00:00:00",
"documentReceptionDate": "0001-01-01T00:00:00",
"documentReceiptDate": "0001-01-01T00:00:00",
"documentDeliveryDate": "0001-01-01T00:00:00",
"documentPurchaseDate": "0001-01-01T00:00:00",
"documentReleaseDate": "0001-01-01T00:00:00",
"amountPaid": 0 ,
"amountToPay": 0 ,
"checkIfExists": false,
"symbol": "FA",
"series": "-",
"number": 8 ,
"state": 0 ,
"category": ""


```
http://{{API}}/api/RelatedDocuments
```
#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### PARAMS

**documentId** Int32

```
http://{{API}}/api/DocumentsExport
```
```
category: ,
"internalReservation": 0 ,
"transactionType": 0 ,
```
```
"activeVatVies": 0
}
```
### GET http://YOUR_IP/api/RelatedDocuments

#### Function anable to download list of related document

**Body Response :**

**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Definition
```
```
Int32 id Document ID
```
```
String type Document type
```
```
Int32 warehouseId Warehouse ID
```
```
String fullNumber Full number
```
```
DateTime date Document date
```
```
Decimal totalNetValue Total net value
```
```
Filter by document id
```
### GET http://YOUR_IP/api/DocumentsExport


#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### PARAMS

**id** Int32

**companyName** String

**databaseName** String

**printId** int32

**standardPrint** bool

#### Export document to PDF

Function have access to use print system CrystalReports and sPrint

**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

```
Optima document ID
```
```
Company name (Default in Web.Config)
```
```
Database name (Default in Web.Config)
```
```
Optima print ID - if not set, default
```
```
Is it a standard print.
```

```
http://{{API}}/api/DocumentsExport
```
#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### PARAMS

**id** Int32

**fullNumber** String

```
http://{{API}}/api/DocumentsStatus
```
### GET http://YOUR_IP/api/DocumentsExportEpp

#### Export document to EPP

**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

```
Optima document ID
```
```
Optima document full number
```
### GET http://YOUR_IP/api/DocumentsStatus

#### The Function enable to download history of the date base for DocumentsStatus

**Body Response:**


#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### PARAMS

**id** Int32

**foreignNumber** String

**type** Int32

**Company Data Fields :**

**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Definition
```
```
Int32 documentOptimaId Unique document ID in Optima
```
```
Int32 type Document type in Optima
```
```
String fullNumber Full number of document in
Optima (get)
```
```
String foreignNumber Foregin Number
```
```
Short status -1 Canceled, 0 Confirmed, 1 To
Edit
Bool manyDocuments true - exists many orders in
Optima, false - exist only one
order
```
```
Object companyData Company Data obejct for
Document
```
```
Type Name Definition Length Required
```
```
String companyName Chosen Company
Name for
Document
```
```
None No
```
```
String databaseName Chosen
Database Name
for Document
```
```
None No
```
```
Optima document ID
```
```
Foreign number from an external system
```

```
yp
```
**status** Int32

**offset** Int32

**limit** Int32

**databaseName** String

```
http://{{API}}/api/DocumentsFiscalize
```
#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### PARAMS

**id** Int32

**databaseName** String

```
Type of Comarch ERP Document
```
```
Status of Comarch ERP Document
```
```
Initial record from which data will be retrieved (default: 0)
```
```
Limit of one-time downloaded records (default: 2000)
```
```
Database name (Default in Web.Config)
```
### GET http://YOUR_IP/api/DocumentsFiscalize

**The Function enable to download history of the date base for DocumentsFiscalize**

**Body Response :**

**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Definition
```
```
Bool fiscalized true/false
```
```
Optima document ID
```
```
Chosen Database Name(Default Web Api Config)
```

```
http://{{API}}/api/Settlements
```
#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### PARAMS

**id** Int32

**billingDocumentNumber** String

### GET http://YOUR_IP/api/Settlements

**The Function enable to download history of the date base for Settlements**

**Body Response :**

**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Definition
```
```
Int32 Id Settlement Id
```
```
String billingDocumentNumber Document Number
```
```
Decimal billingDocumentValue Document Value
```
```
String billedDocumentNumber Billed Document Number
```
```
Decimal billedDocumentValue Biled Document Value
```
```
(DataTime)String settlementDate Settlement Date
```
```
Decimal billedValue Billed Value
```
```
Decimal billedValueInLocalCurrency Billed Value in Local Currency
```
```
Decimal currecnyRateDifference Currency Rate Difference
```
```
Int32 billingDocumentId Billing Document Id
```
```
Int32 billingDocumentType Billing Document Type
```
```
Int32 nonCashBillingDocumentId Non Cash Billing Document Id
```
```
Int32 billedDocumentId Billed Document Id
```
```
Int32 billedDocumentType Billed Document Type
```
```
Settlements Id
```
```
Billing document number
```

**billedDocumentNumber** String

**settlementDateStart** String (yyyy-MM-dd)

**settlementDateEnd** String (yyyy-MM-dd)

**billingDocumentId** Int32

**billingDocumentType** Int32

**nonCashBillingDocumentId** Int32

**billedDocumentType** Int32

**nonCashBilledDocumentInt** Int32

**billingDocumentOwnerId** Int32

**billedDocumentOwnerId** Int32

**billingDocumentOwnerType** Int32

**billedDocumentOwnerType** Int32

**billing_document_number** String

**billed_document_number** String

**billing_document_id** String

**billed_document_id** String

**non_cash_billed_document_id** String

```
Billed document number
```
```
Start of date range data settlement
```
```
End of date range data settlement
```
```
Biiling document id
```
```
Biiling document type
```
```
Non cash billing document id
```
```
Billed document type
```
```
Non cash billed document int
```
```
Biiling document owner id
```
```
Billed document owner id
```
```
Billing document owner type
```
```
Billed document owner type
```
```
Billing document number
```
```
Billed document number
```
```
Billing document id
```
```
Billed document id
```
```
Non cash billed document id
```

**databaseName** String

```
http://{{API}}/api/VatDocuments
```
```
Database name (Default in Web.Config)
```
### GET http://YOUR_IP/api/VatDocuments

**The Function enable to download history of the date base for VatDocuments**

**Body Response :**

**Company Data Fields :**

**Customer Filds for Contractor and Payer :**

```
Type Name Definition
```
```
Int32 id Vat Document Id
```
```
Int32 type Vat Document Type
```
```
String accountingId Accounting Id
```
```
String fullNumber Docuemnt Vat Full Number
```
```
String registerName Register Name
```
```
String correctedDocumentNumber Corrected Document Number
```
```
String paymentMethod Payment Method
```
```
String currency Currency
```
```
(DateTime)String documentSaleDate Document Sale Date
```
```
(DateTime)String documentIssueDate Document Issue Date
```
```
(DateTime)String documentReceiptDate Document Receipt Date
```
```
(DateTime)String documentPaymentDueDate Document Payment Due Date
```
```
String categoryCode Category Code
```
```
String categoryDescription Category Description
```
```
Type Name Definition Length Required
```
```
String companyName Chosen Company
Name for
Document
```
```
None No
```
```
String databaseName Chosen
Database Name
for Document
```
```
None No
```

**Custo e ds o Co t acto a d aye :**

[http://{{API}}/api/Customers](http://{{API}}/api/Customers)

**Additonal Amounts for Body :**

**Elements for Body :**

**Attributes for Body :**

**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

**401 U th i d**

```
Type Name Definition
```
```
Int32 id Additional Amounts Id
```
```
String category1Code Category First Code
```
```
String cateogry1Description Category First Description
```
```
String category2Code Category Secound Code
```
```
String category2Description Category Secound Description
```
```
String accountWn AccountWn
```
```
String accountMa AccountMa
```
```
Decimal amount Amount
```
```
Type Name Definition
```
```
Int32 id Element Id
```
```
String category1Code Category First Code
```
```
String category1Description Category First Description
```
```
String category2Code Category Secound Code
```
```
String category2Description Category Secound Description
```
```
Decimal grossValue Gross Value
```
```
Decimal vatRate Vat Rate
```
```
Int32 vatFlag Vat Flag
```
```
Int32 deductions Deducation
```
```
Int32 vatType Vat Type
```
```
Type Name Definition
```
```
String code Attribute Code
```
```
String value Attribute Value
```

#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### PARAMS

**idNumber** Int32

**fullNumber** String

**contractorNip** String

**documentSaleDateFrom** String (yyyy-mm-dd)

**documentSaleDateTo** String (yyyy-mm-dd)

**issueDateStart** String (yyyy-mm-dd)

**issueDateEnd** String (yyyy-mm-dd)

**invoiceId** Int32

**customerType** Int32

**customerId** Int32

**invoiceId** Int32

**customer_id** Int32, Int32...

**c_dataFrom** String (yyyy-mm-dd)

**c_dataTo** String (yyyy-mm-dd)

**u_dateFrom** String (yyyy-mm-dd)

**401 Unauthorized**

```
ID of single Vat document
```
```
Full number of single Vat document
```
```
Contractor NIP
```
```
Documents saled after specific date
```
```
Documents saled after before date
```
```
Issue document after specific date
```
```
Issue document before specific date
```
```
Id of invoice
```
```
Type of customer
```
```
Id of customer
```
```
InvoiceId
```
```
List of customer id separated by comma
```
```
Vat document date c_from
```
```
Vat document date c_to
```
```
V d d f
```

**u_dateTo** String (yyyy-mm-dd)

**databaseName** String

```
http:/{{API}}/api/DocumentsSettlement
```
#### AUTHORIZATION Bearer Token

**Token** <token>

#### PARAMS

**id** Int32

**documentId** Int32

**customerId** Int32

**c dateFrom** String (yyyy-mm-dd)

```
Vat document date u_from
```
```
Vat document date u_to
```
```
Database name (Default in Web.Config)
```
### GET http://YOUR_IP/api/DocumentsSettlement

**Body Response :**

```
Type Name Definition
```
```
Int32 id Settlement Id
```
```
Int32 documentId Document Id
```
```
Int32 paymentId Payment Id
```
```
String fullNumber Document full number
```
```
Decimal value Settlement value
```
```
(DateTime)String date Settlement date
```
```
(DateTime)String term Settlement term
```
```
String currency Settlement currency
```
```
Int32 customerId Customer Id
```
```
Int32 type 1 - event, 2 - payment
```
```
Settlement ID
```
```
Document ID
```
```
Customer ID
```

**c_dateFrom** String (yyyy mm dd)

**c_dateTo** String (yyyy-mm-dd)

**u_DateFrom** String (yyyy-mm-dd)

**u_DateTo** String (yyyy-mm-dd)

**limit** Int32

**offset** Int32

**databaseName** String

```
http://{{API}}/api/DocumentLibrary
```
```
Start range of created date
```
```
End range of created date
```
```
Start range of updated date
```
```
End range of updated date
```
```
Limit the numbers of results
```
```
Skip the numbers of results
```
```
Database name (Default in Web.Config)
```
### POST http://YOUR_IP/api/DocumentLibrary

#### The Function enable to Create DocumentLibrary for Document Optima

**Body Params Request :**

**Company Data Fields :**

```
Type Name Description Length Required
```
```
String fileName file name without
extension
```
```
50 Yes
```
```
String fileExtension file extension 5 Yes
```
```
Byte[] fileBinary binary array of
sending file
(jpg,png, pdf,..)
```
```
None Yes
```
```
Object companyData Company Data
obejct for
Document
```
```
None No
```
```
Type Name Definition
```
```
String companyName Chosen Company Name for
Document
String databaseName Chosen Database Name for
```

#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### Body raw (json)

```
http://{{API}}/api/DocumentsAggregation
```
**Http Correct Answer :**

**201 Created**

**Http Possible Answer :**

**401 Unauthorized**

```
Document
```
```
json
```
```
{
"docId": 1617 ,
"fileName": "FS_2021_PR",
"fileExtension": "jpg",
"fileBinary": "binarna wartość pliku pdf, zdjecia itp"
}
```
### POST http://YOUR_IP/api/DocumentsAggregation

#### The Function enable to Create DocumentAggregation

**Body Params Request :**


**Company Data Fields :**

**Elements For Body :**

**Http Correct Answer :**

**201 Created**

```
Type Name Description Length Required
```
```
Int32 sourceDocumentI
d
```
```
Id of Existing
Document in
Comarch ERP
Optima
```
```
None Yes
```
```
Int32 sourceType Type of Existing
Document in
Comarch ERP
Optima
```
```
None Yes
```
```
Int32 targetType Type of
Generated
Document in
Comarch ERP
Optima
```
```
None Yes
```
```
Int32 targetDocumentI
d
```
```
id of Generated
Document in
Comarch ERP
Optima
```
```
None returned
```
```
String targetDocumentF
ullNumber
```
```
Full Number of
Generated
Document in
```
```
1 - 30 returned
```
```
Type Name Definition
```
```
String companyName Chosen Company Name for
Document
```
```
String databaseName Chosen Database Name for
Document
```
```
Type Name Description Lenght Required
```
```
Int32 sourceElementId Element Id None No
```
```
Decimal quantity Element Quantity None No
```
```
Decimal unitPrice Element new unit
price
```
```
None No
```
```
List attributes List of attributes
of element on
created related
document
```
```
None No
```

#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### Body raw (json)

```
http://{{API}}/api/DocumentsCorrection
```
**Http Possible Answer :**

**401 Unauthorized**

```
json
```
```
{
"sourceDocumentId": 54 ,
"sourceType": 302 ,
"targetType": 306 ,
"targetDocumentId": 55 ,
"targetDocumentFullNumber": "WZ/312/2021"
}
```
### POST http://YOUR_IP/api/DocumentsCorrection

#### The function enable to Create different type of document correction for Optima

Document kind :

### Document dates:

```
Kind Description
```
```
301001 Quantity correction for Purchase Invoices
```
```
301002 Value correction for Purchase Invoices
```
```
302001 Quantity correction for Invoices
```
```
302002 Value correction for Invoices
```
```
302011 Data correction for Invoices
```
```
306001 Quantity correction for Sales Order Releases
```
```
306002 Value correction for Sales Order Releases
```
```
307001 Quantity correction for Purchase Order Receipts
```
```
307002 Value correction for Purchase Order Receipts
```

**Body Params Request :**

**Company Data Fields :**

**Elements For Body :**

```
Date Type
```
```
documentIssueDate 301, 302, 306, 307
```
```
documentCorrectionDate 301, 302, 306, 307
```
```
documentReceptionDate 301, 307
```
```
Type Name Definition Length Required
```
```
Int32 sourceDocumentI
d
```
```
Corrected
document id
```
```
None Yes
```
```
Int32 type Document type in
Optima
```
```
None Yes
```
```
Int32 kind Document kind in
Optima
```
```
None Yes
```
```
String fullNumber Full number of
document in
Optima (get)
```
```
0 - 30 No
```
```
String foreignNumber Foreign number 0 - 30 Required only for
corrections of
Purchase Order
Receipts
Short status 0 Confirmed, 1 To
Edit
```
```
None Yes
```
```
(DateTime)String documentIssueD
ate
```
```
(today's date by
default)
```
```
(yyyy-MM-dd) No
```
```
(DateTime)String documentCorrect
ionDate
```
```
(today's date by
default)
```
```
(yyyy-MM-dd) No
```
```
Type Name Definition Length Required
```
```
String companyName Chosen Company
Name for
Document
```
```
None No
```
```
String databaseName Chosen
Database Name
for Document
```
```
None No
```

**Elements of the payer for body :**

```
Type Name Definition Length Required
```
```
Int32 id ID of source
element
```
```
None Yes
```
```
Int32 itemId Item Id None No
```
```
String code Element Code 0-50 Yes
```
```
String name Element Name 0-255 No
```
```
String menufacturerCod
e
```
```
Menufacutrer
Code
```
```
0-50 No
```
```
Decimal unitNetPrice Unit Net Price None No
```
```
Decimal unitGrossPrice Unit Gross Price None No
```
```
Decimal totalNetValue Total Net Value None No
```
```
Decimal totalGrossValue Total GroSS
Value
```
```
None No
```
```
Decimal quantity Quantity None Yes
```
```
Decimal vatRate Vat Rate None No
```
```
Bool setCustomValue Set Custom
Value
```
```
None No
```
```
Type Name Definition Length Required
```
```
String name1 First name field 0-50 No
```
```
String name2 Second name
field
```
```
0-50 No
```
```
String name3 Third name field 0-50 No
```
```
String code Payer code 0-20 No
```
```
String countryCode Country identifier 2 No
```
```
String vatNumber VAT identification
number
```
```
0-40 No
```
```
String country Payer country 0-40 No
```
```
String street Street name 0-40 No
```
```
String city City name 0-40 No
```
```
String additionalAdress Additional
address
```
```
0-44 No
```
```
String voivodeship Administrative
region
```
```
0-40 No
```
```
String houseNumber House number 0-10 No
```

**Elements of the recipient for body :**

**DeliveryLots For Element :**

**Object Item Features For Delivery Lots :**

**Attributes For Element :**

**Http Correct Answer :**

```
Type Name Definition Length Required
```
```
String name1 First name field 0-50 No
```
```
String name2 Second name
field
```
```
0-50 No
```
```
String name3 Third name field 0-50 No
```
```
String code Recipient code 0-20 No
```
```
String countryCode Country identifier 2 No
```
```
String vatNumber VAT identification
number
```
```
0-40 No
```
```
String country Recipient country 0-40 No
```
```
String street Street name 0-40 No
```
```
String city City name 0-40 No
```
```
String additionalAdress Additional
address
```
```
0-44 No
```
```
String voivodeship Administrative
region
```
```
0-40 No
```
```
String houseNumber House number 0-10 No
```
```
Type Name Definition Length Required
```
```
Decimal quantity Quantity None Yes
```
```
Object features Item Features
Object
```
```
None No
```
```
Type Name Definition Length Required
```
```
String feature1Code Feature First
Code
```
```
None No
```
```
String feature1Value Feature First
Value
```
```
None No
```
```
Type Name Definition Length Required
```
```
String code Attribute Code 0-20 Yes
```
```
String value Attribute Value 0-100 Yes
```

#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### Body raw (json)

```
http://{{API}}/api/Documents
```
**201 Created**

**Http Possible Answer :
401 Unauthorized**

```
json
```
```
{
"sourceDocumentId": 101 ,
"type": 302 ,
"kind": 302002 ,
"documentIssueDate": "2021-03-15T00:00:00",
"documentCorrectionDate": "2021-03-15T00:00:00",
"documentReceptionDate": "2021-03-15T00:00:00",
"elements": [
{
"id": 123 ,
"code": "LAPTOP_15,6",
"unitNetPrice": 1000 ,
"unitGrossPrice": 1230 ,
"totalNetValue": 0 ,
"totalGrossValue": 0 ,
"quantity": 0
}
],
"status": 1 ,
"foreignNumber": "VALUE_CORRECTION"
}
```
### POST http://YOUR_IP/api/Documents

#### The Function enable to Create different tipe of document for Optima

### Document dates:


### Supported document types :

**Body Params Request :**

```
Date Document Type
```
```
documentSaleDate 302, 317, 320
```
```
documentIssueDate 301, 302, 303, 304, 306, 307, 308, 309, 312, 317,
320
documentPaymentDate 301, 302, 308, 317, 320
```
```
documentReservationDate 308
```
```
documentReceiptDate 307
```
```
documentReceptionDate 301, 307
```
```
documentDeliveryDate 309
```
```
documentPurchaseDate 301
```
```
documentReleaseDate 306
```
```
Type Description
```
```
301 Purchase Invoice
```
```
302 Invoice
```
```
303 Internal Receitps
```
```
304 Internal Releases
```
```
305 Receipt
```
```
306 Sales Order Releases
```
```
307 Purchase Order Receipts (Only Add document and
Add correction)
```
```
308 Sales Orders
```
```
309 Purchase Order
```
```
312 Warehouse Movements
```
```
317 Internal Product Acceptance
```
```
320 Pro Forma Invoice
```

**Company Data Fields :**

**Customer Fields for Payer and Recipent :**

[http://{{API}}/api/Customers](http://{{API}}/api/Customers)

**Customer Field for the Default Payer :**

```
Type Name Definition Length Required
```
```
Int32 type Type od Comarch
ERP Optima
document
```
```
None Yes
```
```
String fullNumber Full number of
document in
Optima (get)
```
```
0 - 30 No
```
```
String foreignNumber Foregin Number 0 - 30 No
```
```
Int32 calculatedOn 1 - Subtotal
(Net),
2 - Total (Gross)
```
```
None Yes
```
```
String paymentMethod Payment method
in Optima
```
```
1 - 20 Yes
```
```
String currency Currency in
Optima
```
```
3 Yes
```
```
String description Description 1024 No
```
```
Int32 status 0 - confirmed,
1 - for editing
```
```
None Yes
```
```
Int32 sourceWarehous Id of warehouse None Yes
```
```
Type Name Definition Length Required
```
```
String companyName Chosen Company
Name for
Document
```
```
None No
```
```
String databaseName Chosen
Database Name
for Document
```
```
None No
```

**Elements For Body :**

**Attribute For Elements :**

**Http Correct Answer :**

```
Type Name Definition Length Required
```
```
String code Acronym for
default payer
```
```
0-20 Yes
```
```
String name1 First row of the
customer name
```
```
0-50 Yes
```
```
Int32 type Type of entity ( 1
```
- customer, 2 -
bank, 3 -
employee, 4 -
offices)

```
None Yes
```
```
String bankAccountNu
mber
```
```
Bank account
number
```
```
0-51 No
```
```
Type Name Definition Length Required
```
```
Int32 itemId ID of Optima
item.
```
```
None No
```
```
String code Code of item.
Field used to
associate with an
item in Optima
```
```
0 - 50 Yes
```
```
String name Item name. In
ERP Optima item
must have a
marked
parameter "Edit
name in
documents"
```
```
1 - 255 No
```
```
String manufacturerCod
e
```
```
Manufacture
code of item.
```
```
1 - 50 No
```
```
String category Category code
for position
```
```
None No
```
```
Decimal unitNetPrice Unit price
without tax
```
```
None No
```
```
Type Name Description Length Required
```
```
String code Unique code of
Attribute
```
```
1-20 Yes
```
```
String value Value of Attribute 1-100 Yes
```

#### AUTHORIZATION Bearer Token

**Token** <token>

#### Body raw (json)

```
http://{{API}}/api/DocumentsSettlement
```
**201 Created**

**Http Possible Answer :**

**401 Unauthorized**

```
json
```
```
{
"type": 302 ,
"SourceWareHouseId": 1 ,
//"TargetWareHouseId": 2,
"foreignNumber": "",
"calculatedOn": 1 ,
"paymentMethod": "przelew",
"currency": "PLN",
"elements": [
{
"code": "KOSZULA",
"quantity": 20.0000,
"vatRate": 23.00
//"setCustomValue": true
}
],
"description": "Document description",
"status": 1 ,
"documentIssueDate": "2024-12-11T00:00:00",
"payer": {
"code": "ACTINO"
"name1": "ACTINO"
}
}
```
### POST http://YOUR_IP/api/DocumentsSettlement

#### Adding a settlement to existing payment and document. In body we pass number of payment (we

#### get this field in method /api/Payments - paymentFullNumber) and full number of document.

#### Document in Optima must be confirmed.

**Body Params Request :**


#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### Body raw (json)

**Company Data Fields :**

**Http Correct Answer :**

**201 Created**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Description Length Required
```
```
String documentFullNu
mber
```
```
Full number of
document in
Optima, e.g.
FS/02/02/2021
```
```
1-30 Yes
```
```
String paymentFullNum
ber
```
```
Full number of
payment in
Optima, e.g.
KP/2/2021/KASA
```
```
1-30 Yes
```
```
String error Error None No
```
```
Object companyData Company Data
obejct for
Document
```
```
None No
```
```
Type Name Definition Length Required
```
```
String companyName Chosen Company
Name for
Document
```
```
None No
```
```
String databaseName Chosen
Database Name
for Document
```
```
None No
```
```
json
```
```
{
"documentFullNumber":"FA/159/2023",
"paymentFullNumber": "KP/2/2023/IGO-C"
}
```

```
http://{{API}}/api/VatDocuments
```
### POST http://YOUR_IP/api/VatDocuments

#### The Function enable to Create Vat Document for Erp Optima

**Body Params Request :**

**Company Data Fields :**

**jpkV7Codes List Np :**

["GTU_01","RO","IMP"]

**Customer Object For Contractor and Payer:**

[http://{{API}}/api/Customers](http://{{API}}/api/Customers)

**Element For Body :**

```
Type Name Definition Length Required
```
```
Int32 type 1 - VAT purchase
register 2 - VAT
sales register
```
```
None Yes
```
```
String accountingId Accounting ID 0 - 50 Return value
```
```
String fullNumber The full number
of the
purchase/sales
document
```
```
0 - 256 No
```
```
String registerName The name of
register
```
```
0 - 20 Yes
```
```
String correctedDcume
ntNumber
```
```
Full number of
corrected
document
```
```
0 - 256 No
```
```
String paymentMethod Name of
Payment
```
```
0 - 20 Yes
```
```
String currency Currency ISO
code
```
```
0 - 3 Yes
```
```
String transactionType Export
transaction Type
```
```
0 - 3 Return Value
```
```
Type Name Definition Length Required
```
```
String companyName Chosen Company
Name for
Document
```
```
None No
```
```
String databaseName Chosen
Database Name
for Document
```
```
None No
```

**Attribute for Body :**

**Additional Amounts for Body :**

```
Type Name Definition Length Required
```
```
Int32 id Unique document
element ID in
Optima
```
```
None Return Value
```
```
String category1Code Category First
Code
```
```
1 - 20 No
```
```
String category1Descrip
tion
```
```
Category First
Description
```
```
1 - 20 No
```
```
String category2Code Category
Secound Code
```
```
1 - 20 No
```
```
String category2Descri
ption
```
```
Category
Secound
Description
```
```
1 - 20 No
```
```
Decimal grossValue Gross Value None No
```
```
Decimal netValue Net Value None No
```
```
Int32 vatRate Percentage value
of the VAT rate
```
```
None No
```
```
Decimal vatValue Vat value
overwritten by
```
```
None No
```
```
Type Name Definition Length Required
```
```
String code Unique code of
Attribute
```
```
1-20 Yes
```
```
String value Value of Attribute 1-100 Yes
```

#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

```
http://{{API}}/api/DocumentsSettlement
```
**Http Correct Answer :**

**201 Created**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Definition Length Required
```
```
Int32 id Additional
Amount Id
```
```
None No
```
```
Decimal amount Amount None Yes
```
```
String category1Code Category First
Code
```
```
1 - 20 No
```
```
String category1Descrip
tion
```
```
Category First
Description
```
```
1 - 50 No
```
```
String category2Code Category
Secound Code
```
```
1 - 20 No
```
```
String category2Descri
ption
```
```
Category
Secound
Description
```
```
1 - 50 No
```
```
String accountWn Account in the
current
accounting
period Format:
AccId: 011.2
```
```
1 - 50 No
```
```
String accountMa Account in the
current
```
```
1 - 50 No
```
### POST http://YOUR_IP/api/DocumentsSettlementsMany

#### Adding a settlement to existing payment and document. In body we pass number of payment (we

#### get this field in method /api/Payments - paymentFullNumber) and full number of document.

#### Document in Optima must be confirmed.

**Body Params Request :**


#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### Body raw (json)

**Company Data Fields :**

**Http Correct Answer :**

**201 Created**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Definition Length Required
```
```
String documentFullNu
mber
```
```
NumberFull
number of
document in
Optima, e.g.
FS/02/02/2021
```
```
1-30 Yes
```
```
String paymentFullNum
ber
```
```
Full number of
payment in
Optima, e.g.
KP/2/2021/KASA
```
```
1-30 Yes
```
```
String error Error None No
```
```
Object companyData Company Data
obejct for
Document
```
```
None No
```
```
Type Name Definition Length Required
```
```
String companyName Chosen Company
Name for
Document
```
```
None No
```
```
String databaseName Chosen
Database Name
for Document
```
```
None No
```
```
json
```
```
{
"documentFullNumber":"FS/6/2021",
"paymentFullNumber": "KP/2/2021/KASA"
}
```

```
http://{{API}}/api/DocumentsSettlementCorrection
```
#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### Body raw (json)

### POST http://YOUR_IP/api/DocumentsSettlementCorrection

#### The Function Enable to Create Documents Settlement Correction for Erp Optima

**Body Params Request :**

**Company Data Fields :**

**Http Correct Answer :**

**201 Created**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Definition Length Required
```
```
String documentFullNu
mber
```
```
Full number of
document in
Optima
```
```
0 - 30 Yes
```
```
String correctionFullNu
mber
```
```
Foregin Number
of document in
Optima
```
```
0 - 30 Yes
```
```
String error Error None No
```
```
Object companyData Company Data
obejct for
Document
```
```
None No
```
```
Type Name Definition Length Required
```
```
String companyName Chosen Company
Name for
Document
```
```
None No
```
```
String databaseName Chosen
Database Name
for Document
```
```
None No
```
```
json
```

```
http://YOUR_IP/api/CollectiveCorrections
```
#### AUTHORIZATION Bearer Token

##### [

##### {

```
"documentFullNumber" : "PA/22/2023",
"correctionFullNumber" : "PAKOR/10/2023"
}
]
```
### POST http://YOUR_IP/api/CollectiveCorrections

**Body Params Request :**

**Company Data Fields :**

```
Type Name Definition Length Required
```
```
Int32 id Document ID in
Optima (Only in
response)
```
```
None No
```
```
String fullNumber Full number of
document in
Optima (Only in
response)
```
```
0 - 30 No
```
```
String correctionTitle Correction title 0 - 1024 No
```
```
Int32 type Document type in
Optima
(Currently only
302)
```
```
None Yes
```
```
Int32 kind Document kind in
Optima
(Currently only
302010)
```
```
None Yes
```
```
String paymentMethod Payment method 1 - 20 Yes
```
```
Int32 sourceWarehous
eId
```
```
Source
warehouse ID
```
```
None Yes
```
```
Type Name Definition Length Required
```
```
String companyName Chosen Company
Name for
Document
```
```
None No
```
```
String databaseName Chosen
Database Name
for Document
```
```
None No
```

**Token** <token>

#### Body raw (json)

```
json
```
```
{
"type": 302 ,
"kind": 302010 ,
"status": 1 ,
"payer": {
"id": 13 ,
"code": "BIGGUN",
"name1": "Big Gun LTD",
"name2": "",
"name3": "",
"vatNumber": "879839482374",
"country": "Great Britain",
"city": "Willing Town",
"street": "Great Town",
"postCode": "13213",
"houseNumber": "723",
"flatNumber": "",
"phone1": "",
"phone2": "",
"inactive": 0 ,
"defaultPrice": 4 ,
"regon": "",
"email": "<EMAIL>",
"paymentMethod": "przelew",
"dateOfPayment": 20 ,
"maxPaymentDelay": 0 ,
"created": "2020-07-30T12:53:00",
"updated": "2023-11-22T13:16:28",
"description": "",
"countryCode": "GB",
"countryIso": "ISO",
"group": "UE",
"purchaseCategory": ""
},
"recipient": {
"id": 13 ,
"code": "BIGGUN",
"name1": "Big Gun LTD",
"name2": "",
"name3": "",
"vatNumber": "879839482374",
"country": "Great Britain",
"city": "Willing Town",
"street": "Great Town",
"postCode": "13213",
"houseNumber": "723",
"flatNumber": "",
"phone1": "",
"phone2": "",
"inactive": 0 ,
```

```
http://{{API}}/api/CloseDocuments
```
#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### PARAMS

**id** Int32

```
"defaultPrice": 4 ,
"regon": "",
"email": "<EMAIL>",
```
```
"paymentMethod": "przelew",
"dateOfPayment": 20 ,
"maxPaymentDelay": 0 ,
"created": "2020-07-30T12:53:00",
"updated": "2023-11-22T13:16:28",
"description": "",
"countryCode": "GB",
"countryIso": "ISO",
"group": "UE",
"purchaseCategory": ""
},
"paymentMethod": "przelew",
"documentIds": [
3644
],
"calculatedOn": 1 ,
"discountAmount": 50 ,
"sourceWarehouseId": 1 ,
"correctionTitle": "Tytuł testowy",
"documentIssueDate": "2024-02-18",
"documentCorrectionDate": "2024-02-19"
}
```
### PUT http://YOUR_IP/api/CloseDocuments

#### This function allows you to close a document - required document type 308, 309, 320.

Query Params

int ID - ID of document in Comarch ERP Optima

**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

```
ID of document in Comarch ERP Optima
```

```
http://{{API}}/api/DocumentsAttribute/5
```
### PUT http://YOUR_IP/api/DocumentsAttribute

This method allows you to update and add attributes to a document in Comarch ERP Optima. You can update
attribues via id (Optima document ID) or via a foreignNumber.
If you enter the value of the id parameter equal to zero, you must enter the foreign document number in the body.
In the body, you can also use type and status to search for a document.

**Body Params Request :**

**Company Data Fields :**

**Attrribute for Body :**

**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

```
Type Name Definition Length Required
```
```
String foreignNumber Foregin Number None No
```
```
Int32 type Type None No
```
```
Int32 status Status None No
```
```
Bool manyDocuments Many Documents None No
```
```
List attributes List of Attribute None Yes
```
```
Object companyData Company Data
obejct for
Document
```
```
None No
```
```
Type Name Definition Length Required
```
```
String companyName Chosen Company
Name for
Document
```
```
None No
```
```
String databaseName Chosen
Database Name
for Document
```
```
None No
```
```
Type Name Definition Length Required
```
```
String code Code for
Attribute
```
```
0-20 Yes
```
```
String value Value for
Attribute
```
```
0-100 Yes
```

#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### PARAMS

**id** Int32

#### Body raw (json)

```
http://{{API}}/api/DocumentElementsAttribute
```
**401 Unauthorized**

```
Optima document ID
```
```
json
```
```
{
"attributes": [
{
"code": "ORD",
"value": "OrdValue"
},
{
"code": "PLATNOSC",
"value": "PaymentValue"
},
{
"code": "REFNUMBER",
"value": "RefValue"
}
]
}
```
### PUT http://YOUR_IP/api/DocumentElementsAttribute

This method allows you to update attribute on existing element on specific document.

**Body Params Request :,**

```
Type Name Definition Length Required
```
```
Int32 foreignNumber Document Id None No
```
```
List elements list of elements None No
```
```
Object companyData Company Data
obejct for
Document
```
```
None No
```

#### AUTHORIZATION Bearer Token

**Token** <token>

#### PARAMS

**id** Int32

#### Body raw (json)

**Body Elements :**

**Company Data Fields :**

**Attrribute for Body :**

**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

```
Document
```
```
Type Name Definition Lenght
```
```
int32 lp Lp element none
```
```
List attributes list of attributes none
```
```
Type Name Definition Length Required
```
```
String companyName Chosen Company
Name for
Document
```
```
None No
```
```
String databaseName Chosen
Database Name
for Document
```
```
None No
```
```
Type Name Definition Length Required
```
```
String code Code for
Attribute
```
```
0-20 Yes
```
```
String value Value for
Attribute
```
```
0-100 Yes
```
```
Optima document ID
```
```
json
```

```
http://{{API}}/api/DocumentElementsWarehouse
```
##### {

```
"documentId" : 1303 ,
"elements" : [
{
"lp" : 1 ,
"attributes": [
{
"code": "KOLOR",
"value": "WOJ_TEST"
}
]
}
]
}
```
### PUT http://YOUR_IP/api/DocumentElementsWarehouse

This method allows you to update existing warehouse to specific element on document.

**Body Params Request :**

**Company Data Fields :**

**Element Data fields**

```
Type Name Definition Length Required
```
```
int32 documentid Document ID None No
```
```
List elements Document
elements
```
```
None No
```
```
Object companyData Company Data
obejct for
Document
```
```
None No
```
```
Type Name Definition Length Required
```
```
String companyName Chosen Company
Name for
Document
```
```
None No
```
```
String databaseName Chosen
Database Name
for Document
```
```
None No
```
```
Type Name Definition Length Required
```
```
int32 elementId Element ID None No
```
```
int32 warehouseId Warehouse ID None No
```

#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### Body raw (json)

```
http://{{API}}/api/DocumentsStatus
```
**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

```
json
```
```
{
"documentId" : 1111 ,
"elements" : [
{
"elementId" : 1136 ,
"warehouseId" : 1
}
]
}
```
### PUT http://YOUR_IP/api/DocumentsStatus

Change status of document in Comarch ERP Optima.
You can change the document status via documentOptimaId or via a foreignNumber.
If you enter the value of the id parameter equal to zero, you must enter the foreign document number in the body. You
can use also type of document in body
More information in the examples

**Body Params Request :**


#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### PARAMS

**id** Int32

**Company Data Fields :**

**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Definition
```
```
Int32 documentOptimaId Unique document ID in Optima
```
```
Int32 type Document type in Optima
```
```
String fullNumber Full number of document in
Optima (get)
```
```
String foreignNumber Foregin Number
```
```
Short status -1 Canceled, 0 Confirmed, 1 in
document buffer (unavailable,
only 0 and -1)
int32 PAGenerateWz Flag which determines that
document PA generate WZ when
confirmed
(Values 0 - No
1- Generate)
```
```
Int32 GenerateWz Flag which determines that
document generate WZ when
confirmed
(Values 0 - No
1- Generate)
(Available for type 302)
```
```
Type Name Definition Length Required
```
```
String companyName Chosen Company
Name for
Document
```
```
None No
```
```
String databaseName Chosen
Database Name
for Document
```
```
None No
```

**id** Int32

#### Body raw (json)

```
http://{{API}}/api/DocumentsFiscalize
```
```
Optima document ID
```
```
json
```
```
{
"status": 0 ,
"foreignNumber": "FOREIGN_2",
"type": 302
}
```
### PUT http://YOUR_IP/api/DocumentsFiscalize

Get status of document from Comarch ERP Optima.

#### 200 - Ok

#### 300 - Multiple choices (multiple documents with given foreign number)

#### 400 - Error due to incorrect data sent by the user.

#### 401 - Unauthorized

#### 404 - Not found

### Document Status:

**Company Data Fields :**

```
Type Name Definition Length Required
```
```
Int32 documentOptima
Id
```
```
Unique document
ID in Optima
```
```
None No
```
```
Object companyData Company Data
obejct for
Document
```
```
None No
```
```
Type Name Definition Length Required
```
```
String companyName Chosen Company
Name for
Document
```
```
None No
```
```
String databaseName Chosen
Database Name
for Document
```
```
None No
```

#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### PARAMS

**id** Int32

```
http://{{API}}/api/Documents?id=1220&type=306
```
#### AUTHORIZATION Bearer Token

**Token** <token>

#### PARAMS

**id** 1220

**type** 306

#### AUTHORIZATION OAuth 2.0

This folder is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

```
http://{{API}}/api/EmployeeAttributes
```
**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

```
Optima document ID
```
### DELETE http://YOUR_IP/api/Documents

This function allows us to remove an erroneous document from the buffer.

## Employees

### GET http://YOUR_IP/api/EmployeeAttributes


#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### PARAMS

**id** Int32

**typeId** Int32

**employeId** Int32

**valueFrom** String (yyyy-MM-dd)

**valueTo** String (yyyy-MM-dd)

**databaseName** String

#### The Function enable to download history of date base for Employee Attributes List

**Body Response :**

**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Definition
```
```
Int32 id Employee Attrubute Id
```
```
Int32 employeeId Employee Id
```
```
Int32 attributeId Attribute Id
```
```
Int32 typeId Id Type Attribute
```
```
String typeName Name Type Attribute
```
```
(DateTime)String valueFrom Date Value From
```
```
(DateTime)String valueTo Date Value To
```
```
String value Attribute Value
```
```
Unique Employee Attribute id in the Comarch ERP Optima
```
```
Type of Employee Attribute of Comarch ERP Optima
```
```
Unique Employee id in the Comarch ERP Optima
```
```
Value attribute from
```
```
Value attribute to
```
```
Database name (Default in Web.Config)
```

```
http://{{API}}/api/EmployeeLeaves?year=int32&employeeDescription=string
```
#### AUTHORIZATION Bearer Token

**Token** <token>

#### PARAMS

**year** int32

**employeeDescription** string

### GET http://YOUR_IP/api/EmployeeLeaves

#### The Function enable to get employee leaves

**Body Response :**

**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Definition
```
```
String employeeCode Employee Code
```
```
String employeeDescription Employee description
```
```
Array leaves Array of leaves
```
```
String leaveType Leave type
```
```
decimal total total leave days
```
```
decimal yearTotal total leave days for year
```
```
decimal remaining remaining leave days
```
```
int32 year Year
```
```
Unique Employee Attribute id in the Comarch ERP Optima
```
```
Unique Employee description on employee card in Comarch ERP Optima
```
### POST http://YOUR_IP/api/EmployeeLeaves


```
http://{{API}}/api/EmployeeLeaves
```
#### AUTHORIZATION Bearer Token

**Token** <token>

#### Body raw (json)

#### The Function enable to post employee leaves

**Body Request :**

DatesAndDimension array

**Http Correct Answer :**

**201 Ok**

**Http Possible Answer :**

**401 Unauthorized**

400 BadRequest

```
Type Name Definition Required
```
```
String Code Employee Code No [ One of them,
employeeDescription
or code]
String EmployeeDescription Employee description No [One of them,
employeeDescription
or code]
```
```
String Type Leave type Yes
```
```
Array DatesAndDimension Dates and dimension
value
```
```
Yes
```
```
Type Name Definition
```
```
DateTime Date Date of leave
```
```
decimal Value Dimension value [Range 1- 0.5]
```
```
json
```
```
{
"EmployeeDescripton" : "TO POTRZEBA",
"Type" : "urlop wypoczynkowy",
```
```
"DatesAndDimension": [
{
"Date": "2024-07-01",
```

```
http://{{API}}/api/Employees
```
##### ,

```
"Value": 0.5
},
{
"Date": "2024-07-02",
"Value": 0.5
},
{
"Date": "2024-07-03",
"Value": 1
}
]
```
```
}
```
### GET http://YOUR_IP/api/Employees

#### The Function enable to download history of date base for Employee List

**Body Response :**

##### FTE

**Absences**

```
Type Name Definition
```
```
Int32 id Employee Id
```
```
String code Employee Code
```
```
Int32 type Employee type (1 - employee, 2 -
owner, co-worker; 10 - full-time
employee; 20 - contractor)
String firstName Employee First Name
```
```
String lastName Employee Last Name
```
```
String Pesel Pesel number
```
```
List FTEs List of full time employments
```
```
List Absences List of absences
```
```
type name definition
```
```
string departmentCode department code
```
```
string departmentName department fullname
```
```
string employmentTime full-time employment
```
```
string position Employee position
```

#### AUTHORIZATION Bearer Token

**Token** <token>

#### PARAMS

**id** Int32

**typeId** Int32

**optima_id** Int32, Int32, Int32

**optima_code** 001/E

**databaseName** String

**pesellist** string

**dateFrom** string(yyyy-MM-dd)

**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

```
type name definition
```
```
string absencePeriodFrom DateTime for start absence
period
string absencePeriodTo DateTime for end absence period
```
```
string absenceName Name of absence
```
```
int32 absenceReason Absence reason
```
```
int32 absenceAllDay Is absence is for all day
```
```
decimal absenceHours Absence hours is for all day
value is 0
```
```
Unique Employee id in the Comarch ERP Optima
```
```
Type of Employee of Comarch ERP Optima
```
```
List of employee id separated by comma
```
```
List of value separated by comma
```
```
Database name (Default in Web.Config)
```
```
List of pesel numbers, separated by comma.
```
```
Start range of absence date
```

**dateTo** string(yyyy-MM-dd)

**employeeDescription** string

```
http://{{API}}/api/Employees
```
#### AUTHORIZATION Bearer Token

**Token** <token>

#### PARAMS

**id** Int32

**typeId** Int32

**optima_id** Int32, Int32, Int32

```
End range of absence date
```
```
Employee description
```
### GET http://YOUR_IP/api/EmployeeWorkPlan

#### The Function enable to get work plan list for employee

**Body Response :**

**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Definition
```
```
int32 employeeId Employee ID
```
```
int32 year Year
```
```
int32 month Month
```
```
int32 day Day
```
```
int32 hourStart Start hour
```
```
int32 hourEnd End hour
```
```
string zone Definition of work plan for day.
```
```
Unique Employee id in the Comarch ERP Optima
```
```
Type of Employee of Comarch ERP Optima
```

**optima_code** 001/E

**databaseName** String

**pesellist** string

**dateFrom** string(yyyy-MM-dd)

**dateTo** string(yyyy-MM-dd)

**employeeDescription** string

```
http://{{API}}/api/EmployeWorkZones
```
```
List of employee id separated by comma
```
```
List of value separated by comma
```
```
Database name (Default in Web.Config)
```
```
List of pesel numbers, separated by comma.
```
```
Start range of absence date
```
```
End range of absence date
```
```
Employee description
```
### PUT http://YOUR_IP/api/EmployeeWorkZones

#### The Function enable to modify employee work zone

**Body Parameters:**

**Company Data Fields :**

```
Type Name Definition
```
```
string employeeCode Employee code
```
```
dateTime startedAt started at
```
```
dateTime endAt end at
```
```
string type type
```
```
Object companyData company data
```
```
Type Name Definition Length Required
```
```
String companyName Chosen Company
Name for
Document
```
```
None No
```
```
String databaseName Chosen
Database Name
for Document
```
```
None
```

#### AUTHORIZATION Bearer Token

**Token** <token>

```
http://{{API}}/api/EmployeeAbsenceTypes
```
#### AUTHORIZATION Bearer Token

**Token** <token>

#### PARAMS

**absenceId** Int32

**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

### GET http://YOUR_IP/api/EmployeeAbsenceTypes

#### The Function enable to get absence types

**Body Response :**

**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Definition
```
```
int32 id absence type ID
```
```
string name absence type name
```
```
string code absence type code
```
```
int32 type 1 – justified
2 – not justified
4 – holiday
8 – sick leave
int32 billingOnDays billing on days
```

**absenceName** string

**absenceCode** string

```
Unique absence id in the Comarch ERP Optima
```
```
Absence name in the Comarch ERP Optima
```
```
Absence code in the Comarch ERP Optima
```

```
http://{{API}}/api/EmployeeAbsenceLimits
```
#### AUTHORIZATION Bearer Token

**Token** <token>

#### PARAMS

**absenceID** int32

### GET http://YOUR_IP/api/EmployeeAbsenceLimits

#### The Function enable to get absence limits for employee

**Body Response :**

**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Definition
```
```
int32 absenceTypeId absence type id
```
```
string code absence code
```
```
string absenceLimitName absence name
```
```
int32 id absence limit id
```
```
int32 employeeId employee id
```
```
string employeeCode employee code
```
```
int32 limitYear limit of year
```
```
dateTime periodFrom period from
```
```
dateTime periodTo period to
```
```
dateTime validFrom valid from
```
```
decimal transfer transfer
```
```
decimal due due
```
```
decimal dueInTotal due in total
```
```
decimal left left
```

**absenceName** string

**absenceCode** string

**absenceLimitId** int32

**empoloyeeId** int32

**year** string

**periodFromFrom** string

**periodFromTo** string

**periodToFrom** string

**periodToTo** string

**validFrom** string

**validTo** string

#### AUTHORIZATION OAuth 2.0

This folder is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

```
http://{{API}}/api/Employments
```
```
Unique absence type in the Comarch ERP Optima
```
```
Absence limit name in the Comarch ERP Optima
```
```
Absence limit code in the Comarch ERP Optima
```
```
Unique absence limit name in the Comarch ERP Optima
```
```
Unique employee id in the Comarch ERP Optima
```
```
Year of absence limit
```
```
Start range of period from date
```
```
End range of period from date
```
```
Start range of period to date
```
```
End range of period to date
```
```
Valid From
```
```
Valid To
```
## Employments

### GET http://YOUR_IP/api/Employments

#### The Function enable to download history of date base for Employments List


#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### PARAMS

**id** Int32

**employeId** Int32

**dateOfFirstEmploymentStart** String

**dateOfFirstEmploymentEnd** String

#### y p y

**Body Response :**

**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Definition
```
```
Int32 id Employments Id
```
```
Int32 employeeId Employee Id
```
```
String employeeCode Employee code
```
```
String center Subordination center
```
```
String department Department/multi-tenure
```
```
String name First name
```
```
String name2 Second name
```
```
String surname Surname
```
```
String pesel PESEL number
```
```
(DateTime)String dateOfFirstEmployment DaTe of First Employment
```
```
(DateTime)String dateOfTermination Date of Termination
```
```
Int32 jobPositionId Job Position Id
```
```
String jobPositionName Job Position Name
```
```
Int32 costCenterId Cost Center Id
```
```
ID of record in Comarch ERP Optima
```
```
Unique Employee ID in Comarch ERP Optima
```
```
Start of employment date range
```
```
End of employment date range
```

**dateOfTerminationStart** String

**dateOfTerminationEnd** String

**jobPositionId** Int32

**costCenterId** Int32

**departmentId** Int32

**dateFromStart** String(yyyy-mm-dd)

**dateFromEnd** String(yyyy-mm-dd)

**dateToStart** String(yyyy-mm-dd)

**dateToEnd** String(yyyy-mm-dd)

**employee_id** Int32, Int32...

**databaseName** String

#### AUTHORIZATION OAuth 2.0

This folder is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

```
http:/{{API}}/api/Entities
```
```
Start of termination date range
```
```
End of termination date range
```
```
ID of job position in Comarch ERP Optima
```
```
ID of the employee center
```
```
ID of the employee department
```
```
The initial range of the employee's start date
```
```
The end range of the employee's start date
```
```
The initial range of the employee's end date
```
```
The end range of the employee's end date
```
```
LIst of employee id separated by comma
```
```
Database name (Default in Web.Config)
```
## Entities

### GET http://YOUR_IP/api/Entities

#### The Function enable to download history of date base for Entities


#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### PARAMS

**id** Int32

#### y

```
Get list of Entities
```
```
Get single Entity by ID
Get list of Entities by type
Get single Entity by code
Get single Entity by pesel
Get single Entity by VAT number
Get list of Entities by iinactive
```
**Body Response :**

**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Definition
```
```
Int32 id Entities Id
```
```
Int32 type Entities Type
```
```
String code Entities Code
```
```
String postalCode Postal Code
```
```
String pesel Pesel
```
```
String taxCountryCode Text Country Code
```
```
String taxId Tex Id
```
```
String emails Email
```
```
Int32 inactive Inactive
```
```
String countryName Country Name
```
```
String name1 First Descripiton
```
```
String name2 Secound Description
```
```
String name3 Third Description
```
```
String houseNumber House Number
```
```
Unique Entity id in the Comarch ERP Optima
```

**type** Int32

**code** String

**pesel** String

**vatNumber** String

**isoVatNumber** String

**inactive** Int32

**optima_id** Int32, Int32...

**optima_code** String, Int32, String....

**databaseName** String

```
http://{{API}}/api/EntitiesAttribute
```
```
Type of Entity of Comarch ERP Optima
```
```
Unique Entity code in the Comarch ERP Optima
```
```
Unique Entity Pesel number
```
```
Number used to identify taxpayers
```
```
ISO country codes used with vatNumber
```
```
Entity is inacvtie in Comarch ERP Optima 0 - Active, 1 - Inactive
```
```
List of values separated by comma
```
```
List of values separated by comma
```
```
Database name (Default in Web.Config)
```
### GET http://YOUR_IP/api/EntitiesAttribute

#### The Function enable to download history of date base for EntitiesAttribute

**Body Response :**

```
Type Name Definition
```
```
Int32 id Entities Attribute Id
```
```
Int32 attrDefinitionId Attribute Definition Id
```
```
Int32 attrDefinitionType Attribute Definition Type
```
```
String attrDefinitionCode Attribute Definition Code
```
```
Int32 entityType Entity Type
```
```
Int32 entityId Entity Id
```
```
String attrValue Attribute Value
```

#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### PARAMS

**attrDefinitionId** Int32

**entityType** Int32

**attrValue** String, String...

**entityId** Int32, Int32...

**databaseName** String

#### AUTHORIZATION OAuth 2.0

This folder is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

```
http://{{API}}/api/ExchangeRateTypes
```
**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

```
Id attribute definition
```
```
Type of Entity
```
```
List of value separated by comma
```
```
List of entity id separated by comma
```
```
Database name (Default in Web.Config)
```
## Exchange

### GET http:/YOUR_IP/api/ExchangeRateTypes

#### The Function enable to download types of courses

**Body Response :**


#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### PARAMS

**Id** Int32

#### AUTHORIZATION OAuth 2.0

This folder is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

```
http://{{API}}/api/RealizeInventories
```
**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Definition
```
```
Int32 id Course Id
```
```
String symbol Course Symbol
```
```
String name Course Name
```
```
Course Id
```
## Inventory

### PUT http://YOUR_IP/api/RealizeInventories

#### The Function enable to modificate for RealizeInventories

**Body Params Request :**

```
Type Name Definition Length Required
```
```
Int32 id Realize
Inventories Id
```
```
None Yes
```
```
Object companyData Company Data
obejct for Realize
I t i
```
```
None No
```

#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### AUTHORIZATION OAuth 2.0

This folder is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

```
http://{{API}}/api/InvoiceItems
```
**Company Data Fields :**

**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

```
Inventories
```
```
Type Name Definition Length Required
```
```
String companyName Chosen Company
Name for Realize
Inventories
```
```
None No
```
```
String databaseName Chosen
Database Name
for Realize
Inventories
```
```
None No
```
## Invoices

### GET http://YOUR_IP/api/InvoiceItems

#### The Function enable to download history of date base for InvoiceItems

**Body Response :**


#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### PARAMS

**id** Int32

**invoiceId** Int32

**refundedItemId** Int32

**saleDate** String (yyyy-mm-dd)

**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Definition
```
```
Int32 id Invoice Items Id
```
```
Int32 invoiceId Invoide Id
```
```
Int32 refundedItemId Refunded Item Id
```
```
String productName Product Description
```
```
Decimal singleNetPrice Single Net Price
```
```
Decimal quantity Quantity
```
```
(DateTime)String saleDate Sale Date
```
```
Decimal fullNetPrice Full Net Price
```
```
Decimal fullGrossPrice Full Gross Price
```
```
Decimal taxValue Tax Value
```
```
Int32 categoryId Category Id
```
```
String categoryCode Category Code
```
```
String categoryDescription Category Description
```
```
Invoice item id
```
```
invoice id
```
```
refunded item id
```
```
Invoide item sale date
```

**databaseName** String

```
http://{{API}}/api/Invoices
```
#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

```
Database name (Default in Web.Config)
```
### GET http://YOUR_IP/api/Invoices

#### The Function enable to download history of date base for Invoices

**Body Response :**

**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Definition
```
```
Int32 id Invoices Id
```
```
String invoiceNumber Invoice Number
```
```
(DateTime)String issueDate Issue Date
```
```
(DateTime)String dueDate Due Date
```
```
Decimal netValue Net Value
```
```
Decimal grossValue Gross Value
```
```
String currency Currency
```
```
Int32 customerType Customer Type
```
```
Int32 customerId Customer Id
```
```
Int32 cancelled Cancelled
```
```
Int32 bufor Bufor
```
```
Decimal currencyRate Currency Rate
```
```
(DateTime)String dateOfSale Date Od Sale
```
```
String invoicingPerson Invoicing Person
```

#### PARAMS

**id** Int32

**invoiceNumber** String

**issueDateStart** String (yyyy-mm-dd)

**issueDateEnd** String (yyyy-mm-dd)

**customerType** Int32

**customerId** Int32

**cancelled** Int32

**bufor** Int32

**dateOfSaleStart** String (yyyy-mm-dd)

**dateOfSaleEnd** String (yyyy-mm-dd)

**refundedInvoiceId** Int32

**documentTypeId** Int32

**settlementStatus1** Int32

**settlementStatus2** Int32

**vatInvoiceId** Int32

**customer_id** Int32, Int32...

**databaseName** String

```
Invoice id
```
```
Invoice number
```
```
Start date range of issue date
```
```
End date range of issue date
```
```
Customer type
```
```
Customer id
```
```
Candelled
```
```
Bufor status
```
```
Start date range date of sale
```
```
End date range date of sale
```
```
Refunded invoice id
```
```
Document type id
```
```
Settlement first status
```
```
Settlement secound status
```
```
Vat invoide Id
```
```
List of customers id separated by comma
```

```
g
```
#### AUTHORIZATION OAuth 2.0

This folder is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

```
http://{{API}}/api/Barcodes
```
#### AUTHORIZATION Bearer Token

**Token** <token>

#### PARAMS

**itemId** Int32

**ean** Int32

```
Database name (Default in Web.Config)
```
## Items

### GET http://YOUR_IP/api/Barcodes

#### The Function enable to download items barcodes

**Body Response :**

**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Definition
```
```
Int32 itemId Item ID
```
```
Int32 ean ean code
```
```
Int32 isDefault is/is not default
```
```
String unit unit
```
### GET http://YOUR_IP/api/Items


```
http://{{API}}/api/Items
```
#### The Function enable to download history of date base for Items

```
Get list of items
Get single item by ID (ID is unique)
Get single item by code (code is unique)
Get single item by barcode (barcode is unique)
Get items with specific price or all prices (priceNumber parameter)
Get items by type (type parameter)
```
**Body Response :**

**Company Data Fields :**

**Price for Body :**

```
Type Name Definition
```
```
Int32 id Item Id
```
```
Int32 type Item Type
```
```
Int32 inactive Inactive
```
```
String code Item Code
```
```
String name Item Name
```
```
String manufacturerCode Item Manufacturer Code
```
```
Decimal vatRate Vat Rate
```
```
Int32 vatRateFlag Vat Rate Flag Settings :
0 -VAT rate on the goods,
1 - exempt from tax,
2 - taxed,
4 - Is not subject to.
```
```
String unit unit
```
```
String barcode Barcode
```
```
String description Description
```
```
Type Name Definition Length Required
```
```
String companyName Chosen Company
Name for Item
```
```
None No
```
```
String databaseName Chosen
Database Name
for Item
```
```
None No
```

#### AUTHORIZATION Bearer Token

**Token** <token>

#### PARAMS

**id** Int32

**code** String

**barcode** String

**priceNumber** Int32

**supplierCode** String

**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Definition
```
```
String optimaItemId Optima Item Id
```
```
String itemBarcode Item Bar Code
```
```
Int32 number Number
```
```
Int32 type Type
```
```
String name Name
```
```
Decimal value Value
```
```
String currency Currency
```
```
String vatRate Vat Rate
```
```
String unit Unit
```
```
String itemCode Item Code
```
```
String error Error
```
```
Unique item identifier in the Comarch ERP Optima
```
```
Unique item code in the Comarch ERP Optima
```
```
Unique item barcode in the Comarch ERP Optima
```
```
0 - All prices (default)
```
```
Supplier code
```

**offset** Int32

**limit** Int32

**c_dateFrom** String (yyyy-mm-dd)

**c_dateTo** String (yyyy-mm-dd)

**u_dateFrom** String (yyyy-mm-dd)

**u_dateFrom** String (yyyy-mm-dd)

**databaseName** String

**get_attribute** true

**type** Int32

```
http://{{API}}/api/ItemsGroups
```
```
Skip the numbers of results
```
```
Limit the numbers of results (defualt 2000)
```
```
Start range of created date
```
```
End range of created date
```
```
Start range of updated date
```
```
End range of updated date
```
```
Database name (Default in Web.Config)
```
```
Should get an attibutes [value true/false]
```
```
Type of item
```
### GET http://YOUR_IP/api/ItemsGroups

#### The Function enable to download history of date base for ItemsGroups

**Body Response :**

**Http Correct Answer :**

**200 Ok**

```
Type Name Definition
```
```
Int32 id Items Group Id
```
```
Int32 gidNumber Gid Number
```
```
Int32 parentGidNumber Parent Gid Number
```
```
String code Item Group Code
```
```
String name Item Group Description
```

#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### PARAMS

**databaseName** String

```
http://{{API}}/api/Items
```
**Http Possible Answer :**

**401 Unauthorized**

```
Database name (Default in Web.Config)
```
### POST http://YOUR_IP/api/Items

#### The Function enable to Create Item

**Body Params Request :**

**Company Data Fields :**

```
Type Name Definition Length Required
```
```
Int32 id Unique item ID in
Optima. We get
after adding
```
```
None No
```
```
Int32 type 0 - Service, 1 -
Item
```
```
None Yes
```
```
Int32 inactive 1 - Inactive, 0 -
Active
```
```
None No
```
```
String code Unique item
Code in Optima
```
```
1 - 50 Yes
```
```
String name Description 1 - 255 Yes
```
```
String manufacturerCod
e
```
```
Manufacturer
Code
```
```
0-50 No
```
```
Decimal vatRate Vat Rate None Yes
```
```
Int32 vatRateFlag Vat Rate Flag
Settings :
0 -VAT rate on
the goods,
1 - exempt from
t
```
```
None No
```

#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### Body raw (json)

**Price For Body :**

**Attribute For Body :**

**Http Correct Answer :**

**201 Created**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Definition Length Required
```
```
String companyName Chosen Company
Name for Item
```
```
None No
```
```
String databaseName Chosen
Database Name
for Item
```
```
None No
```
```
Type Name Definition Length Required
```
```
String optimaItemId Optima Item Id None No
```
```
String itemBarcode Item Bar Code None No
```
```
Int32 number Number of Price None No
```
```
Int32 type Type Price None No
```
```
String name Description None No
```
```
Decimal value Value None No
```
```
String currency Currency None No
```
```
String vatRate Vat Rate None No
```
```
String unit Unit None No
```
```
String itemCode Item Code None No
```
```
String error Error None No
```
```
Type Name Definition Required
```
```
string code Code of attribute Yest
```
```
string value Value of attribute No
```

```
http://{{API}}/api/Items/{id}
```
```
json
```
```
{
"type": 1 ,
"inactive": 0 ,
"code": "NEW_ITEM_ELTES",
"name": "Api new item",
"manufacturerCode": "NEW_12_1",
"vatRate": 23.00,
"unit": "szt.",
"barcode": "",
"description": "",
"prices": [
{
"number": 2 ,
"type": 1 ,
"name": "hurtowa 1",
"value": 64.0000
},
{
"number": 3 ,
"type": 1 ,
"name": "hurtowa 2",
"value": 66.0000
},
{
"number": 4 ,
"type": 1 ,
"name": "hurtowa 3",
"value": 68.0000
},
{
"number": 5 ,
"type": 2 ,
"name": "detaliczna",
"value": 79.0000
}
],
"supplierCode": "NEW-ADA",
"catalogNumber": "NEW-DADA",
"package_deposit": 0 ,
"product": 0
"gettingElementsForFSPA" : 0
}
```
### PUT http://YOUR_IP/api/Items/{id}

**The Function enable to modification for Items**


**Company Data Fields :**

**Price For Body :**

```
Type Name Definition Length Required
```
```
Int32 id Unique item ID in
Optima. We get
after adding
```
```
None No
```
```
Int32 type 0 - Service, 1 -
Item
```
```
None Yes
```
```
Int32 inactive 1 - Inactive, 0 -
Active
```
```
None No
```
```
String code Unique item
Code in Optima
```
```
1 - 50 Yes
```
```
String name Description 1 - 255 Yes
```
```
String manufacturerCod
e
```
```
Manufacturer
Code
```
```
0-50 No
```
```
Decimal vatRate Vat Rate None Yes
```
```
String unit Unit 1 - 20 Yes
```
```
Int32 vatRateFlag Vat Rate Flag
Settings :
0 -VAT rate on
the goods,
```
```
None No
```
```
Type Name Definition Length Required
```
```
String companyName Chosen Company
Name for Item
```
```
None No
```
```
String databaseName Chosen
Database Name
for Item
```
```
None No
```

#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### Body raw (json)

* _Attribute For Body :__*_

**Http Correct Answer :**

**201 Created**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Definition Length Required
```
```
String optimaItemId Optima Item Id None No
```
```
String itemBarcode Item Bar Code None No
```
```
Int32 number Number of Price None No
```
```
Int32 type Type Price None No
```
```
String name Description None No
```
```
Decimal value Value None No
```
```
String currency Currency None No
```
```
String vatRate Vat Rate None No
```
```
String unit Unit None No
```
```
String itemCode Item Code None No
```
```
String error Error None No
```
```
Type Name Definition Required
```
```
int32 id Attribute id No
```
```
string value Attribute value No
```
```
json
```
```
{
"type": 1 ,
"inactive": 0 ,
"code": "NEW_ITEM_ELTES",
"name": "Api new item edited",
"manufacturerCode": "NEW_12_1)E",
```

```
http://{{API}}/api/Items/{id}
```
#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### PARAMS

**companyName** string

```
"vatRate": 23.00,
"unit": "szt.",
"barcode": "",
"description": "edited item api",
"prices": [
{
"number": 2 ,
"type": 1 ,
"name": "hurtowa 1",
"value": 100.0000
},
{
"number": 3 ,
"type": 1 ,
"name": "hurtowa 2",
"value": 200.0000
},
{
"number": 4 ,
"type": 1 ,
"name": "hurtowa 3",
"value": 300.0000
},
{
"number": 5 ,
"type": 2 ,
"name": "detaliczna",
"value": 500.0000
}
],
"supplierCode": "NEW-ADA-E",
"catalogNumber": "CATALOG_1",
"package_deposit": 0 ,
"product": 0
}
```
### DELETE http://YOUR_IP/api/Items/{id}

```
Company name (Default in Web.Config)
```
### GET http://YOUR_IP/api/ItemsAttribute


```
http://{{API}}/api/ItemsAttribute
```
#### AUTHORIZATION Bearer Token

**Token** <token>

#### PARAMS

**id** Int32

#### The Function enable to get item attributes

```
Get list of items
Get single item by ID (ID is unique)
Get single item by code (code is unique)
Get single item by barcode (barcode is unique)
Get items with specific price or all prices (priceNumber parameter)
```
**Body Response :**

**Company Data Fields :**

**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Definition
```
```
Int32 id ID
```
```
int32 attributeId Attribute Id
```
```
int32 objectId Item Id
```
```
string value Attribute value
```
```
int32 copyToTransaction Copy to transaction [value 0,1]
```
```
int32 copyToDelivery Copy to delivery [value 0,1]
```
```
Type Name Definition Length Required
```
```
String companyName Chosen Company
Name for Item
```
```
None No
```
```
String databaseName Chosen
Database Name
for Item
```
```
None No
```
```
Unique attribute identifier in the Comarch ERP Optima
```

**itemId** Int32

**attributeId** Int32

**offset** Int32

**limit** Int32

**databaseName** String

```
http://{{API}}/api/ItemsAttribute
```
```
Unique item id in the comarch ERP Optima
```
```
Unique attribute id in the comarch ERP Optima
```
```
Skip the numbers of results
```
```
Take number of results
```
```
Database name (Default in Web.Config)
```
### POST http://YOUR_IP/api/ItemsAttribute

#### The Function enable to add new item attributes

**Body Request:**

**Company Data Fields :**

**Http Correct Answer :**

```
Type Name Definition
```
```
Int32 id ID
```
```
int32 attributeId Attribute Id
```
```
int32 objectId Item Id
```
```
string value Attribute value
```
```
int32 copyToTransaction Copy to transaction [value 0,1]
```
```
int32 copyToDelivery Copy to delivery [value 0,1]
```
```
Type Name Definition Length Required
```
```
String companyName Chosen Company
Name for Item
```
```
None No
```
```
String databaseName Chosen
Database Name
for Item
```
```
None No
```

#### AUTHORIZATION Bearer Token

**Token** {{BearerToken}}

#### Body raw (json)

```
http://{{API}}/api/ItemsAttribute
```
**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

```
json
```
```
{
"objectId" : 3 ,
"attributeId" : 4 ,
"value" : "test",
"copyToTransaction" : 1 ,
"copyToDelivery" : 1
}
```
### PUT http://YOUR_IP/api/ItemsAttribute

#### The Function enable to update item attribute

**Body Request:**

**Company Data Fields :**

```
Type Name Definition
```
```
Int32 id ID
```
```
int32 attributeId Attribute Id
```
```
int32 objectId Item Id
```
```
string value Attribute value
```
```
int32 copyToTransaction Copy to transaction [value 0,1]
```
```
int32 copyToDelivery Copy to delivery [value 0,1]
```

#### AUTHORIZATION Bearer Token

**Token** {{BearerToken}}

```
http://{{API}}/api/Recipes
```
**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Definition Length Required
```
```
String companyName Chosen Company
Name for Item
```
```
None No
```
```
String databaseName Chosen
Database Name
for Item
```
```
None No
```
### GET http://YOUR_IP/api/Recipes

StartFragment

#### The Function enable to download recipes

**Body Response :**


#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### PARAMS

**id** int

**itemId** int

**offset** int

**limit** int

**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

EndFragment

```
Type Name Definition
```
```
Int32 id recipe ID
```
```
Int32 itemId item ID
```
```
String code recipe code
```
```
String name recipe name
```
```
Int32 defaultRecipe default recipe
```
```
Decimal quantity Quantity
```
```
String unit unit
```
```
String description description
```
```
DateTime created date of creation
```
```
DateTime updated date of update
```
```
String companyData company data
```
```
String categoryCode Category Code
```
```
String categoryDescription Category Description
```
```
Unique recipes identifier in the Comarch ERP Optima
```
```
Unique item identifier in the Comarch ERP Optima
```
```
Offset
```
```
Li i
```

**c_dateFrom** string

**c_dateTo** string

**u_dateFrom** string

**u_dateTo** string

**databaseName** string

```
http://{{API}}/api/Recipes
```
```
Limit
```
```
Create date from
```
```
Create date to
```
```
Update date from
```
```
Update date to
```
```
Database name (Default in Web.Config)
```
### POST http://YOUR_IP/api/Recipes

#### The Function enable to Create Recipes

**Body Params Request :**

**Company Data Fields :**

```
Type Name Definition Length Required
```
```
Int32 itemId Unique item ID in
Optima.
```
```
None Yes
```
```
String code Unique item
recipse in Optima
```
```
1 - 50 Yes
```
```
String name Description 1 - 255 No
```
```
Int32 defaultRecipe default recipe:
0 - No
1 - Yes
```
```
None No
```
```
Decimal quantity Quantity None Yes
```
```
String unit Unit None Yes
```
```
String description Description Max Yes
```
```
Object companyData Company Data
obejct for Item
```
```
None No
```

#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

```
http://{{API}}/api/Recipes
```
**Http Correct Answer :**

**201 Created**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Definition Length Required
```
```
String companyName Chosen Company
Name for Item
```
```
None No
```
```
String databaseName Chosen
Database Name
for Item
```
```
None No
```
### PUT http://YOUR_IP/api/Recipes

#### The Function enable to Modyficate Recipes

**Body Params Request :**

```
Type Name Definition Length Required
```
```
Int32 id Unique recipe ID
in Optima
```
```
None Yes
```
```
Int32 itemId Unique item ID in
Optima.
```
```
None Yes
```
```
String code Unique item
recipse in Optima
```
```
1 - 50 Yes
```
```
String name Description 1 - 255 No
```
```
Int32 defaultRecipe default recipe:
0 - No
1 - Yes
```
```
None No
```
```
Decimal quantity Quantity None Yes
```
```
String unit Unit None Yes
```
```
String description Description Max Yes
```
```
Object companyData Company Data
obejct for Item
```
```
None No
```

#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### Body raw (json)

```
http://{{API}}/api/Recipes/{id}
```
#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

```
http://{{API}}/api/RecipeElements
```
**Company Data Fields :**

**Http Correct Answer :**

**201 Created**

**Http Possible Answer :**

**401 Unauthorized**

```
obejct for Item
```
```
Type Name Definition Length Required
```
```
String companyName Chosen Company
Name for Item
```
```
None No
```
```
String databaseName Chosen
Database Name
for Item
```
```
None No
```
```
json
```
```
{
"itemId" : 20 ,
"code" : "Test2",
"quantity": 1.00,
"unit": "SZT",
"description": "Test opis2"
}
```
### DELETE http://YOUR_IP/api/Recipes

### GET http://YOUR_IP/api/RecipeElements


#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### PARAMS

**id** int

**recipeId** int

**databaseName** string

```
http://{{API}}/api/RecipeElements
```
StartFragment

#### The Function enable to download history of date base for InvoiceItems

**Body Response :**

**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

EndFragment

```
Type Name Definition
```
```
Int32 id ID
```
```
Int32 recipeId recipe ID
```
```
Int32 itemId item ID
```
```
String warehouseId warehouse ID
```
```
Int32 quantity quantity
```
```
String unit unit
```
```
String companyData company data
```
```
Unique recipe elements identifier in the Comarch ERP Optima
```
```
Unique recipe identifier in the Comarch ERP Optima
```
```
Database name (Default in Web.Config)
```
### POST http://YOUR_IP/api/RecipeElements


#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### Body raw (json)

#### The Function enable to Create Recipe Elements

**Body Params Request :**

**Company Data Fields :**

**Http Correct Answer :**

**201 Created**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Definition Length Required
```
```
Int32 RecipeId Unique item ID in
Optima.
```
```
None Yes
```
```
Int32 itemId Item Id None Yes
```
```
Int32 warehouseId Warehouse Id None Yes
```
```
Decimal quantity Quantity None Yes
```
```
String unit Unit None Yes
```
```
Object companyData Company Data
obejct for Item
```
```
None No
```
```
Type Name Definition Length Required
```
```
String companyName Chosen Company
Name for Item
```
```
None No
```
```
String databaseName Chosen
Database Name
for Item
```
```
None No
```
```
json
```
```
{
"RecipeId" : 2 ,
"itemId" : 20 ,
"warehouseId": 1 ,
"code": "Test",
"quantity" : 1.00,
"unit" : "SZT",
"description" : "Test"
}
```

```
http://{{API}}/api/RecipeElements
```
#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2 0 from collectionAPI Comarch ERP Optima v Professional / Multi Company 2 0

### PUT http://YOUR_IP/api/RecipeElements

#### The Function enable to Create Recipe Elements

**Body Params Request :**

**Company Data Fields :**

**Http Correct Answer :**

**201 Created**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Definition Length Required
```
```
Int32 id Unique recipe
element ID in
Optima
```
```
None No
```
```
Int32 RecipeId Unique item ID in
Optima.
```
```
None Yes
```
```
String code Unique item
recipse in Optima
```
```
1 - 50 Yes
```
```
String name Description 1 - 255 No
```
```
Int32 defaultRecipe default recipe:
0 - No
1 - Yes
```
```
None No
```
```
Decimal quantity Quantity None Yes
```
```
String unit Unit None Yes
```
```
Object companyData Company Data
obejct for Item
```
```
None
```
```
Type Name Definition Length Required
```
```
String companyName Chosen Company
Name for Item
```
```
None No
```
```
String databaseName Chosen
Database Name
for Item
```
```
None No
```

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### Body raw (json)

```
http://{{API}}/api/RecipeElements/{Id}
```
#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

```
http://{{API}}/api/GroupProducts
```
```
json
```
```
{
"id": 4 ,
"RecipeId" : 2 ,
"itemId" : 20 ,
"warehouseId": 1 ,
"code": "Test",
"quantity" : 1.00,
"unit" : "SZT",
"description" : "Test opis"
}
```
### DELETE http://YOUR_IP/api/RecipeElements

### GET http://YOUR_IP/api/GroupProducts

StartFragment

#### The Function enable to download groups of products

**Body Response :**

**List of products :**

```
Type Name Definition
```
```
Int32 groupId group ID
```
```
String groupCode group code
```
```
String groupPath group path
```
```
List products list of products
```
```
Type Name Definition
```

#### AUTHORIZATION Bearer Token

#### PARAMS

**groupId** int

**databaseName** String

```
http://{{API}}/api/ItemsCountryVatRate?itemId=int32
```
**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

EndFragment

```
String productCode product code
```
```
String productName product name
```
```
Unique group identifier in the Comarch ERP Optima
```
```
Database name (Default in Web.Config)
```
### GET http://YOUR_IP/api/ItemsCountryVatRate

StartFragment

#### The Function enable to download Items vat rate and country code

**Body Response :**

**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

EndFragment

```
Type Name Definition
```
```
Int32 itemId Item Id
```
```
String countryCode country code
```
```
Decimal vatRate items vat rate
```

#### AUTHORIZATION Bearer Token

**Token** <token>

#### PARAMS

**itemId** int32

#### AUTHORIZATION OAuth 2.0

This folder is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

```
http://{{API}}/api/Journal
```
```
Item id
```
## Journal

### POST http://YOUR_IP/api/Journal

#### Adding a JournalEntry document to Optima. After adding, we get the ID and Accounting Id of the

#### newly created document

**Body Params Request :**

```
Type Name Description Length Required
```
```
String journal The journal name 0 - 50 Yes
```
```
String documentNumbe
r
```
```
full number 0 - 256 Yes
```
```
(DateTime)String postingDate posting date (yyyy-MM-dd) Yes
```
```
(DateTime)String operationDate operation date (yyyy-MM-dd) Yes
```
```
(DateTime)String issueDate issue date (yyyy-MM-dd) Yes
```
```
String categoryDescript
ion
```
```
category
description
```
```
0-50 No
```
```
String voucherType voucher type 0-256 No
```
```
Decimal businessTransact
ionAmount
```
```
business
transaction
amount
```
```
None No
```
```
List elements List Of Jurnalitem None No
```

#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### Body raw (json)

**Company Data Fields :**

**Journal Items for Body :**

**Http Correct Answer :**

**201 Created**

**Http Possible Answer :**

**401 Unauthorized**

```
Object companyData Company Data
obejct for Journal
```
```
None No
```
```
Type Name Definition Length Required
```
```
String companyName Chosen Company
Name for Journal
```
```
None No
```
```
String databaseName Chosen
Database Name
for Journal
```
```
None No
```
```
Type Name Description Length Required
```
```
String customerCode customer /
vendor
```
```
1 - 20 No
```
```
String categoryCode Category Code 1 - 20 No
```
```
String categoryDescript
ion
```
```
Category
Description
```
```
1 - 50 No
```
```
String accountWn account in the
current
accounting
period Format:
AccId: 011.2
```
```
1 - 50 No
```
```
String accountMa account in the
current
accounting
period Format:
AccId: 461.1
```
```
1 - 50 No
```
```
Decimal amount Amount None No
```
```
String currency Currency ISO
code. If empty
set default PLN
```
```
None No
```

```
json
```
{
"accountingId": "5/20/BANK",
"id": 11 ,
"journal": "BANK",
"postingDate": "2023-02-09T12:00:00",
"operationDate": "2023-02-09T12:00:00",
"issueDate": "2023-02-09T12:00:00",
"documentNumber": "GG/1/2021",
"elements": [
{
"id": 0 ,
"customerCode": null,
"categoryCode": "ODSET. DLA URZĘDÓW",
"categoryDescription": "test GG",
"accountWn": null,
"accountMa": "015.3",
"amount": 1000.0,
"currency": null,
"exchangeRateDate": "0001-01-01T00:00:00",
"generatingPayment": false,
"paymentMethod": null,
"deadlineDate": "0001-01-01T00:00:00"
},
{
"id": 0 ,
"customerCode": null,
"categoryCode": null,
"categoryDescription": null,
"accountWn": "015.3",
"accountMa": null,
"amount": 1000.0,
"currency": null,
"exchangeRateDate": "0001-01-01T00:00:00",
"generatingPayment": false,
"paymentMethod": null,
"deadlineDate": "0001-01-01T00:00:00"
},
{
"id": 0 ,
"customerCode": null,
"categoryCode": null,
"categoryDescription": null,
"accountWn": null,
"accountMa": "203.BIGGUN.EUR",
"amount": 1000.0,
"currency": "EUR",
"exchangeRateDate": "2020-01-12T12:00:00",
"generatingPayment": false,
"paymentMethod": null,
"deadlineDate": "0001-01-01T00:00:00"
},
{
"id": 0 ,
"customerCode": null,
"categoryCode": null,
"categoryDescription": null,
"accountWn": "203.BIGGUN.EUR",


#### AUTHORIZATION OAuth 2.0

This folder is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

```
http://{{API}}/api/OcrFile
```
```
"accountMa": null,
"amount": 1000.0,
"currency": "EUR",
```
```
"exchangeRateDate": "2020-01-12T12:00:00",
"generatingPayment": true,
"paymentMethod": "gotówka",
"deadlineDate": "2022-01-12T12:00:00"
}
]
}
```
## OcrFile

### POST http://YOUR_IP/api/OcrFile

#### The Function enable to Create Ocr File

**Body Params Request :**

**Company Data Fields :**

```
Type Name Description Length Required
```
```
Int32 docId Vat document Id None Yes
```
```
Int32 docType Type of adding
document[
Values 0 -
VatRegister,
2 -
AdditionalRecord
]
```
```
None No
```
```
String fileName File name without
extension
```
```
50 Yes
```
```
String fileExtension File extension 5 Yes
```
```
Byte[] fileBinary Binary array of
sending file
(jpg,png, pdf)
```
- Yes

```
Object companyData Company Data
obejct for Ocr
File
```
```
None No
```

#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### Body raw (json)

#### AUTHORIZATION OAuth 2.0

This folder is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

```
http://{{API}}/api/Operators
```
**Http Correct Answer :**

**201 Created**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Definition Length Required
```
```
String companyName Chosen Company
Name for Ocr File
```
```
None No
```
```
String databaseName Chosen
Database Name
for Ocr File
```
```
None No
```
```
json
```
```
{
"docId": 1617 ,
"fileName": "FS_2021_PR",
"fileExtension": "jpg",
"fileBinary": "binarna wartość pliku pdf, zdjecia itp"
}
```
## Operators

### GET http://YOUR_IP/api/Operators

#### The Function enable to get operators from Comarch ERP Optima

**Body Params Request :**


#### AUTHORIZATION Bearer Token

**Token** <token>

#### PARAMS

**id** Int32

**u_DateFrom** string(yyyy-MM-dd HH:mm:ss)

**u_DateTo** string(yyyy-MM-dd HH:mm:ss)

**databaseName** String

#### Body raw (json)

**Http Correct Answer :**

**200 OK**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Description
```
```
Int32 id Operator ID
```
```
String code Operator code
```
```
String name Operator name
```
```
String email Operator email
```
```
String phone Operator phone
```
```
DateTime updated DataTime of last modification.
```
```
Operator ID
```
```
Start range of modification date
```
```
End range of modification date
```
```
Database name (Default in Web.Config)
```
```
json
```
```
{
"docId": 1617 ,
"fileName": "FS_2021_PR",
"fileExtension": "jpg",
"fil Bi " "bi t ść lik df dj i it "
```

#### AUTHORIZATION OAuth 2.0

This folder is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

```
http://{{API}}/api/PaycheckComponents
```
#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### PARAMS

```
"fileBinary": "binarna wartość pliku pdf, zdjecia itp"
}
```
## PayCheck

### GET http://YOUR_IP/api/PaycheckComponents

#### The Function enable to download history of date base for Paycheck Components

**Body Response :**

**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Definition
```
```
Int32 id Paycheck Components
```
```
Int32 paycheckId Paycheck Id
```
```
Int32 paycheckItemId Paycheck Item Id
```
```
Int32 employeeId Employee Id
```
```
Int32 paycheckTypeId Paycheck Type Id
```
```
String paycheckTypeName Paycheck Type Description
```
```
Decimal value Value
```
```
(DateTime)String createDate Create Date
```
```
(DateTime)String dateFrom Date From
```
```
(DateTime)String dateTo Date To
```

**id** Int32

**paycheckId** Int32

**paycheckItemId** Int32

**employeeId** Int32

**createDate** String (yyyy-mm-dd)

**dateFrom** String (yyyy-mm-dd)

**dateTo** String (yyyy-mm-dd)

**databaseName** String

```
http://{{API}}/api/Paychecks
```
```
Unique Paycheck Component id in the Comarch ERP Optima
```
```
Unique Paycheck id in the Comarch ERP Optima
```
```
Unique Paycheck Item id in the Comarch ERP Optima
```
```
Unique Employee id in the Comarch ERP Optima
```
```
Date of create paycheck component
```
```
Paycheck component date from
```
```
Paycheck component date to
```
```
Database name (Default in Web.Config)
```
### GET http://YOUR_IP/api/Paychecks

#### The Function enable to download history of date base for Paychecks

**Body Response :**


#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### PARAMS

**id** iNT32

**employeeId** Int32

**documentTypeId** Int32

**createDateStart** String (yyyy-mm-dd)

**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Definition
```
```
Int32 id Paycheck Id
```
```
String number Paycheck Number
```
```
Int32 employeeId Empoyee Id
```
```
Int32 documentTypeId Document Type Id
```
```
String documentTypeSignature Document Type Signature
```
```
String documentTypeName Document Type Name
```
```
(DateTime)String createDate Create Date
```
```
Decimal netValue Net Value
```
```
Decimal grossValue Gross Value
```
```
Decimal currencyRate Currency Rate
```
```
Int32 month Month
```
```
Int32 year Year
```
```
(DateTime)String dateFrom Date From
```
```
(DateTime)String dateTo Date To
```
```
Unique Paycheck id in the Comarch ERP Optima
```
```
Unique Employee id in the Comarch ERP Optima
```
```
Unique Document type id in the Comarch ERP Optima
```
```
Start date range of create paycheck
```

**createDateEnd** String (yyyy-mm-dd)

**month** Int32

**year** Int32

**dateFromStart** String (yyyy-mm-dd)

**dateFromEnd** String (yyyy-mm-dd)

**dateToStart** String (yyyy-mm-dd)

**dateToEnd** String (yyyy-mm-dd)

**month_list** Int32, Int32...

**year_list** Int32, Int32...

**databaseName** String

```
http://{{API}}/api/PaycheckItems
```
```
End date range of create paycheck
```
```
Month of paycheck
```
```
Year of paycheck
```
```
Start date range Paycheck date from
```
```
End date range Paycheck date from
```
```
Start date range Paycheck date to
```
```
End date range Paycheck date to
```
```
List of month separated by comma
```
```
List of years separated by comma
```
```
Database name (Default in Web.Config)
```
### GET http://YOUR_IP/api/PaycheckItems

#### The Function enable to download history of date base for Paycheck Items

**Body Response :**


#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### PARAMS

**id** Int32

**paycheckId** Int32

**employeeId** Int32

**paycheckTypeId** Int32

**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Definition
```
```
Int32 id PaycheckI tems Id
```
```
Int32 paycheckId Paycheck Id
```
```
Int32 employeeId Employee Id
```
```
Int32 paycheckTypeId Paycheck Type Id
```
```
String paycheckTypeName Paycheck Type Name
```
```
String paycheckItemName Paycheck Item Name
```
```
Int32 categoryId Category Id
```
```
String categoryName Category Name
```
```
String currency Currency
```
```
Decimal grossValue Gross Value
```
```
Decimal grossValueCurrency Gross Value Currency
```
```
Decimal netValue Net Value
```
```
Decimal netValueCurrency Net Value Currency
```
```
Decimal basePension Base Pension
```
```
Unique Paycheck Item id in the Comarch ERP Optima
```
```
Unique Paycheck id in the Comarch ERP Optima
```
```
Unique Employee id in the Comarch ERP Optima
```
```
Unique Paycheck type id in the Comarch ERP Optima
```

**databaseName** String

#### AUTHORIZATION OAuth 2.0

This folder is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

```
http://{{API}}/api/PaymentMethods
```
#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

```
Database name (Default in Web.Config)
```
## Payments

### GET http://YOUR_IP/api/PaymentMethods

#### The Function enable to download history of date base for Payment Methods

```
Get list of payment methods
Get single payment method by id
Get single payment method by name
```
You can combine parameters

**Body Response :**

**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Description
```
```
Int32 id Payment Id
```
```
String name Payment Description
```
```
Int32 inactive Inactive
```
```
Int32 type 1 - cash,
2 - bank transfer,
3 - credit card
```

#### PARAMS

**id** Int32

**name** String

**databaseName** String

```
http://{{API}}/api/Payments?paymentId=Int32
```
```
Unique payment method id in the Comarch ERP Optima
```
```
Payment method name in the Comarch ERP Optima
```
```
Database name (Default in Web.Config)
```
### GET http://YOUR_IP/api/Payments

#### This method is used to add payments in Comarch ERP Optima. The payment is later used to settle

#### the documents, so write down the value of the paymentFullNumber field.

**Company Data Fields :**

```
Type Name Description Length Required
```
```
Int32 id Unique
settlement ID in
Optima. We get
this field after
adding
```
```
None No
```
```
String foreignNumber Foreign payment
number from the
external system
```
```
0-256 No
```
```
String paymentFullNum
ber
```
```
Unique payment
number in
Optima. We get
this field after
adding (only get)
```
```
0-30 No
```
```
String currency Currency of
payment
```
```
0-3 Yes
```
```
(DateTime)String date Date of payment
"2021-03-03"
```
```
(yyyy-MM-dd) Yes
```
```
Decimal grossValue Value of payment None Yes
```
```
Int32 type 1 revenue, -1 None Yes
```

#### AUTHORIZATION Bearer Token

**Token** <token>

#### PARAMS

**customerId** Int32

**paymentId** Int32

**type** Int32

**dateFrom** String(yyyy-mm-dd)

**dateTo** String(yyyy-mm-dd)

**databaseName** String

**docId** Int32

**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Definition Length Required
```
```
String companyName Chosen Company
Name for
Payments
```
```
None No
```
```
String databaseName Chosen
Database Name
for Payments
```
```
None No
```
```
Payment customer id
```
```
Payment id
```
```
Payment type
```
```
Payment date from
```
```
Payment date to
```
```
Database name (Default in Web.Config)
```
```
Document Id
```
### POST http://YOUR_IP/api/Payments


```
http://{{API}}/api/Payments
```
#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

This method is used to add payments in Comarch ERP Optima. The payment is later used to settle the documents, so
write down the value of the paymentFullNumber field.

**Body Params Request :**

**Company Data Fields :**

**Http Correct Answer :**

**201 Created**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Description Length Required
```
```
Int32 id Unique
settlement ID in
Optima. We get
this field after
adding
```
```
None No
```
```
String paymentFullNum
ber
```
```
Unique payment
number in
Optima. We get
this field after
adding (only get)
```
```
0-30 No
```
```
String currency Currency of
payment
```
```
0-3 No
```
```
String foreignNumber Foreign payment
number from the
external system
```
```
0-256 Yes
```
```
(DateTime)String date Date of payment
e.g. "2021-03-
03"
```
```
(yyyy-MM-dd) Yes
```
```
Decimal grossValue Value of payment None Yes
```
```
Type Name Definition Length Required
```
```
String companyName Chosen Company
Name for
Payments
```
```
None No
```
```
String databaseName Chosen
Database Name
for Payments
```
```
None No
```

#### Body raw (json)

```
http://{{API}}/api/PaymentsSettlement
```
```
json
```
```
{
"paymentMethodId": 1 ,
"foreignNumber":"TEST/2021/1",
"date":"2021-03-03",
"grossValue": 250 ,
"type": 1 ,
"description": "desc",
"currency":"PLN",
"customerId": 3
}
```
### POST http://YOUR_IP/api/PaymentsSettlement

#### This method is used to settle payments in the Comarch ERP Optima system by linking payments

#### with payouts. It uses full document numbers for identification in the system.

**Body Params Request :**

**Company Data Fields :**

```
Type Name Definition Length Required
```
```
Int32 id Payments
Settlement Id
```
```
None No
```
```
String paymentFullNum
ber
```
```
Payment Full
Number
```
```
0-30 Yes
```
```
String payOutFullNumb
er
```
```
Pay Out Full
Number
```
```
0-30 Yes
```
```
String error Error None No
```
```
Object companyData Company Data
obejct for
Payment
Settlement
```
```
None No
```
```
Type Name Definition Length Required
```
```
String companyName Chosen Company
Name for
Settlement
```
```
None No
```
```
String databaseName Chosen
Database Name
```
```
None No
```

#### AUTHORIZATION Bearer Token

**Token** <token>

#### Body raw (json)

```
http://{{API}}/api/Payments/{Id}
```
**Http Correct Answer :**

**201 Created**

**Http Possible Answer :**

**401 Unauthorized**

```
for Payments
Settlement
```
```
json
```
```
[
{
"id": 0 ,
"paymentFullNumber": "PAY1234567890",
"payOutFullNumber": "POUT987654321",
"error": null,
"companyData": {
"companyName": "FIRMA_DEMO",
"databaseName": "CDN_FIRMA_DEMO"
}
}
]
```
### PUT http://YOUR_IP/api/Payments/{id}

#### This method is used to add payments in Comarch ERP Optima. The payment is later used to settle

#### the documents, so write down the value of the paymentFullNumber field.

**Body Response :**


#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### Body raw (json)

**Company Data Fields :**

**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Definition Length Required
```
```
Int32 id Unique
settlement ID in
Optima. We get
this field after
adding
```
```
None No
```
```
String foreignNumber Foreign payment
number from the
external system
```
```
0-256 No
```
```
String paymentFullNum
ber
```
```
Unique payment
number in
Optima. We get
this field after
adding (only get)
```
##### 1-30 -

```
String currency Currency of
payment
```
```
3 Yes
```
```
(DateTime)String date Date of payment
e.g. "2021-03-
03"
```
```
yyyy-MM-dd Yes
```
```
Decimal grossValue Value of payment None Yes
```
```
Type Name Definition Length Required
```
```
String companyName Chosen Company
Name for
Payments
```
```
None No
```
```
String databaseName Chosen
Database Name
for Payments
```
```
None No
```

#### AUTHORIZATION OAuth 2.0

This folder is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

```
http://{{API}}/api/Prices
```
```
json
```
##### {

```
"paymentMethodId": 1 ,
"foreignNumber":"TEST/2021/1",
"date":"2021-03-03",
"grossValue": 250 ,
"type": 1 ,
"description": "desc",
"currency":"PLN",
"customerId": 3 ,
"oppositeAccount" : "182"
}
```
## Prices

### GET http://YOUR_IP/api/Prices

#### The Function enable to download history of date base for Prices

**Body Response :**

**Http Correct Answer :**

```
Type Name Definition
```
```
String optimaItemId Optima Item Id
```
```
String itemBarcode Iteam Barcode
```
```
Int32 number Number
```
```
Int32 type Type
```
```
String name Description
```
```
Decimal value Value
```
```
String currency Currency
```
```
String vatRate Vat Rate
```
```
String unit Unit
```
```
String itemCode Item Code
```
```
String error Error
```

#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### PARAMS

**priceNumbers** Int32

**itemId** Int32

**itemBarcode** String

**itemCode** String

**priceType** Int32

**key** String

**offset** Int32

**limit** Int32

**databaseName** String

```
http://{{API}}/api/Prices
```
```
ttp Co ect s e :
```
**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

```
Can be used multiple times
```
```
ID of item in Comarch ERP Optima
```
```
Barcode of item in Comarch ERP Optima
```
```
Code of item in Comarch ERP Optima
```
```
Price type (1 - net, 2 - gross)
```
```
Possible values: id, code, barcode
```
```
Initial record from which data will be retrieved (default: 0)
```
```
Limit of one-time downloaded records (default: 2000)
```
```
Database name (Default in Web.Config)
```
### PUT http://YOUR_IP/api/Prices

#### The Function enable to modyficate for prices

**Body :**


#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

**Company Data Filds :**

**Body List Price Params Reqest :**

**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name
```
```
Dictionary> PricesDictionary
```
```
Object companyData
```
```
Type Name Definition Length Required
```
```
String companyName Chosen Company
Name for Price
```
```
None No
```
```
String databaseName Chosen
Database Name
for Price
```
```
None No
```
```
Type Name Definition
```
```
String optimaItemId Optima Item Id
```
```
String itemBarcode Iteam Barcode
```
```
Int32 number Number
```
```
Int32 type Type
```
```
String name Description
```
```
Decimal value Value
```
```
String currency Currency
```
```
String vatRate Vat Rate
```
```
String unit Unit
```
```
String itemCode Item Code
```
```
String error Error
```

#### AUTHORIZATION OAuth 2.0

This folder is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

```
http://{{API}}/api/AutomaticSelectQuery
```
#### AUTHORIZATION OAuth 2.0

## Query

### POST AutomaticQuery

#### The function allows you to perform any select query (ONLY FOR ELTE-S)

**Body Request :**

**Company Data Fields :**

**Http Correct Answer :**

**201 Created**

**Http Possible Answer :**

**401 Unauthorized**

**404 BadRequest**

**Attention !!**

The function can only be used by employees who have access to the key

```
Type Name Definition Length Required
```
```
String Query Place to place a
select query
```
```
None No
```
```
String Key Private access
key
```
```
None Yes
```
```
Object companyData Company Data
obejct for
Warehouses
```
```
None No
```
```
Type Name Definition Length Required
```
```
String companyName Chosen Company
Name for
Warehouse
```
```
None No
```
```
String databaseName Chosen
Database Name
for Warehouse
```
```
None No
```

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### Body raw (json)

#### AUTHORIZATION OAuth 2.0

This folder is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

```
http://{{API}}/api/PricesForCustomer
```
```
json
```
```
{
"Query" : "",
"Key" : ""
}
```
## RequestsForCustomerAccount

### GET http://YOUR_IP/api/PricesForCustomer

#### The Function enable to get stock and prices of item

**Body Response :**

**Http Correct Answer :**

**200 Ok**

```
Type Name Definition
```
```
String optimaItemId Optima Item Id
```
```
String itemCode Optima Item Code
```
```
String itemName Optima Item Name
```
```
String itemBarcode Iteam Barcode
```
```
Int32 type Type
```
```
Decimal value Value
```
```
String currency Currency
```
```
String vatRate Vat Rate
```
```
String unit Unit
```
```
Decimal stock Stock on warehouse
```

#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### PARAMS

**itemId** String

**itemBarcode** String

**itemCode** String

**offset** Int32

**limit** Int32

**vatNumber** Int32

```
http://{{API}}/api/DocumentsForCustome
```
**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

```
Item Id
```
```
Item Barcode
```
```
Item Code
```
```
Initial record from which data will be retrieved (default: 0)
```
```
Limit of one-time downloaded records (default: 2000)
```
```
Vat Number
```
### POST http://YOUR_IP/api/DocumentsForCustomer

#### The Function enable to Create Document

**Body Params Request :**

**Elements For Body :**

```
Type Name Definition Length Required
```
```
String paymentMethod type of payment 20 Yes
```
```
String currency currency 30 Yes
```
```
List elements List of item None Yes
```
```
String description description of
document
```
```
1024 No
```

#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### Body raw (json)

#### AUTHORIZATION OAuth 2.0

**Company Data Fields :**

**Price For Body :**

**Http Correct Answer :**

**201 Created**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Definition Length Required
```
```
String code Code of item.
Field used to
associate with an
item in Optima
```
```
50 Yes
```
```
Decimal quantity Quantity of
ordered goods
```
```
none Yes
```
```
json
```
```
{
"paymentMethod": "przelew",
"currency": "PLN",
"elements": [
{
"code": "ProductCode",
"quantity": 1.0000
},
{
"code": "ProductCode2",
"quantity": 1.0000
}
],
"description": "description"
}
```
## Resources


This folder is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

```
http://{{API}}/api/Resources
```
#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### PARAMS

**ItemId** Int32

**WarehouseId** Int32

**AveragePrice** Bool

**databaseName** String

### GET http://YOUR_IP/api/Resources

#### The Function enable to download history of date base for Resources

**Body Response :**

**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Definition
```
```
Decimal purschasePrice Resources Price
```
```
Decimal quantity Resources Quantity
```
```
Decimal totalValue Total Value
```
```
Decimal averagePrice Avarage Price
```
```
(DateTime)String deliveryDate Delivery Date (yyyy-mm-dd)
```
```
Specific item ID
```
```
Warehouse ID
```
```
When calculating the average delivery price true
```
```
Database name (Default in Web.Config)
```
## Representatives


#### AUTHORIZATION OAuth 2.0

This folder is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

```
http://{{API}}/api/Representatives
```
#### AUTHORIZATION Bearer Token

**Token** <token>

#### PARAMS

**customerId** Int32

## Representatives

### GET http://YOUR_IP/api/Representatives

#### The Function enable to download representative

```
Get list of representatives from ERP Optima
Get single representative to specific customer
```
Body response :

**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Description
```
```
int id Representative ID
```
```
int customerId Customer ID
```
```
string title Title for representative
```
```
string name Representative name
```
```
string phone Representative phone
```
```
string phone2 Representative additional phone
```
```
string email Representative email
```
```
string owner Representative owner
```
```
DateTime created Created representative
```

#### AUTHORIZATION OAuth 2.0

This folder is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

```
http://{{API}}/api/Stocks
```
#### AUTHORIZATION Bearer Token

**Token** <token>

#### PARAMS

**itemId** Int32

```
Customer id in Comarch ERP Optima
```
## Stocks

### GET http://YOUR_IP/api/Stocks

#### The Function enable to download history of date base for Stocks

**Body Response :**

**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Definition
```
```
Int32 warehouseId Ware House Id
```
```
String optimaItemId Optima Item Id
```
```
String itemBarcode Item Bar Code
```
```
Decimal quantity Quantity
```
```
Decimal reservation Reservation
```
```
String unit Unit
```
```
String itemCode Item Code
```
```
Decimal possibleQuantity Possible Quantity
```
```
ID of item in Comarch ERP Optima
```

**warehouseId** Int32

**offset** Int32

**limit** Int32

**itemBarcode** String

**itemCode** String

**possibleQuantity** Int32

**key** String

**databaseName** String

**date_to** String(yyyy-MM-dd)

#### AUTHORIZATION OAuth 2.0

This folder is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

```
http://{{API}}/api/Transfers
```
```
ID of item in Comarch ERP Optima
```
```
ID of warehouse in Coamrch ERP Optima (Default in Configuration)
```
```
Initial record from which data will be retrieved (default: 0)
```
```
Limit of one-time downloaded records (default: 2000)
```
```
Barcode of item in Comarch ERP Optima
```
```
Code of item in Comarch ERP Optima
```
```
Stocks possible quantity
```
```
Possible values: id,code,barcode
```
```
Database name (Default in Web.Config)
```
```
Retrieves the states for the nearest date from the specified
```
## Transfers

### GET http://YOUR_IP/api/Transfers

#### The Function enable to download history of date base for Transfers

**Body Response :**


#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### PARAMS

**id** Int32

**Company Data Fields :**

**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Definition
```
```
Int32 id Transfer Id
```
```
Int32 bankStatement Bank Statement
```
```
Int32 ordinalNumber Ordinal Number
```
```
Int32 type Type
```
```
Int32 documentTypeId Document Type Id
```
```
String documentTypeSignature Document Type Signature
```
```
String documentTypeName Document Type Name
```
```
String foreignNumber Foregin Number
```
```
String fullNumber Full Number
```
```
String transferNumber Transfer Number
```
```
(DateTime)String issueDate Issue Date
```
```
Int32 entityTyp Entity Type
```
```
Int32 entityId Entity Id
```
```
String entityCode Entity Code
```
```
Type Name Definition Length Required
```
```
String companyName Chosen Company
Name for
Transfers
```
```
None No
```
```
String databaseName Chosen
Database Name
for Transfers
```
```
None No
```

**id** Int32

**bankStatementID** Int32, Int32...

**type** Int32

**documentTypeId** Int32

**foreignNumber** String

**fullNumber** String

**transferNumber** String

**issueDateStart** String (yyyy-mm-dd)

**issueDateEnd** String (yyyy-mm-dd)

**entityType** Int32

**entityId** Int32

**settled1** Int32

**settled2** Int32

**optima_id** Int32, Int32...

**bank_statement_id** Int32, Int32...

**foreign_number** String, String...

**full_number** String, String...

```
Unique Transfer id in the Comarch ERP Optima
```
```
Unique bank statement id in the Comarch ERP Optima
```
```
Transfer type
```
```
Id transfer type
```
```
Foreign number of transfer
```
```
Full number of transfer
```
```
Transfer Number
```
```
Start of date range of issue
```
```
End of date range of issue
```
```
entity type
```
```
entity Id
```
```
Settlement status (0, 1, 2) - together with settled_2 report the settlement
status in the format [0,0], [1,1], [1,2] or [2,2]
```
```
Settlement status (0, 1, 2) - together with settled_1 report the settlement
status in the format [0,0], [1,1], [1,2] or [2,2]
```
```
List of values separated by comma
```
```
List of values separated by comma
```
```
List of values separated by comma
```
```
List of values separated by comma
```

**transfer_number** String, String...

**entity_id** Int32, Int32...

**entityCode** String

**booked** String

**databaseName** String

```
http://{{API}}/api/Transfers
```
```
List of values separated by comma
```
```
List of values separated by comma
```
```
List of values separated by comma
```
```
Entity Code
```
```
Posted raport has an value, unposted has a null value
```
```
Database name (Default in Web.Config)
```
### PUT http://YOUR_IP/api/Transfers

#### The Function enable to modifiacte transfers

**Body Params Request :**

**Company Data Fields :**

```
Type Name Definition
```
```
Int32 id Transfer Id
```
```
Int32 bankStatement Bank Statement
```
```
Int32 ordinalNumber Ordinal Number
```
```
Int32 type Type
```
```
Int32 documentTypeId Document Type Id
```
```
String documentTypeSignature Document Type Signature
```
```
String documentTypeName Document Type Name
```
```
String foreignNumber Foregin Number
```
```
String fullNumber Full Number
```
```
String transferNumber Transfer Number
```
```
(DateTime)String issueDate Issue Date
```
```
Int32 entityTyp Entity Type
```
```
Int32 entityId Entity Id
```
```
String entityCode Entity Code
```

#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### Body raw (json)

#### AUTHORIZATION OAuth 2.0

This folder is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

```
http://{{API}}/api/UnitsOfMeasures
```
**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Definition Length Required
```
```
String companyName Chosen Company
Name for
Transfers
```
```
None No
```
```
String databaseName Chosen
Database Name
for Transfers
```
```
None No
```
```
json
```
```
{
"recordNumber" : "KP/11/2022/PKO",
"description" : "description",
"rateDate" : "2023-02-28",
"oppositeAccount" : "11-3445-321",
"entityId" : 926
}
```
## UnitsOfMeasures

### GET http://YOUR_IP/api/UnitsOfMeasures

#### The Function enable to download history of date base for UnitsOfMeasures


#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### PARAMS

**ItemId** Int32

**ItemCode** String

**databaseName** String

#### AUTHORIZATION OAuth 2.0

This folder is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

**Body Response :**

**Units of Measure Unit for Body :**

**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Definition
```
```
Int32 id Unit of Measure Id
```
```
String code Code
```
```
List unitsOfMeasures List Of Units of Measure
```
```
Type Name Definition
```
```
String name Name
```
```
String numerator Numerator
```
```
String denominator Denominator
```
```
String barcode Barcode
```
```
Database name (Default in Web.Config)
```
## Vat

### POST http://YOUR_IP/api/VatRegister


```
http://{{API}}/api/VatRegister
```
#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### AUTHORIZATION OAuth 2.0

This folder is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### The Function enable to Create Vat Register

**Body Params Request :**

**Company Data Fields :**

**Http Correct Answer :**

**201 Created**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Definition Length Required
```
```
Int32 Id Vat Id None No
```
```
Int32 documentId Document Id None Yes
```
```
Int32 settleVat Allow to disable
settle vat in
register ( 0 - no
settle, 1 -
(default) settle
```
```
None No [Allow inputs
0,1]
```
```
Int32 name Name None Yes
```
```
Object companyData Company Data
obejct for Vat
Register
```
```
None No
```
```
Type Name Definition Length Required
```
```
String companyName Chosen Company
Name for Vat
Register
```
```
None No
```
```
String databaseName Chosen
Database Name
for Vat Register
```
```
None No
```
## Warehouses


```
http://{{API}}/api/Warehouses?opis=String
```
#### AUTHORIZATION OAuth 2.0

### GET http://YOUR_IP/api/Warehouses

#### The Function enable to download history of date base for Warehouses

```
Get list of warehouses
Get single warehouse by id
Get single warehouse by code
```
**Body Response:**

**Company Data Fields :**

**Http Correct Answer :**

**200 Ok**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Definition
```
```
Int32 id Ware House Id
```
```
String symbol Symbol
```
```
String name Name
```
```
String symbolInNumbering Symol in Numbering
```
```
String description Description
```
```
Int32 type Type
```
```
Int32 inactive Inactive
```
```
Int32 defaultPrice Default Price
```
```
String accountingAccount Accounting Account
```
```
Object companyData Company Data obejct for
Warehouse
```
```
Type Name Definition Length Required
```
```
String companyName Chosen Company
Name for
Warehouse
```
```
None No
```
```
String databaseName Chosen
Database Name
for Warehouse
```
```
None No
```

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### PARAMS

**id** Int32

**symbol** String

**databaseName** String

**opis** String

```
http://{{API}}/api/Warehouses
```
```
Unique warehouse id in the Comarch ERP Optima
```
```
Unique warehouse symbol in the Comarch ERP Optima
```
```
Database name (Default in Web.Config)
```
```
Warehouse description
```
### POST http://YOUR_IP/api/Warehouses

#### The Function enable to Create Warehouses

**Body Params Request :**

```
Type Name Definition Length Required
```
```
Int32 id Unique
warehouse ID in
Optima
```
```
None No
```
```
String symbol Unique
warehouse
symbol in Optima
```
```
1 - 20 Yes
```
```
String name Name 1 - 50 Yes
```
```
String symbolInNumberi
ng
```
```
Symbol in
Number
```
```
1 - 5 Yes
```
```
String description Description 0 - 254 No
```
```
Int32 type Type of
warehouse.
1 (default) - local,
2 - remote,
3 - service
```
```
None Yes
```
```
Int32 inactive 0 (default) -
active,
1 - inactive
```
```
None Yes
```
```
I t32 d f ltP i N b f i N Y
```

#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### PARAMS

**id**

**bankStatementID**

#### Body raw (json)

**Company Data Fields :**

**Http Correct Answer :**

**201 Created**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Definition Length Required
```
```
String companyName Chosen Company
Name for
Warehouse
```
```
None No
```
```
String databaseName Chosen
Database Name
for Warehouse
```
```
None No
```
```
json
```
```
{
"symbol": "POZNAN",
"name": "Magazyn Poznań",
"symbolInNumbering": "PZN",
"description": "Magazyn w Poznaniu",
"type": 1 ,
"inactive": 0 ,
"defaultPrice": 2 ,
"accountingAccount": "317"
}
```
### PUT http://YOUR_IP/api/Warehouses/{id}


```
http://{{API}}/api/Warehouses/{id}
```
#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

**The Function enable to modificate WareHouses**

**Body Params Request :**

**Company Data Fields :**

**Http Correct Answer :**

**201 Created**

**Http Possible Answer :**

**401 Unauthorized**

```
Type Name Definition Length Required
```
```
Int32 id Unique
warehouse ID in
Optima
```
```
None No
```
```
String symbol Unique
warehouse
symbol in Optima
```
```
1 - 20 Yes
```
```
String name Name 1 - 50 Yes
```
```
String symbolInNumberi
ng
```
```
Symbol in
Number
```
```
1 - 5 Yes
```
```
String description Description 0 - 254 No
```
```
Int32 type Type of
warehouse.
1 (default) - local,
2 - remote,
3 - service
```
```
None Yes
```
```
Int32 inactive 0 (default) -
active,
1 - inactive
```
```
None Yes
```
```
I t32 d f ltP i N b f i N Y
```
```
Type Name Definition Length Required
```
```
String companyName Chosen Company
Name for
Warehouse
```
```
None No
```
```
String databaseName Chosen
Database Name
for Warehouse
```
```
None No
```

#### Body raw (json)

```
http://{{API}}/api/Warehouses/{id}
```
#### AUTHORIZATION OAuth 2.0

This request is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

#### PARAMS

**databaseName** string

#### AUTHORIZATION OAuth 2.0

This folder is using OAuth 2.0 from collectionAPI Comarch ERP Optima v. Professional / Multi-Company 2.0

```
http://{{API}}/api/LoginOptima
```
#### AUTHORIZATION Bearer Token

**Token** <token>

#### PARAMS

```
json
```
```
{
"symbol": "POZ",
"name": "Magazyn Poznań Edited",
"symbolInNumbering": "PZ",
"description": "Magazyn w Poznaniu Edited",
"type": 1 ,
"inactive": 0 ,
"defaultPrice": 3 ,
"accountingAccount": "316"
}
```
### DELETE http://YOUR_IP/api/Warehouses/{id}

```
Database name (Default in Web.Config)
```
## General

### POST http://YOUR_IP/api/LoginOptima


#### PARAMS

**comapnyName** string

```
http://{{API}}/api/LogoutOptima
```
#### AUTHORIZATION Bearer Token

**Token** <token>

```
Optima comapny name (Only Multicompany version)
```
### POST http://{{API}}/api/LogoutOptima


